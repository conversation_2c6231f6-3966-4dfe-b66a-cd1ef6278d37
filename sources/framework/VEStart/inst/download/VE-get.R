# This "script" is a one-liner that can be modified and placed on the website to
# the user's R in order to start using VisionEval. See VE4-install.R for
# what happens after that.

# To test the installation function locally, use ve.test.install() from within VE-Bootstrap.R
# (or after loading the VEBuild package).

# The website has the following code, and the script lives in visioneval.github.io/assets/install
# Keep the assets/install version of VE4-install.R up to date with this file in VEStart/inst/download
source("https://visioneval.github.io/assets/install/VE4-install.R")
