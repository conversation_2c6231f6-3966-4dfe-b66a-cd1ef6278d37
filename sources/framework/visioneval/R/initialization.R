#================
#initialization.R
#================

#This script defines functions used to set up and manage a model
#run. These are helpers for finding the ModelState and running modules

#INITIALIZE MODEL STATE
#======================
#' Initialize model state.
#'
#' \code{initModelState} a visioneval framework control function that builds creates
#' a new ModelState_ls structure in the model environment, optionally saving it to
#' ModelState.rda.
#'
#' Parameters are can be supplied from run_parameters.json or from the function
#' invocation (the latter mostly for backwards compatibility). The new structure of
#' runtime parameters moves environmental parameters to configuration files outside the
#' model (e.g. DatastoreType) and reserves model specific elements for
#' run_parameters.json (Model, BaseYear, Years). See \code{loadConfiguration}.
#'
#' @param Save A logical (default=TRUE) indicating whether the model state file
#'   should be written out.
#' @param Param_ls A named list of model run parameters
#' @param RunPath (default getwd()) is the directory in which ModelState, Datastore and log
#'   are being stored.
#' @param envir The environment in which to create the ModelState_ls
#' @return The updated RunParam_ls with ModelState parameters fleshed out
#' @import sf
#' @export
initModelState <- function(Save=TRUE,Param_ls=NULL,RunPath=NULL,envir=modelEnvironment()) {

  # Load model environment (should have RunParam_ls already loaded, furnishing ...)
  model.env <- envir
  if ( ! is.list(Param_ls) ) {
    Param_ls <- model.env$RunParam_ls;
  }

  # The required parameters will be the initial elements for the ModelState
  # Other RunParam_ls elements will be placed in ModelState_ls$RunParam_ls

  # Make sure structural defaults are present in Param_ls
  # These will mostly be set in either the ve.runtime configuration or the model
  # configuration/run_parameters.json
  DefaultValues_ <- defaultVERunParameters(Param_ls)[c( "DatastoreName", "DatastoreType", "Seed" )]
  DefaultValues_ <- addParameterSource(DefaultValues_,"Defaults")
  Param_ls <- mergeParameters(DefaultValues_,Param_ls)

  # Don't need these until running model; Save will always be TRUE if running model
  # In memory model state will not contain these elements, though they may be added after the fact
  RequiredParam_ <- c( "Model", "Scenario", "Description", "Region", "BaseYear", "Years", "ParamPath" )
  ParamExists_ <- RequiredParam_ %in% names(Param_ls)
  if (any(!ParamExists_)) {
    MissingParam_ <- RequiredParam_[!ParamExists_]
    Message <- c(
      "Missing model run parameters (not set in VisionEval configuration, run_parameters.json or function call):",
      paste(MissingParam_, collapse = ", ")
    )
    stop( writeLog(Message,Level="error") )
  }

  # Install the parameters that do exist - the required parameters become the foundation for
  # ModelState_ls. Other parameters are placed in newModelState_ls$RunParameters,
  # (including things like ParamDir, UnitsFile, etc.)
  # ModelState version of Param_ls now also includes the required parameters
  newModelState_ls <- Param_ls[c(names(DefaultValues_),RequiredParam_[ParamExists_])]
  newModelState_ls$LastChanged <- Sys.time()
  
  # Also load the complete deflators and units files, which will be accessed later via ModelState_ls
  ParamPath <- getRunParameter("ParamPath",Param_ls=Param_ls)
  DeflatorsFile <- getRunParameter("DeflatorsFile",Param_ls=Param_ls)
  DeflatorsFilePath <- file.path(ParamPath,DeflatorsFile)
  DeflatorsFilePath <- DeflatorsFilePath[file.exists(DeflatorsFilePath)][1] # Allow ParamPath to be a vector of Paths
  if ( is.na(DeflatorsFilePath) || length(DeflatorsFilePath)!=1 ) {
    stop(
      writeLog(
        paste("Deflators File",DeflatorsFile,"does not exist in",ParamPath),
        Level="error"
      )
    )
  }
  newModelState_ls$Deflators <- read.csv(DeflatorsFilePath, as.is = TRUE)

  UnitsFile <- getRunParameter("UnitsFile",Param_ls=Param_ls)
  UnitsFilePath <- file.path(ParamPath,UnitsFile)
  UnitsFilePath <- UnitsFilePath[file.exists(UnitsFilePath)][1] # Allow ParamPath to be a vector of Paths
  if ( is.na(UnitsFilePath) || length(UnitsFilePath)!=1 ) {
    stop(
      writeLog(
        paste("Units File",UnitsFile,"does not exist in",ParamPath),
        Level="error"
      )
    )
  }
  newModelState_ls$Units <- read.csv(UnitsFilePath, as.is = TRUE)

  # Note: reproduces visioneval::readGeography, which is itself only called in tests.R
  GeoFile <- getRunParameter("GeoFile",Param_ls=Param_ls)
  # If length 2, then it is geodatabase + table and we ignore ParamPath role
  # Geo_df <- read.csv(GeoFilePath, colClasses="character")
  Geo_df <- if ( length(GeoFile) > 1 ) {
    GeoFilePath <- GeoFile[1]
    GeoFileTable <- GeoFile[2]
    sf::st_read(GeoFilePath,GeoFileTable,quiet=TRUE)
  } else {
    GeoFileTable <- character(0)
    GeoFilePath <- file.path(ParamPath,GeoFile)
    GeoFilePath <- GeoFilePath[file.exists(GeoFilePath)][1] # Allow ParamPath to be a vector of Paths
    if ( is.na(GeoFilePath) || length(GeoFilePath)!=1 ) {
      stop(
        writeLog(
          paste("Geography File",GeoFile,"does not exist in",ParamPath),
          Level="error"
        )
      )
    }
    sf::st_read(GeoFilePath,quiet=TRUE)
  }
  # NOTE: following transformations are needed because st_read does not recognize text "NA" as NA
  if ( "Bzone" %in% names(Geo_df) ) {
    Geo_df <- within(Geo_df, Bzone[Bzone=="NA"] <- as.character(NA))
  }
  if ( "Czone" %in% names(Geo_df) ) {
    Geo_df <- within(Geo_df, Czone[Czone=="NA"] <- as.character(NA))
  }

  # Add GeoFile source as attribute
  attr(Geo_df,"source") <- paste(c(GeoFilePath,GeoFileTable),collapse=" : ")

  CheckResults_ls <- checkGeography(Geo_df) # TODO: could move the geodatabase field renaming to checkGeography
  Messages_ <- CheckResults_ls$Messages
  if (length(Messages_) > 0) {
    writeLog(Messages_,Level="error")
    stop(paste0("Errors found in ", GeoFilePath, ". See log for details."))
  } else {
    writeLog("Geographical indices successfully read.",Level="info")
  }
  newModelState_ls[ names(CheckResults_ls$Update) ] <- CheckResults_ls$Update;

  # Finalize the ModelState in model.env using initLog for the "kickoff"
  if ( "LogStatus" %in% names(model.env) ) {
    newModelState_ls$LogFile <- model.env$LogStatus$LogFile
    newModelState_ls$FirstCreated <- model.env$LogStatus$ModelStart
  } else {
    newModelState_ls$FirstCreated <- Sys.time() # Timestamp
  }
  newModelState_ls$RunParam_ls <- Param_ls;

  # Establish RunPath and DatastorePath in parameters and ModelState_ls
  if ( is.null(RunPath) ) RunPath <- getwd()   # Where the model results are going
  newModelState_ls$ModelStatePath <- RunPath;  # Will create ModelStateFile (parameter) in that path
  if ( ! "DatastorePath" %in% names(Param_ls) ) { # DatastorePath used for virtual linkage
    Param_ls$DatastorePath <- RunPath
  }
  newModelState_ls$DatastorePath <- Param_ls$DatastorePath

  # Mark ModelState_ls as "Initialized" (cooperation with VEModel)
  newModelState_ls$RunStatus <- "Initialized"

  # Set up model run environment
  model.env$ModelState_ls <- newModelState_ls # Replace whatever is already there
  model.env$RunParam_ls <- Param_ls # Includes all the run Parameters, including "required"

  # Note that the ModelState is saved in the working directory
  # Model is expected to run in the directory that will receive its output.
  #   ModelState.Rda is the model description for this run
  #   Datastore are the model results for this run
  if ( Save) save("ModelState_ls", envir=model.env, file = file.path(RunPath,getModelStateFileName(model.env$RunParam_ls)))

  return(Param_ls) 
}
#initModelState(ParamDir = "tests/defs")

#ARCHIVE RESULTS
#===============
#' Move an existing set of results into a different time-stamped directory
#'
#' \code{archiveResults} a visioneval framework control function that moves existing results
#' to a new directory.
#'
#' If RunDir is the same as RunParam_ls$ModelDir, then pick out the ModelState.Rda, Datastore and
#' Log Files to move into a newly created archive directory. If RunDir is different (probably the
#' absolute path to some Results directory), we can just rename the directory.
#'
#' @param RunParam_ls Run parameters describing which Results to archive and where to put them
#' @param ResultsDir Directory containing results to archive (could be ModelDir/ResultsDir or just
#' ModelDir)
#' @param SaveDatastore If provided, replaces SaveDatastore in RunParam_ls
#' @return a vector of artifacts that were found and copy attempted but failed (or character(0)
#' if everything succeeded)
#' @export
archiveResults <- function(RunParam_ls,ResultsDir=getwd(),SaveDatastore=NULL) {

  if ( missing(SaveDatastore) || is.null(SaveDatastore) ) {
    SaveDatastore <- getRunParameter("SaveDatastore",Param_ls=RunParam_ls)
  } # if FALSE, do not archive

  # Do nothing if SaveDatastore is FALSE
  if ( ! SaveDatastore ) return(invisible(character(0)))

  writeLog("Saving previous model results...",Level="warn")

  # Copy the Datastore plus the ModelState plus any log file from RunDirectory
  # to new location based on run timestamp in ModelState
  ModelDir <- getRunParameter("ModelDir",Param_ls=RunParam_ls)
  ResultsName <- getRunParameter("ArchiveResultsName",Param_ls=RunParam_ls)

  ModelStateName <- getRunParameter("ModelStateFile",Param_ls=RunParam_ls)

  # Get the timestamp from the newest ModelState, if available
  if ( missing(ResultsDir) || ResultsDir == ModelDir ) { # classic model
    ModelStateFile <- file.path(ResultsDir,ModelStateName)
    if ( ! file.exists(ModelStateFile) ) {
      writeLog("No previous model state or information to save.",Level="warn")
      return(invisible(character(0)))
    }
  } else { # Model with separate results directory ("ResultsDir")
    ModelStateFile <- file.path(ResultsDir,dir( ResultsDir, pattern=ModelStateName, recursive=TRUE))
    if ( length(ModelStateFile) == 0 ) {
      writeLog("No previous model state or information to save.",Level="warn")
      return(invisible(character(0)))
    }
    # Get the newest ModelState
    ModelStateFile <- ModelStateFile[ order(file.info(ModelStateFile)[,"mtime"]) ][length(ModelStateFile)]
    if ( ! file.exists(ModelStateFile) ) {
      writeLog("No previous model state or information to save.",Level="warn")
      return(invisible(character(0)))
    }
  }    

  # Use the "newest" ModelState for the timestamp
  ModelState_ls <- readModelState(FileName=ModelStateFile,envir=new.env())

  if ( "LastChanged" %in% names(ModelState_ls) ) {
    TimeStamp <- ModelState_ls$FirstCreated
    writeLog(paste("Archive TimeStamp FirstCreated:",TimeStamp),Level="trace")
  } else if ( "FirstCreated" %in% names(ModelState_ls) ) {
    TimeStamp <- ModelState_ls$LastChanged
    writeLog(paste("Archive TimeStamp LastChanged:",TimeStamp),Level="trace")
  } else {
    TimeStamp <- Sys.time()
    writeLog(paste("Archive TimeStamp Sys.time():",TimeStamp),Level="trace")
  }

  # Archive directory is relative to the model directory
  ArchiveDirectory <- normalizePath(
    file.path(
      ModelDir,
      fileTimestamp(TimeStamp,Prefix=ResultsName)
    ), winslash="/",mustWork=FALSE
  )

  if ( ResultsDir != ModelDir ) { # Requires them to be equivalently normalized paths

    # Do nothing if there is no directory to archive
    if ( ! dir.exists(ResultsDir) ) return(character(0))

    # ResultsDir is a sub-directory of ModelDir
    owd <- setwd(ModelDir)
    on.exit(setwd(owd))

    file.rename(ResultsDir,ArchiveDirectory)
    return(character(0))
  }

  # Remainder is for a single-stage classic model storing its results in the ModelDir

  if ( dir.exists(ArchiveDirectory) ) {
    writeLog(c("Results have already been saved:",ArchiveDirectory),Level="warn")
    # Despite the warning, we'll still copy everything over!
  } else {
    dir.create(ArchiveDirectory)
  }
  writeLog(paste("Archive Directory:",ArchiveDirectory),Level="trace")

  # Change out to the "ResultsDir"
  owd <- setwd(ResultsDir)
  on.exit(setwd(owd))

  # Now working in existing ResultsDir

  DatastoreName <- ModelState_ls$DatastoreName;

  # Will clobber OutputDir (need to check if this is an absolute path...)
  OutputDir <- getRunParameter("OutputDir",Param_ls=ModelState_ls$RunParam_ls)

  # remove vector if TRUE if attempting to remove
  # set to FALSE if removal is not attempted
  # set to NA if something was found but not removed
  remove <- c(
    Datastore=is.character(DatastoreName),
    Outputs=is.character(OutputDir),
    ModelState=is.character(ModelStateName),
    Logs=TRUE
  )

  # Process Datastore
  if ( remove["Datastore"] && dir.exists(DatastoreName) ) {
    success <- file.copy(DatastoreName,ArchiveDirectory,recursive=TRUE,copy.date=TRUE)
    msg <- paste("Datastore: ",file.path(getwd(),DatastoreName))
    if ( ! all(success) ) {
      remove["Datastore"] <- NA
      writeLog(paste0("Failed to archive ",msg),Level="error")
    } else {
      writeLog(paste0("Archived ",msg),Level="trace")
    }
  }

  # Process Output directory (extracts, query results from this run)
  if ( remove["Outputs"] && dir.exists(OutputDir) ) {
    success <- file.copy(OutputDir,ArchiveDirectory,recursive=TRUE,copy.date=TRUE)
    msg <- paste("Outputs:",file.path(getwd(),OutputDir))
    if ( ! all(success) ) {
      remove["Outputs"] <- NA
      writeLog(paste0("Failed to archive ",msg),Level="error")
    } else {
      writeLog(paste0("Archived ",msg),Level="trace")
    }
  } else {
    remove <- remove[ c("Datastore","ModelState","Logs") ] # don't bother with OutputDir
  }

  # Process ModelState
  if ( remove["ModelState"] && file.exists(ModelStateName) ) {
    success <- file.copy(ModelStateName,ArchiveDirectory,copy.date=TRUE)
    msg <- paste("Model State:",file.path(getwd(),ModelStateName))
    if ( ! all(success) ) {
      remove["ModelState"] <- NA
      writeLog(paste0("Failed to archive ",msg),Level="error")
    } else {
      writeLog(paste0("Archived ",msg),Level="trace")
    }
  }

  # Process Log file
  if ( "LogFile" %in% ModelState_ls ) {
    # If the LogFile is listed in ModelState_ls, just copy that
    LogPath <- ModelState_ls$LogFile
  } else {
    # Sweep away all the existing log files
    # Always process all the relevant log files (may be more than one)
    # Will copy based on what is in the file system, not the ModelState_ls
    LogPath <- dir(pattern="Log_.*\\.txt",full.names=TRUE) # see initLog for template
  }
  if ( length(LogPath) == 0 ) { # No Log present; a recoverable error
    remove["Logs"] <- FALSE
    writeLog(paste0("No Log file for run at ",TimeStamp," (not stopping)"),Level="info")
  } else {
    success <- file.copy(LogPath,ArchiveDirectory,copy.date=TRUE)
    msg <- paste("Log File(s):\n",paste(LogPath,collapse="\n"))
    if ( ! all(success) ) {
      remove["Logs"] <- NA
      writeLog(paste0("Failed to archive ",msg),Level="error")
    } else {
      writeLog(paste0("Archived ",msg),Level="trace")
    }
  }

  # Remove the previous Results if they were copied successfully
  for ( file in names(remove[which(remove)]) ) {
    if ( remove[file] ) {
      if ( file=="Datastore" ) unlink(DatastoreName,recursive=TRUE)
      if ( file=="Outputs"   ) unlink(OutputDir,recursive=TRUE)
      if ( file=="ModelState") unlink(ModelStateFile)
      if ( file=="Logs"      ) unlink(LogPath)
    } else {
      writeLog(paste("Saving prior results failed; Not removing",file),Level="error")
    }
  }
  writeLog(paste("Archived",paste(names(remove)[which(remove)],collapse=",")),Level="info")

  return(invisible(names(remove)[which(is.na(remove))]))
}

#LOAD MODEL STATE
#================
#' Load ModelState into ve.model environment
#'
#' \code{loadModelState} a visioneval framework control function that
#' loads the ModelState for a model run into the ve.model environment.
#' Will replace any ModelState that is already there, so be cautious to
#' get the environment right, and to make sure that any existing ModelState
#' in memory is not unsaved (shouldn't be a problem inside a model run).
#'
#' @param FileName A string identifying the name of the file that contains
#' the ModelState_ls list. The default is the ModelStateFile in getwd().
#' @param envir An environment into which to load ModelState.Rda (default ve.model)
#' @return The RunParam_ls from the saved model state (or an empty list if not found)
#' @export
loadModelState <- function(FileName=NULL,envir=NULL) {
  if ( is.null(envir) ) envir = modelEnvironment()
  explicitFileName <- if ( is.character(FileName) && file.exists(FileName[1]) ) TRUE else {
    FileName <- getModelStateFileName()
    FALSE
  }
  # Get here with a file name
  if (file.exists(FileName)) {
    loaded <- try (silent=TRUE,
      load(FileName,envir=envir)
    )
    if ( inherits(loaded,"try-error") ) {
      # Something wrong with ModelState file - just ignore the attempt
      writeLog("Pre-existing ModelState.Rda is empty or invalid:",Level="warn")
      writeLog(as.character(loaded),Level="warn")
    }
  }
  ModelState_ls <- get0( "ModelState_ls", envir=envir, ifnotfound=list() )
  if ( length(ModelState_ls) > 0 ) {
    writeLog("Saved RunParam_ls from ModelState_ls",Level="debug") 
    Param_ls <- ModelState_ls$RunParam_ls
  } else {
    writeLog("RunParam_ls from environmentment",Level="debug")
    Param_ls <- get0( "RunParam_ls", envir=envir, ifnotfound=list() )
  }
  if ( explicitFileName ) {
    # Make sure internal record of ModelState location tracks with where we're loading it from
    FileName=normalizePath(FileName,mustWork=TRUE,winslash="/")
    ModelStatePath <- dirname(FileName)
    if ( envir$ModelState_ls$ModelStatePath != ModelStatePath ) {
      writeLog(paste("Loading model state originally created as",envir$ModelState_ls$ModelStatePath),Level="warn")
      envir$ModelState_ls$ModelStatePath = ModelStatePath
    }
    envir$ModelStateFile <- FileName
  }
  return ( Param_ls )
}

# GET MODEL STATE
#================
#' Get elements from ModelState in ve.model environment (option to Load)
#'
#' \code{getModelState} a visioneval framework control function that
#' gets ModelState elements from ModelState_ls in the ve.model environment
#'
#' Use this faster function inside of modules rather than readModelState, which
#' can load the ModelState.Rda or work with different environments.
#' Note that it just becomes a call to readModelState if any
#' parameters are passed (see \code{readModelState}).
#'
#' @param envir An R environment with assigned Datastore functions and a ModelState_ls
#' @param stopOnError if TRUE (default) stop if model state is not initialized else return NULL
#' @param ... If there are any parameters, this quietly becomes a call to readModelState(...) which
#'    can subset, read a different file, load into an environment, etc.
#' @return The model state list
#' @export
getModelState <- function(envir=NULL,stopOnError=TRUE,...) {
  if ( ! missing(...) ) return(readModelState(envir=envir,...))
  if ( is.null(envir) ) envir <- modelEnvironment()
  if ( ! "ModelState_ls" %in% ls(envir=envir) ) {
    if ( stopOnError ){
      stop("getModelState: ModelState is not initialized.")
    }
  }
  return(envir$ModelState_ls) # Returns NULL if not stopOnError
}

#SET (UPDATE) MODEL STATE
#==================
#' Update model state.
#'
#' \code{setModelState} a visioneval framework control function that updates the
#' list that keeps track of the model state with list of components to update
#' and resaves in the model state file.
#'
#' Key variables that are important for managing the model run are stored in a
#' list (ModelState_ls) that is in the global workspace and saved in the
#' 'ModelState.Rda' file. This function updates  entries in the model state list
#' with a supplied named list of values, and then saves the results in the file.
#'
#' @param ChangeState_ls A named list of components to change in ModelState_ls.
#'   If empty, just Save (and if Save==FALSE, do nothing)
#' @param FileName A string identifying the name of the file in which to save
#'   the ModelState_ls list. The default name is 'ModelState.Rda'.
#' @param Save A boolean (default TRUE) saying whether to save the
#' ModelState to its file (otherwise only ModelState_ls in ve.model
#'   environment is updated)
#' @param envir An R environment with assigned Datastore functions and a ModelState_ls
#'   (see assignDatastoreFunctions)
#' @return always TRUE
#' @export
setModelState <- function(ChangeState_ls=list(), FileName = NULL, Save=TRUE, envir=modelEnvironment()) {
  # Get current ModelState
  currentModelState_ls <- readModelState(FileName=FileName,envir=envir)

  # Make requested changes, if any
  # (pass an empty list just to Save existing ModelState_Ls)
  if ( length(ChangeState_ls) > 0 ) {
    # Replace names in currentModelState_ls with corresponding names in ChangeState_ls
    currentModelState_ls[ names(ChangeState_ls) ] <- ChangeState_ls
    currentModelState_ls$LastChanged <- Sys.time()
    envir$ModelState_ls <- currentModelState_ls
  }

  if ( Save ) {
    if ( is.null(FileName) ) FileName <- envir$ModelStateFile # file.path(envir$ModelStatePath,getModelStateFileName())
    writeLog(paste0("Saving ",paste(names(ChangeState_ls),collapse=",")),Level="trace")
    writeLog(paste0("To file: ",FileName),Level="trace")
    result <- try(save("ModelState_ls",envir=envir,file=FileName))
    if ( class(result) == 'try-error' ) {
      Msg <- paste('Could not write ModelState:', FileName)
      writeLog(Msg,Level="error")
      writeLog(result,Level="error")
      stop(Msg,call.=FALSE)
    }
  } else if ( length(ChangeState_ls)==0 ) {
    writeLog("I'm just sitting here watching the wheels go round and round...",Level="trace")
    writeLogMessage(.traceback(1),Level="trace")
  }
  invisible(TRUE)
}

#GET MODEL STATE FILENAME
#========================
#' Get model state file name
#'
#' \code{getModelStateFileName} a visioneval framework control function that
#' reports the model state file name for this model (from configuration)
#'
#' @param Param_ls a run parameter list to search (if NULL, the default, load the RunParam_Ls from
#    the modelEnvironment if that exists)
#' @return a character string containing the ModelState file base name
#' @export
getModelStateFileName <- function(Param_ls=NULL) {
  # We'll look in envir$RunParam_ls for name and path information
  return(basename(getRunParameter("ModelStateFile", Param_ls=Param_ls)))
}

#READ MODEL STATE FILE
#=====================
#' Reads values from model state file (attempting to load if not present)
#'
#' \code{readModelState} a visioneval framework control function that reads
#' components of the file that saves a copy of the model state.
#'
#' The model state is stored in a list (ModelState_ls) that is also saved as a
#' file (ModelState.Rda) whenever the list is updated. This function reads the
#' contents of the ModelState.Rda file.
#'
#' @param Names_ A string vector of the components to extract from the
#' ModelState_ls list.
#' @param FileName A string vector with the full path name of the model state
#' file.
#' @param envir An environment into which to load ModelState.Rda
#' @return A list containing the specified components from the model state file.
#' @export
readModelState <- function(Names_ = "All", FileName=NULL, envir=NULL) {
  # Establish environment
  if ( is.null(envir) ) envir <- modelEnvironment()
  if ( !is.null(FileName) ) {
    if ( length(loadModelState(FileName,envir))==0 ) {
      Msg <- paste("Could not load ModelState from",FileName)
      writeLog(c(Msg,getwd()),Level="error")
      writeLogMessage(.traceback(2))
      stop(Msg,call.=FALSE)
    }
  }
  State_ls <- get0("ModelState_ls",envir=envir,ifnotfound=list())
  if (Names_[1] == "All") {
    return(State_ls)
  } else {
    return(State_ls[Names_])
  }
}

#RETRIEVE YEARS
#==============
#' Retrieve years
#'
#' \code{getYears} a visioneval framework model user function that reads the
#' Years component from the the model state file.
#'
#' This is a convenience function to make it easier to retrieve the Years
#' component of the model state file which lists all of the specified model run
#' years. If the Years component includes the base year, then the returned
#' vector of years places the base year first in the order. This ordering is
#' important because some modules calculate future year values by pivoting off
#' of base year values so the base year must be run first.
#'
#' @param envir An environment into which to load ModelState.Rda (default ve.model)
#' @return A character vector of the model run years.
#' @export
getYears <- function(envir=modelEnvironment()) {
  BaseYear <- readModelState(envir=envir)$BaseYear
  Years <- readModelState(envir=envir)$Years
  if (BaseYear %in% Years) {
    c(BaseYear, Years[!Years %in% BaseYear])
  } else {
    Years
  }
}

#RETRIEVE DEFAULT UNITS
#======================
#' Retrieve default units for model
#'
#' \code{getUnits} a visioneval framework control function that retrieves the
#' default model units for a vector of complex data types.
#'
#' This is a convenience function to make it easier to retrieve the default
#' units for a complex data type (e.g. distance, volume, speed). The default
#' units are the units used to store the complex data type in the datastore.
#'
#' @param Type_ A string vector identifying the complex data type(s).
#' @param envir An environment into which to load ModelState.Rda (default ve.model)
#' @return A string vector identifying the default units for the complex data
#' type(s) or NA if any of the type(s) are not defined.
#' @export
getUnits <- function(Type_,envir=modelEnvironment()) {
  Units_df <- getModelState(envir=envir)$Units
  Units_ <- Units_df$Units
  names(Units_) <- Units_df$Type
  Result_ <- Units_[Type_]
  if (any(is.na(Result_))) Result_ <- NA
  Result_
}
#getUnits("Bogus")
#getUnits("currency")
#getUnits("area")

#==================
#PARSE MODULE CALLS
#==================
#' parse the list of module calls from the ModelScriptFile
#'
#' Process the raw list of module calls and their parameters from the ModelScriptFile and expand
#' them into a detailed list of input/output specifications.
#'
#' @param ModuleCalls_df a list of module calls returned from \code{parseModelScript}
#' @param AlreadyInitialized a character vector of names of packages that
#'   would already have been initialized (if loading a pre-existing Datastore)
#' @param RequiredPackages a character vector of packages already required
#' @param Save if FALSE, just update ModelState in memory, otherwise write changes to disk
#' @return A list of all processed module specifications
#' @export
parseModuleCalls <- function( ModuleCalls_df, AlreadyInitialized=character(0), RequiredPackages=character(0), Save=TRUE ) {

  ModuleCalls_df <- unique(ModuleCalls_df)
  # Use 'Instance' as non-empty string to distinguish multiple calls that might have different specs

  # Report any required packages that are not also in module calls
  umc <- unique(ModuleCalls_df$PackageName)
  explicitRequired_ <- unique(RequiredPackages)
  if ( any( not.in.umc <- ! (explicitRequired_ %in% umc) ) ) {
    for ( p in explicitRequired_[not.in.umc] ) {
      writeLog(paste("Package",p,"is required"),Level="info")
    }
  }
  RequiredPackages <- c(umc,explicitRequired_)

  #Get list of installed packages
  #Check that all module packages are in list of installed packages
  InstalledPkgs_ <- rownames(installed.packages())
  MissingPkg_ <- RequiredPackages[!(RequiredPackages %in% InstalledPkgs_)]
  if (length(MissingPkg_ != 0)) {
    Msg <-
    paste0("One or more required packages need to be installed in order ",
      "to run the model. Following are the missing package(s): ",
      paste(MissingPkg_, collapse = ", "), ".")
    writeLog(Msg,Level="error")
    stop(Msg)
  }
  #Check for 'Initialize' module in each package if so add to ModuleCalls_df
  Add_ls <- list()
  # Do not add Initialize if the package was required in an earlier model stage
  # Results of Initialization in that case will already be in the Datastore
  for (Pkg in unique(setdiff(ModuleCalls_df$PackageName,AlreadyInitialized))) {
    PkgData <- data(package = Pkg)$results[,"Item"]
    if ("InitializeSpecifications" %in% PkgData) {
      Add_df <-
      data.frame(
        ModuleName = "Initialize",
        PackageName = Pkg,
        RunFor = "AllYears",
        RunYear = "Year",
        Instance = NA
      )
      Add_ls[[Pkg]] <- Add_df
    }
  }
  writeLog("Adding initialize module entries",Level="info")
  Pkg_ <- names(Add_ls)
  for (Pkg in Pkg_) {
    Idx <- head(grep(Pkg, ModuleCalls_df$PackageName), 1)
    End <- nrow(ModuleCalls_df)
    ModuleCalls_df <- rbind(
      ModuleCalls_df[1:(Idx - 1),],
      Add_ls[[Pkg]],
      ModuleCalls_df[Idx:End,]
    )
  }

  #Identify all modules and datasets in required packages
  Datasets_df <-
  data.frame(
    do.call(
      rbind,
      lapply(RequiredPackages, function(x) {
        data(package = x)$results[,c("Package", "Item")]
      })
    ), stringsAsFactors = FALSE
  )
  WhichAreModules_ <- grep("Specifications", Datasets_df$Item)
  ModulesByPackage_df <- Datasets_df[WhichAreModules_,]
  ModulesByPackage_df$Module <-
  gsub("Specifications", "", ModulesByPackage_df$Item)
  ModulesByPackage_df$Item <- NULL
  DatasetsByPackage_df <- Datasets_df[-WhichAreModules_,]
  names(DatasetsByPackage_df) <- c("Package", "Dataset")
  #Save the modules and datasets lists in the model state
  setModelState(
    list (
      ModulesByPackage_df = ModulesByPackage_df,
      DatasetsByPackage_df = DatasetsByPackage_df,
      RequiredVEPackages = RequiredPackages
    ),
    Save=Save
  )

  #Iterate through each module call and check availability and specifications
  #create combined list of all specifications
  # ModulesByPackage_df lists all modules available in the packages
  # ModuleCalls_df lists only modules that appear in runModule commands
  # AllSpecs_ls is a list of processed specifications, which is returned from the function
  Errors_ <- character(0)
  AllSpecs_ls <- list()
  for (i in 1:nrow(ModuleCalls_df)) {
    AllSpecs_ls[[i]] <- list()
    ModuleName <- ModuleCalls_df$ModuleName[i]
    AllSpecs_ls[[i]]$ModuleName <- ModuleName
    PackageName <- ModuleCalls_df$PackageName[i]
    AllSpecs_ls[[i]]$PackageName <- PackageName
    Instance <- ModuleCalls_df$Instance[i]
    AllSpecs_ls[[i]]$Instance <- Instance
    AllSpecs_ls[[i]]$RunFor <- ModuleCalls_df$RunFor[i]
    names(AllSpecs_ls)[i] <- paste0(PackageName,"::",ModuleName)
    #Check module availability
    Err <- checkModuleExists(ModuleName, PackageName, InstalledPkgs_)
    if (length(Err) > 0) {
      Errors_ <- c(Errors_, Err)
      next()
    }
    #Load and check the module specifications
    ## DEBUG
    ## writeLog(paste(PackageName,ModuleName,sep="::"),Level="warn")
    Specs_ls <- processModuleSpecs(getModuleSpecs(ModuleName, PackageName, AllSpecs_ls=AllSpecs_ls, Instance=Instance))
    ## DEBUG
    ## print(paste("Get Spec length:",length(Specs_ls$Get)))
    Err <- checkModuleSpecs(Specs_ls, ModuleName)
    if (length(Err) > 0) {
      Errors_ <- c(Errors_, Err)
      next()
    } else {
      AllSpecs_ls[[i]]$Specs <- Specs_ls
    }
    #If the 'Call' spec is not null and is a list, check the called module
    if (!is.null(Specs_ls$Call) && is.list(Specs_ls$Call)) {
      #Iterate through module calls
      for (j in 1:length(Specs_ls$Call)) {
        writeLog(paste("Processing Call spec:",Specs_ls$Call[[j]]),Level="info")
        Call_ <- unlist(strsplit(Specs_ls$Call[[j]], "::"))
        #Check module availability
        if (length(Call_) == 2) { # package explicitly specified
          Err <-
          checkModuleExists(
            Call_[2],
            Call_[1],
            InstalledPkgs_,
            c(Module = ModuleName, Package = PackageName))
        } else  {
          if (length(Call_) == 1) { # only module name is provided
            if (! any(Call_ %in% ModulesByPackage_df$Module) ) {
              Err <- c(
                paste0("Error in runModule call for module ", Call_,"."),
                "It is not present in any package already identified in the model run script.",
                "Please add requirePackage(<package-with-module>) to the script."
              )
            } else {
              callPkgs_ <- ModuleCalls_df$PackageName[Call_ %in% ModuleCalls_df$ModuleName]
              if ( length(callPkgs_)==1 ) { # use existing explicit call to module
                Call_ <- c( callPkgs_, Call_ )
              } else  { # callPkgs_ is probably length 0, but could also have more than 1
                Pkg <- ModulesByPackage_df$Package[ModulesByPackage_df$Module == Call_]
                Call_ <- c(unique(Pkg), Call_)
                if (length(Call_) > 2 ) { # More than one package contains the module
                  callPkgs_ <- Call_[-length(Call_)]
                  callModule_ <- Call_[length(Call_)]
                  testPkgs_ <- callPkgs_[callPkgs_ %in% explicitRequired_]
                  if ( length(testPkgs_) == 0 ) testPkgs_ <- callPkgs_  # No explicit required package
                  if ( length(testPkgs_) > 1 ) { # Could not resolve by explicit required package
                    Msg_ <- paste("Providing module",callModule_,"from package",testPkgs_[1])
                    Warn_ <- c(
                      Msg_,
                      paste("Also present in: ", paste(testPkgs_[2:length(testPkgs_)],collapse=", ")),
                      "Use requirePackage() to force selection."
                    )
                    writeLog(Warn_,Level="warn")
                  } else { # Resolved to exactly one package with the module
                    writeLog(paste("Provided module",callModule_,"from Package",testPkgs_[1]),Level="info")
                  }
                  Call_ <- c(testPkgs_[1],callModule_) # Use the first package found, unless explicit
                }
              }
            }
          } else {
            Err <- paste("Cannot fathom Call specification:",Specs_ls$Call[[j]])
          }
        }
        if (length(Err) > 0) {
          Errors_ <- c(Errors_, Err)
          next()
        }
        # Load and check the module specifications and add Get specs if
        # there are no specification errors
        for (k in 1:(length(Call_)-1)) { # Code above forces length(Call_) always to be 2 or fail
          CallSpecs_ls <- processModuleSpecs(getModuleSpecs(Call_[length(Call_)], Call_[k], AllSpecs_ls=AllSpecs_ls))
          Err <- checkModuleSpecs(CallSpecs_ls, Call_[length(Call_)])
          if (length(Err) > 0) {
            Errors_ <- c(Errors_, Err)
            next()
          } else {
            # Add the Get specs for the called modules
            # Any module that calls another should already have its Set specs listed...
            AllSpecs_ls[[i]]$Specs$Get <- c(AllSpecs_ls[[i]]$Specs$Get, CallSpecs_ls$Get)
          }            
        }
      }
    }

    #If any errors, print to log and stop execution
    if (length(Errors_) > 0) {
      Msg <-
      paste0("There are errors in the module calls:\n",
        "package not installed, or module not present in package, ",
        "or invalid module specifications.")
      writeLog(c(Msg,Errors_),Level="error")
      stop(Msg," Check log for details")
    }
  }
  ## DEBUG
  # for ( mod in names(AllSpecs_ls) ) {
  #   print(paste(mod,length(AllSpecs_ls[[mod]]$Spec$Get)))
  #   if ( grepl('CreateHouseholds',mod) ) {
  #     print(sapply(AllSpecs_ls[[mod]]$Spec$Get,function(s)s$NAME))
  #   }
  # }
  setModelState(list(AllSpecs_ls=AllSpecs_ls),Save=Save)
  return(invisible(AllSpecs_ls))
}

#DOCUMENT A MODULE
#=================
#' Produces markdown documentation for a module
#'
#' \code{documentModule} a visioneval framework module developer function
#' that creates a vignettes directory if one does not exist and produces
#' module documentation in markdown format which is saved in the vignettes
#' directory.
#'
#' This function produces documentation for a module in markdown format. A
#' 'vignettes' directory is created if it does not exist and the markdown file
#' and any associated resources such as image files are saved in that directory.
#' The function is meant to be called within and at the end of the module
#' script. The documentation is created from a commented block within the
#' module script which is enclosed by the opening tag, <doc>, and the closing
#' tag, </doc>. (Note, these tags must be commented along with all the other
#' text in the block). This commented block may also include tags which identify
#' resources to include within the documentation. These tags identify the
#' type of resource and the name of the resource which is located in the 'data'
#' directory. A colon (:) is used to separate the resource type and resource
#' name identifiers. For example:
#' <txt:DvmtModel_ls$EstimationStats$NonMetroZeroDvmt_GLM$Summary>
#' is a tag which will insert text which is located in a component of the
#' DvmtModel_ls list that is saved as an rdata file in the 'data' directory
#' (i.e. data/DvmtModel_ls.rda). The following 3 resource types are recognized:
#' * txt - a vector of strings which are inserted as lines of text in a code block
#' * fig - a png file which is inserted as an image
#' * tab - a matrix or data frame which is inserted as a table
#' The function also reads in the module specifications and creates
#' tables that document user input files, data the module gets from the
#' datastore, and the data the module produces that is saved in the datastore.
#' This function is intended to be called in the R script which defines the
#' module. It is placed near the end of the script (after the portions of the
#' script which estimate module parameters and define the module specifications)
#' so that it is run when the package is built. It may not properly in other
#' contexts.
#'
#' @param ModuleName A string identifying the name of the module
#' (e.g. 'CalculateHouseholdDvmt')
#' @return None. The function has the side effects of creating a 'vignettes'
#' directory if one does not exist, copying identified 'fig' resources to the
#' 'vignettes' directory, and saving the markdown documentation file to the
#' 'vignettes' directory. The markdown file is named with the module name and
#' has a 'md' suffix.
#' @export
#' @import knitr
documentModule <- function(ModuleName){

  # Do not bother to re-run if in BUILD phase (only processed during
  # documentation/check step).
  if ( toupper(Sys.getenv("VE_BUILD_PHASE","SAVE")) != "SAVE" ) return()

  #Make vignettes directory if doesn't exist
  #TODO: should put this output into /vignettes
  #-----------------------------------------
  if(!file.exists("inst/module_docs")) dir.create("inst/module_docs",recursive=TRUE)

  #Define function to trim strings
  #-------------------------------
  trimStr <- function(String, Delimiters = NULL, Indices = NULL, Characters = NULL) {
    Chars_ <- unlist(strsplit(String, ""))
    if (!is.null(Delimiters)) {
      Start <- which(Chars_ == Delimiters[1]) + 1
      End <- which(Chars_ == Delimiters[2]) - 1
      Result <- paste(Chars_[Start:End], collapse = "")
    }
    if (!is.null(Indices)) {
      Result <- paste(Chars_[-Indices], collapse = "")
    }
    if (!is.null(Characters)) {
      Result <- paste(Chars_[!(Chars_ %in% Characters)], collapse = "")
    }
    Result
  }

  #Define function to split documentation into list
  #------------------------------------------------
  splitDocs <- function(Docs_, Idx_) {
    Starts_ <- c(1, Idx_ + 1)
    Ends_ <- c(Idx_ - 1, length(Docs_))
    apply(cbind(Starts_, Ends_), 1, function(x) Docs_[x[1]:x[2]])
  }

  #Define function to process documentation tag
  #--------------------------------------------
  #Returns list identifying documentation type and reference
  processDocTag <- function(DocTag) {
    DocTag <- trimStr(DocTag, Delimiters = c("<", ">"))
    DocTagParts_ <- unlist(strsplit(DocTag, ":"))
    if (length(DocTagParts_) != 2) {
      DocTag_ <- c(Type = "none", Reference = "none")
    } else {
      DocTag_ <- DocTagParts_
      names(DocTag_) <- c("Type", "Reference")
      DocTag_["Type"] <- tolower(DocTag_["Type"])
      if (!(DocTag_["Type"] %in% c("txt", "tab", "fig"))) {
        DocTag_["Type"] <- "none"
      }
    }
    DocTag_
  }

  #Define function to insert Rmarkdown for 'txt' tags
  #--------------------------------------------------
  insertTxtMarkdown <- function(Reference) {
    Object <- unlist(strsplit(Reference, "\\$"))[1]
    File <- paste0("data/", Object, ".rda")
    if (file.exists(File)) {
      load(File)
      if (!is.null(eval(parse(text = Reference)))) {
        Text_ <- eval(parse(text = Reference))
        Markdown_ <- c(
          "```",
          Text_,
          "```"
        )
        rm(list = Object)
      } else {
        return("Error in module documentation tag reference")
      }
    } else {
      return("Error in module documentation tag reference")
    }
    Markdown_
  }

  #Define function to insert Rmarkdown for 'fig' tags
  #--------------------------------------------------
  insertFigMarkdown <- function(Reference) {
    FromFile <- paste0("data/", Reference)
    ToFile <- paste0("inst/module_docs/", Reference)
    if (file.exists(FromFile)) {
      if (file.exists(ToFile)) file.remove(ToFile)
      file.copy(from = FromFile, to = ToFile)
      file.remove(FromFile)
      Markdown_ <- paste0("![", Reference, "](", Reference, ")")
    } else {
      return("Error in module documentation tag reference")
    }
    Markdown_
  }

  #Define function to insert Rmarkdown for 'tab' tags
  #--------------------------------------------------
  insertTabMarkdown <- function(Reference) {
    Object <- unlist(strsplit(Reference, "\\$"))[1]
    File <- paste0("data/", Object, ".rda")
    if (file.exists(File)) {
      load(File)
      if (!is.null(eval(parse(text = Reference)))) {
        Table_df <- eval(parse(text = Reference))
        ColNames <- colnames(Table_df)
        Markdown_ <- c(
          "",
          kable(
            eval(Table_df),
            format = "markdown",
            col.names = ColNames)
        )
        rm(list = Object, Table_df, ColNames)
      } else {
        return("Error in module documentation tag reference")
      }
    } else {
      return("Error in module documentation tag reference")
    }
    Markdown_
  }

  #Locate documentation portion of script and strip off leading comments
  #---------------------------------------------------------------------
  FilePath <- paste0("R/", ModuleName, ".R")
  Text_ <- readLines(FilePath)
  DocStart <- grep("#<doc>", Text_) + 1
  DocEnd <- grep("#</doc>", Text_) - 1
  Docs_ <-
  unlist(lapply(Text_[DocStart:DocEnd], function(x) trimStr(x, Indices = 1)))

  #Insert knitr code for inserting documentation tag information
  #-------------------------------------------------------------
  #Locate statistics documentation tags
  TagIdx_ <- grep("<", Docs_)
  #If one or more tags, split Docs_ and insert tag content
  if (length(TagIdx_) > 0) {
    #Split documentation into list of components before and after tags
    Docs_ls <- splitDocs(Docs_, TagIdx_)
    #Initialize new list into which knitr-processed tags will be inserted
    RevDocs_ls <- list(
      Docs_ls[1]
    )
    #Iterate through tags and insert knitr-processed tags
    #if there are any tags
    for (n in 1:length(TagIdx_)) {
      Idx <- TagIdx_[n]
      DocTag_ <- processDocTag(Docs_[Idx])
      if (DocTag_["Type"] == "none") {
        Markdown_ <- "Error in module documentation tag"
      }
      if (DocTag_["Type"] == "txt") {
        Markdown_ <- insertTxtMarkdown(DocTag_["Reference"])
      }
      if (DocTag_["Type"] == "fig") {
        Markdown_ <- insertFigMarkdown(DocTag_["Reference"])
      }
      if (DocTag_["Type"] == "tab") {
        Markdown_ <- insertTabMarkdown(DocTag_["Reference"])
      }
      RevDocs_ls <- c(
        RevDocs_ls,
        list(Markdown_),
        Docs_ls[n + 1]
      )
    }
  } else {
    RevDocs_ls <- list(
      list(Docs_)
    )
  }

  #Functions to assist in loading and processing module specifications
  #-------------------------------------------------------------------
  #Define function to process specifications
  processModuleSpecs_local <- function(Spec_ls) {
    #Define a function to expand a specification having multiple NAMEs
    expandSpec <- function(SpecToExpand_ls, ComponentName) {
      Names_ <- unlist(SpecToExpand_ls$NAME)
      Descriptions_ <- unlist(SpecToExpand_ls$DESCRIPTION)
      Expanded_ls <- list()
      for (i in 1:length(Names_)) {
        Temp_ls <- SpecToExpand_ls
        Temp_ls$NAME <- Names_[i]
        Temp_ls$DESCRIPTION <- Descriptions_[i]
        Expanded_ls <- c(Expanded_ls, list(Temp_ls))
      }
      Expanded_ls
    }
    #Define a function to process a component of a specifications list
    processComponent <- function(Component_ls, ComponentName) {
      Result_ls <- list()
      for (i in 1:length(Component_ls)) {
        Temp_ls <- Component_ls[[i]]
        Result_ls <- c(Result_ls, expandSpec(Temp_ls, ComponentName))
      }
      Result_ls
    }
    #Process the list components and return the results
    Out_ls <- list()
    Out_ls$RunBy <- Spec_ls$RunBy
    if (!is.null(Spec_ls$NewInpTable)) {
      Out_ls$NewInpTable <- Spec_ls$NewInpTable
    }
    if (!is.null(Spec_ls$NewSetTable)) {
      Out_ls$NewSetTable <- Spec_ls$NewSetTable
    }
    if (!is.null(Spec_ls$Inp)) {
      Out_ls$Inp <- processComponent(Spec_ls$Inp, "Inp")
    }
    if (!is.null(Spec_ls$Get)) {
      Out_ls$Get <- processComponent(Spec_ls$Get, "Get")
    }
    if (!is.null(Spec_ls$Set)) {
      Out_ls$Set <- processComponent(Spec_ls$Set, "Set")
    }
    if (!is.null(Spec_ls$Call)) {
      Out_ls$Call <- Spec_ls$Call
    }
    Out_ls
  }
  #Define a function to load the specifications
  loadSpecs <- function(ModuleName) {
    ModuleSpecs <- paste0(ModuleName, "Specifications")
    ModuleSpecsFile <- paste0("data/", ModuleSpecs, ".rda")
    if (file.exists(ModuleSpecsFile)) {
      load(ModuleSpecsFile)
      eval(parse(text = ModuleSpecs))
    } else {
      list()
    }
  }
  #Define function to creates a data frame from specifications Inp, Get, or Set
  makeSpecsTable <- function(ModuleName, Component, SpecNames_) {
    Specs_ls <- processModuleSpecs_local(loadSpecs(ModuleName))[[Component]]
    if (Component == "Inp") {
      Specs_ls <- lapply(Specs_ls, function(x) {
        if (!("OPTIONAL" %in% names(x))) {
          x$OPTIONAL <- FALSE
        }
        x
      })
      SpecNames_ <- c(SpecNames_, "OPTIONAL")
    }
    Specs_ls <- lapply(Specs_ls, function(x) x[SpecNames_])
    Specs_ls <- lapply(Specs_ls, function(x) {
      data.frame(lapply(x, function(y) {
        if (length(y) == 1) {
          y
        } else {
          paste(y, collapse = ", ")
        }
      }))
    })
    do.call(rbind, Specs_ls)
  }
  #Define function to break long strings into lines
  wordWrap <- function(WordString, MaxLength) {
    Words_ls <- list(
      "",
      unlist(strsplit(WordString, " "))
    )
    #Define recursive function to peel off first words from string
    getFirstWords <- function(Words_ls) {
      if (length(Words_ls[[2]]) == 0) {
        return(Words_ls[[1]][-1])
      } else {
        RemWords_ <- Words_ls[[2]]
        NumChar_ <- cumsum(nchar(RemWords_))
        NumChar_ <- NumChar_ + 1
        IsFirst_ <- NumChar_ < MaxLength
        AddString <-paste(RemWords_[IsFirst_], collapse = " ")
        RemWords_ <- RemWords_[!IsFirst_]
        getFirstWords(list(
          c(Words_ls[[1]], AddString),
          RemWords_
        ))
      }
    }
    paste(getFirstWords(Words_ls), collapse = "<br>")
  }

  #Insert documentation of dynamic spec functions
  #----------------------------------------------
  #Make a table of Function specifications
  SpecNames_ <- "FUNCTION"
  FuncSpecs_df <- makeSpecsTable(ModuleName, "Function", SpecNames_)
  if (!is.null(FuncSpecs_df)) {
    FuncMarkdown_ <- c(
      "",
      "## Specification Function",
      paste0("Specifications for this module are generated by a function called '",FuncSpecs_df[1,"FUNCTION"],"'.\n"),
      "See R help and discussion elsewhere in the module documentation.",
      ""
    )
    #Add the markdown text to the documentation list
    RevDocs_ls <- c(
      RevDocs_ls,
      list(FuncMarkdown_)
    )
  }

  #Insert documentation of user input files
  #----------------------------------------
  #Make a table of Inp specifications
  SpecNames_ <-
  c("NAME", "FILE", "TABLE", "GROUP", "TYPE", "UNITS", "PROHIBIT",
    "ISELEMENTOF", "UNLIKELY", "DESCRIPTION")
  InpSpecs_df <- makeSpecsTable(ModuleName, "Inp", SpecNames_)
  #Make markdown text
  if (!is.null(InpSpecs_df)) {
    InpMarkdown_ <- c(
      "",
      "## User Inputs",
      "The following table(s) document each input file that must be provided in order for the module to run correctly. User input files are comma-separated valued (csv) formatted text files. Each row in the table(s) describes a field (column) in the input file. The table names and their meanings are as follows:",
      "",
      "NAME - The field (column) name in the input file. Note that if the 'TYPE' is 'currency' the field name must be followed by a period and the year that the currency is denominated in. For example if the NAME is 'HHIncomePC' (household per capita income) and the input values are in 2010 dollars, the field name in the file must be 'HHIncomePC.2010'. The framework uses the embedded date information to convert the currency into base year currency amounts. The user may also embed a magnitude indicator if inputs are in thousand, millions, etc. The VisionEval model system design and users guide should be consulted on how to do that.",
      "",
      "TYPE - The data type. The framework uses the type to check units and inputs. The user can generally ignore this, but it is important to know whether the 'TYPE' is 'currency'",
      "",
      "UNITS - The units that input values need to represent. Some data types have defined units that are represented as abbreviations or combinations of abbreviations. For example 'MI/HR' means miles per hour. Many of these abbreviations are self evident, but the VisionEval model system design and users guide should be consulted.",
      "",
      "PROHIBIT - Values that are prohibited. Values may not meet any of the listed conditions.",
      "",
      "ISELEMENTOF - Categorical values that are permitted. Value must be one of the listed values.",
      "",
      "UNLIKELY - Values that are unlikely. Values that meet any of the listed conditions are permitted but a warning message will be given when the input data are processed.",
      "",
      "DESCRIPTION - A description of the data.",
      ""
    )
    InpSpecs_ls <- split(InpSpecs_df[, names(InpSpecs_df) != "FILE"], InpSpecs_df$FILE)
    FileNames_ <- names(InpSpecs_ls)
    for (fn in FileNames_) {
      InpMarkdown_ <- c(
        InpMarkdown_,
        paste("###", fn)
      )
      IsOptional <- unique(InpSpecs_ls[[fn]]$OPTIONAL)
      if (IsOptional) {
        InpMarkdown_ <- c(
          InpMarkdown_,
          "This input file is OPTIONAL.",
          ""
        )
      }
      SpecsTable_df <-
      InpSpecs_ls[[fn]][, !(names(InpSpecs_ls[[fn]]) %in% c("TABLE", "GROUP", "OPTIONAL"))]
      # SpecsTable_df$DESCRIPTION <-
      #   unname(sapply(as.character(SpecsTable_df$DESCRIPTION), function(x)
      #   {wordWrap(x, 40)}))
      Geo <- as.character(unique(InpSpecs_ls[[fn]]$TABLE))
      HasGeo <- Geo %in% c("Marea", "Azone", "Bzone", "Czone")
      Year <- as.character(unique(InpSpecs_ls[[fn]]$GROUP))
      if (HasGeo) {
        if (Year == "Year") {
          GeoYearDescription <- paste(
            "Must contain a record for each", Geo, "and model run year."
          )
        }
        if (Year == "BaseYear") {
          GeoYearDescription <- paste(
            "Must contain a record for each", Geo, "for the base year only."
          )
        }
        if (Year == "Global") {
          GeoYearDescription <- paste(
            "Must contain a record for each", Geo, "which is applied to all years."
          )
        }
      } else {
        GeoYearDescription <- paste(
          "Must contain a record for each model run year"
        )
      }
      if (Year == "Year") {
        Year_df <- data.frame(
          NAME = "Year",
          TYPE = "",
          UNITS = "",
          PROHIBIT = "",
          ISELEMENTOF = "",
          UNLIKELY = "",
          DESCRIPTION = GeoYearDescription
        )
        SpecsTable_df <- rbind(Year_df, SpecsTable_df)
      }
      if (HasGeo) {
        Geo_df <- data.frame(
          NAME = "Geo",
          TYPE = "",
          UNITS = "",
          PROHIBIT = "",
          ISELEMENTOF = paste0(Geo, "s"),
          UNLIKELY = "",
          DESCRIPTION = GeoYearDescription
        )
        InpMarkdown_ <- c(
          InpMarkdown_,
          kable(rbind(Geo_df, SpecsTable_df))
        )
      } else {
        InpMarkdown_ <- c(
          InpMarkdown_,
          kable(SpecsTable_df)
        )
      }
    }
  } else {
    InpMarkdown_ <- c(
      "",
      "## User Inputs",
      "This module has no user input requirements."
    )
  }
  #Add the markdown text to the documentation list
  RevDocs_ls <- c(
    RevDocs_ls,
    list(InpMarkdown_)
  )

  #Insert documentation of module inputs
  #-------------------------------------
  #Make a table of Get specifications
  SpecNames_ <-
  c("NAME", "TABLE", "GROUP", "TYPE", "UNITS", "PROHIBIT", "ISELEMENTOF")
  GetSpecs_df <- makeSpecsTable(ModuleName, "Get", SpecNames_)
  #Make markdown text
  if (!is.null(GetSpecs_df)) {
    GetMarkdown_ <- c(
      "",
      "## Datasets Used by the Module",
      "The following table documents each dataset that is retrieved from the datastore and used by the module. Each row in the table describes a dataset. All the datasets must be present in the datastore. One or more of these datasets may be entered into the datastore from the user input files. The table names and their meanings are as follows:",
      "",
      "NAME - The dataset name.",
      "",
      "TABLE - The table in the datastore that the data is retrieved from.",
      "",
      "GROUP - The group in the datastore where the table is located. Note that the datastore has a group named 'Global' and groups for every model run year. For example, if the model run years are 2010 and 2050, then the datastore will have a group named '2010' and a group named '2050'. If the value for 'GROUP' is 'Year', then the dataset will exist in each model run year group. If the value for 'GROUP' is 'BaseYear' then the dataset will only exist in the base year group (e.g. '2010'). If the value for 'GROUP' is 'Global' then the dataset will only exist in the 'Global' group.",
      "",
      "TYPE - The data type. The framework uses the type to check units and inputs. Refer to the model system design and users guide for information on allowed types.",
      "",
      "UNITS - The units that input values need to represent. Some data types have defined units that are represented as abbreviations or combinations of abbreviations. For example 'MI/HR' means miles per hour. Many of these abbreviations are self evident, but the VisionEval model system design and users guide should be consulted.",
      "",
      "PROHIBIT - Values that are prohibited. Values in the datastore do not meet any of the listed conditions.",
      "",
      "ISELEMENTOF - Categorical values that are permitted. Values in the datastore are one or more of the listed values.",
      ""
    )
    SpecsTable_df <- GetSpecs_df
    GetMarkdown_ <- c(
      GetMarkdown_,
      kable(SpecsTable_df)
    )
  } else {
    GetMarkdown_ <- c(
      "",
      "## Datasets Used by the Module",
      "This module uses no datasets that are in the datastore."
    )
  }
  #Add the markdown text to the documentation list
  RevDocs_ls <- c(
    RevDocs_ls,
    list(GetMarkdown_)
  )

  #Insert documentation of module outputs
  #--------------------------------------
  #Make a table of Set specifications
  SpecNames_ <-
  c("NAME", "TABLE", "GROUP", "TYPE", "UNITS", "PROHIBIT", "ISELEMENTOF", "DESCRIPTION")
  SetSpecs_df <- makeSpecsTable(ModuleName, "Set", SpecNames_)
  #Make markdown text
  if (!is.null(SetSpecs_df)) {
    SetMarkdown_ <- c(
      "",
      "## Datasets Produced by the Module",
      "The following table documents each dataset that is placed in the datastore by the module. Each row in the table describes a dataset. All the datasets must be present in the datastore. One or more of these datasets may be entered into the datastore from the user input files. The table names and their meanings are as follows:",
      "",
      "NAME - The dataset name.",
      "",
      "TABLE - The table in the datastore that the data is placed in.",
      "",
      "GROUP - The group in the datastore where the table is located. Note that the datastore has a group named 'Global' and groups for every model run year. For example, if the model run years are 2010 and 2050, then the datastore will have a group named '2010' and a group named '2050'. If the value for 'GROUP' is 'Year', then the dataset will exist in each model run year. If the value for 'GROUP' is 'BaseYear' then the dataset will only exist in the base year group (e.g. '2010'). If the value for 'GROUP' is 'Global' then the dataset will only exist in the 'Global' group.",
      "",
      "TYPE - The data type. The framework uses the type to check units and inputs. Refer to the model system design and users guide for information on allowed types.",
      "",
      "UNITS - The native units that are created in the datastore. Some data types have defined units that are represented as abbreviations or combinations of abbreviations. For example 'MI/HR' means miles per hour. Many of these abbreviations are self evident, but the VisionEval model system design and users guide should be consulted.",
      "",
      "PROHIBIT - Values that are prohibited. Values in the datastore do not meet any of the listed conditions.",
      "",
      "ISELEMENTOF - Categorical values that are permitted. Values in the datastore are one or more of the listed values.",
      "",
      "DESCRIPTION - A description of the data.",
      ""
    )
    SpecsTable_df <- SetSpecs_df
    # SpecsTable_df$DESCRIPTION <-
    #   unname(sapply(as.character(SpecsTable_df$DESCRIPTION), function(x)
    #     {wordWrap(x, 40)}))
    SetMarkdown_ <- c(
      SetMarkdown_,
      kable(SpecsTable_df)
    )
  } else {
    SetMarkdown_ <- c(
      "",
      "## Datasets Produced by the Module",
      "This module produces no datasets to store in the datastore."
    )
  }
  #Add the markdown text to the documentation list
  RevDocs_ls <- c(
    RevDocs_ls,
    list(SetMarkdown_)
  )

  #Produce markdown file documentation
  #-----------------------------------
  writeLines(unlist(RevDocs_ls), paste0("inst/module_docs/", ModuleName, ".md"))
}

#READ GEOGRAPHIC SPECIFICATIONS
#==============================
#' Read geographic specifications.
#'
#' \code{readGeography} a visioneval framework model developer function that reads the
#' geographic specifications file for the model.
#'
#' This function is not used when running a model: it is only intended for debugging geographic
#' file specifications during model development. See initModelState for identical code that
#' loads the geography along with units and deflators
#'
#' This function manages the reading and error checking of geographic specifications for the model.
#' It calls the checkGeography function to check for errors in the specifications. The
#' checkGeography function reads in the file and checks for errors. That function returns a list of
#' any errors that are found and a data frame containing the geographic specifications. If errors
#' are found, write the errors to a log file and stops model execution. If there are no errors, the
#' function adds the geography in the geographic specifications file, the errors are written to the
#' log file and execution stops. If no errors are found, the geographic specifications are added to
#' the model state file. This function is only called from tests.R. Standard model run reproduces
#' these steps in visioneval::initModelState.
#'
#' @param Save A logical (default=TRUE) indicating whether the model state
#'   should be saved to the model state file, or just updated in ve.model environment
#' @param Param_ls a list of parameters
#'   the file and the specifications are consistent. It stops if there are any
#'   errors in the specifications. All of the identified errors are written to
#'   the run log. A data frame containing the file entries is added to the
#'   model state file as Geo_df'.
#' @return The value TRUE is returned if the function is successful at reading
#' @export
readGeography <- function(Save=TRUE,Param_ls=NULL) {
  #Check for errors in the geographic definitions file

  # Load model environment (should have RunParam_ls already loaded, furnishing ...)
  if ( ! is.list(Param_ls) ) {
    model.env <- modelEnvironment()
    Param_ls <- model.env$RunParam_ls
  }

  #TODO: expand to use the same logic as the model initialization,
  #  including spatial data, field name mapping.
  #Read in geographic definitions if file exists, otherwise error
  #--------------------------------------------------------------
  GeoFile <- getRunParameter("GeoFile",Param_ls=Param_ls)
  ParamPath <- getRunParameter("ParamPath",Param_ls=Param_ls)
  GeoFilePath <- file.path(ParamPath,GeoFile)
  GeoFilePath <- GeoFilePath[file.exists(GeoFilePath)][1] # Allow ParamPath to be a vector of Paths
  if ( is.na(GeoFilePath) || length(GeoFilePath)!=1 ) {
    stop(
      writeLog(
        paste("Geography File",GeoFile,"does not exist in",ParamPath),
        Level="error"
      )
    )
  }
  Geo_df <- read.csv(GeoFilePath, colClasses="character")
  attr(Geo_df,"file") <- GeoFilePath
  CheckResults_ls <- checkGeography(Geo_df)

  #Notify if any errors
  Messages_ <- CheckResults_ls$Messages
  if (length(Messages_) > 0) {
    writeLog(Messages_,Level="error")
    stop(paste0("Errors in ", GeoFilePath, ". See log for details."))
  } else {
    writeLog("Geographical indices successfully read.",Level="info")
  }

  #Update the model state file with loaded and checked geography
  setModelState(CheckResults_ls$Update,Save=Save)
  return(TRUE)
}

#CHECK GEOGRAPHIC SPECIFICATIONS
#===============================
#' Check geographic specifications.
#'
#' \code{checkGeography} a visioneval framework control function that checks
#' geographic specifications file for model.
#'
#' This function reads the file containing geographic specifications for the
#' model and checks the file entries to determine whether they are internally
#' consistent. This function is called when setting up the geography file.
#'
#' @param Geo_df A data.frame containing a model geography description
#' @return A list having two components. The first component, 'Messages',
#' contains a string vector of error messages. It has a length of 0 if there are
#' no error messages. The second component, 'Update', is a list of components to
#' update in the model state file. The components of this list include: Geo, a
#' data frame that contains the geographic specifications; BzoneSpecified, a
#' logical identifying whether Bzones are specified; and CzoneSpecified, a
#' logical identifying whether Czones are specified.
#' @export
checkGeography <- function(Geo_df) {
  #Check that file has all required fields and extract field attributes
  #--------------------------------------------------------------------
  # TODO: add field name mapping using RunParam_ls$GeoFileFields
  # Do that in two parts: if the GeoFileFields are present, just carry on
  # Otherwise, see if there is a mapping for the missing ones in GeoFileFields
  # Also, we can relax about Bzone and Czone as those can be filled with NA
  FieldNames_ <- c("Azone", "Bzone", "Czone", "Marea")
  missing <- ! (FieldNames_ %in% names(Geo_df))
  if ( any(missing) ) {
    Message <- paste(attr(Geo_df,"file"),"is missing required fields:",paste(FieldNames_[missing],collapse=", "))
    writeLog(Message,Level="error")
    stop(Message)
  }
  #Check table entries
  #-------------------
  BzoneSpecified <- !all(is.na(Geo_df$Bzone))
  CzoneSpecified <- !all(is.na(Geo_df$Czone))
  Messages_ <- character(0)
  #Determine whether entries are correct if Bzones have not been specified
  if (!BzoneSpecified) {
    if (any(duplicated(Geo_df$Azone))) {
      DupAzone <- unique(Geo_df$Azone[duplicated(Geo_df$Azone)])
      Messages_ <- c(
        Messages_, paste0(
          "Duplicated Azone entries (",
          paste(DupAzone, collapse = ", "),
          ") not allowed when Bzones not specified."
        )
      )
    }
  }

  # Determine whether entries are correct if Bzones have been specified and Czones are unspecified
  if (BzoneSpecified & !CzoneSpecified) {
    # Are Bzones completely specified
    if (any(is.na(Geo_df$Bzone))) {
      Messages_ <- c(Messages_,
        "Either all Bzone entries must be NA or no Bzone entries must be NA.")
    }
    # Are any Bzone names duplicated
    if (any(duplicated(Geo_df$Bzone))) {
      DupBzone <- unique(Geo_df$Bzone[duplicated(Geo_df$Bzone)])
      Messages_ <- c(Messages_, paste0(
        "Duplicated Bzone entries (",
        paste(DupBzone, collapse = ", "),
        ") not allowed."
      ))
    }
    # Are metropolitan area designations consistent
    AzoneMareas_ <- tapply(Geo_df$Marea, Geo_df$Azone, unique)
    AzoneMareas_ <- lapply(AzoneMareas_, function(x) {
      x[x != "None"]
    })
    if (any(unlist(lapply(AzoneMareas_, length)) > 1)) {
      Messages_ <- c(Messages_,
        "At least one Azone is assigned more than one Marea.")
    }
  }
  # Determine whether entries are correct if Czones have been specified
  if (CzoneSpecified) {
    # Are Czones completely specified
    if (any(is.na(Geo_df$Czone))) {
      Messages_ <- c(Messages_,
        "Either all Czone entries must be NA or no Czone entries must be NA.")
    }
    # Are any Czone names duplicated
    if (any(duplicated(Geo_df$Czone))) {
      DupCzone <- unique(Geo_df$Czone[duplicated(Geo_df$Czone)])
      Messages_ <- c(Messages_, paste0(
        "Duplicated Czone entries (",
        paste(DupCzone, collapse = ", "),
        ") not allowed."
      ))
    }
    # Are metropolitan area designations consistent
    AzoneMareas_ <- tapply(Geo_df$Marea, Geo_df$Azone, unique)
    AzoneMareas_ <- lapply(AzoneMareas_, function(x) {
      x[x != "None"]
    })
    if (any(unlist(lapply(AzoneMareas_, length)) > 1)) {
      Messages_ <- c(Messages_,
        "At least one Azone is assigned more than one Marea.")
    }
  }
  # Return messages and elements for ModelState
  Update_ls <- list(Geo_df = Geo_df, BzoneSpecified = BzoneSpecified,
    CzoneSpecified = CzoneSpecified)
  list(Messages = Messages_, Update = Update_ls)
}

#INITIALIZE DATASTORE GEOGRAPHY
#==============================
#' Initialize datastore geography.
#'
#' \code{initDatastoreGeography} a visioneval framework control function that
#' initializes tables and writes datasets to the datastore which describe
#' geographic relationships of the model.
#'
#' This function writes tables to the datastore for each of the geographic
#' levels. These tables are then used during a model run to store values that
#' are either specified in scenario inputs or that are calculated during a model
#' run. The function populates the tables with cross-references between
#' geographic levels. The function reads the model geography (Geo_df) from the
#' model state file. Upon successful completion, the function calls the
#' listDatastore function to update the datastore listing in the global list.
#'
#' @param GroupNames a character vector of the names of groups to initialize
#' the datastore groups to initialize with geography. The purpose of this
#' parameter is to enable the loading of a datastore in a model run, in which
#' case initialization of geography is only needed for new year groups for the
#' model run. The default value is NULL, which is the case when a datastore
#' is not being loaded.
#' @param envir An environment in which to seek the model state
#' @return The function returns TRUE if the geographic tables and datasets are
#'   sucessfully written to the datastore.
#' @export
initDatastoreGeography <- function(GroupNames = NULL, envir=modelEnvironment()) {
  G <- getModelState(envir=envir)

  #Get FILE and INPUTDIR attribute for specifications
  # TODO: update to handle a spatial database as the source (not just a file)
  # TODO: Put layer as FILE, and database as INPUTDIR if GeoFile is two-part
  FILENAME <- attr(G$Geo_df,"file")
  if ( is.character(FILENAME) ) {
    FILE <- basename(FILENAME)
    INPUTDIR <- dirname(FILENAME)
  } else {
    FILE <- INPUTDIR <- as.character(NA)
  }

  #Make lists of zone specifications
  Mareas_ <- unique(G$Geo_df$Marea)
  MareaSpec_ls <- list(
    MODULE = "visioneval",
    NAME = "Marea",
    TABLE = "Marea",
    TYPE = "character",
    UNITS = "",
    NAVALUE = "NA",
    PROHIBIT = "",
    ISELEMENTOF = "",
    SIZE = max(nchar(Mareas_)),
    FILE = FILE,
    INPUTDIR = INPUTDIR
  )
  
  Azones_ <- unique(G$Geo_df$Azone)
  AzoneSpec_ls <- list(
    MODULE = "visioneval",
    NAME = "Azone",
    TABLE = "Azone",
    TYPE = "character",
    UNITS = "",
    NAVALUE = "NA",
    PROHIBIT = "",
    ISELEMENTOF = "",
    SIZE = max(nchar(Azones_)),
    FILE = FILE,
    INPUTDIR = INPUTDIR
  )

  if(G$BzoneSpecified) {
    Bzones_ <- unique(G$Geo_df$Bzone)
    BzoneSpec_ls <- list(
      MODULE = "visioneval",
      NAME = "Bzone",
      TABLE = "Bzone",
      TYPE = "character",
      UNITS = "",
      NAVALUE = "NA",
      PROHIBIT = "",
      ISELEMENTOF = "",
      SIZE = max(nchar(Bzones_)),
      FILE = FILE,
      INPUTDIR = INPUTDIR
    )
  }

  if(G$CzoneSpecified) {
    Czones_ <- unique(G$Geo_df$Czone)
    CzoneSpec_ls <- list(
      MODULE = "visioneval",
      NAME = "Czone",
      TABLE = "Czone",
      TYPE = "character",
      UNITS = "",
      NAVALUE = "NA",
      PROHIBIT = "",
      ISELEMENTOF = "",
      SIZE = max(nchar(Czones_)),
      FILE = FILE,
      INPUTDIR = INPUTDIR
    )
  }

  # closure to create specification list for extra fields
  # TODO: for starters, ignore (don't save) the geometry field
  # TODO: The geometry and smallest defined geo field should be saved separately in the Datastore
  #   Perhaps create a Global/Geometry virtual table that has geometry and Azone or Bzone IDs
  getExtraGeoFields <- function(Geo_df) {
    # TODO: only load specified extra fields from GeoFileExtraFields
    extraFields <- ! names(Geo_df) %in% c("Marea","Azone","Bzone","Czone") # TODO: affirmative present in ExtraFields
    extraFieldSpecs <- list()
    fieldSpec <- list(
      MODULE = "visioneval",
      UNITS = "",
      NAVALUE = "NA",
      PROHIBIT = "",
      ISELEMENTOF = "",
      FILE = FILE,
      INPUTDIR = INPUTDIR
    )
    for ( name in names(Geo_df)[extraFields] ) {
      writeLog(paste("Extra field in geo.csv:",name),Level="info")
      nameSpec <- fieldSpec
      nameSpec$NAME <- name
      nameSpec$SIZE <- max(nchar(Geo_df[[name]]))
      Type <- mode(Geo_df[[name]])
      if ( Type == "numeric" ) Type <- "double" # Probably either "character" or "double"
      nameSpec$TYPE <- Type
      extraFieldSpecs[[name]] <- nameSpec
    }
    return(extraFieldSpecs)
  }

  extraFieldSpecs <- getExtraGeoFields(G$Geo_df) # names other than "Marea","Azone","Bzone","Czone"

  # Perhaps put geometry somewhere else?
  writeExtraFields <- function(Geo_df,extraFieldSpecs,GroupName,Table,envir) {
    GroupTable <- file.path(GroupName,Table)
    for ( name in names(extraFieldSpecs) ) { # Geo_df column names
      Spec_ls <- extraFieldSpecs[[name]]
      Spec_ls$TABLE = Table # Might be Azone or Bzone depending on smallest geography
      writeToTable(Geo_df[[name]], Spec_ls, Group = GroupName, Index = NULL, envir=envir)
      writeLog(paste("Adding Field",name,"to",GroupTable),Level="info")
    }
  }

  # Initialize geography tables and zone datasets
  if (is.null(GroupNames)) GroupNames <- c("Global", G$Years)
  for (GroupName in GroupNames) {
    initTable(Table = "Region", Group = GroupName, Length = 1,envir=envir)
    initTable(Table = "Azone", Group = GroupName, Length = length(Azones_),envir=envir)
    initTable(Table = "Marea", Group = GroupName, Length = length(Mareas_),envir=envir)
    if(G$BzoneSpecified) {
      initTable(Table = "Bzone", Group = GroupName, Length = length(Bzones_),envir=envir)
    }
    if(G$CzoneSpecified) {
      initTable(Table = "Czone", Group = GroupName, Length = length(Czones_),envir=envir)
    }
  }

  # Add zone names to zone tables
  for (GroupName in GroupNames) {
    if (!G$BzoneSpecified & !G$CzoneSpecified) {
      # Write to Azone table
      writeToTable(G$Geo_df$Azone, AzoneSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      MareaSpec_ls$TABLE = "Azone"
      writeToTable(G$Geo_df$Marea, MareaSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      writeExtraFields(G$Geo_df,extraFieldSpecs,GroupName,Table="Azone",envir=envir) # Writes to "Azone" table
      #Write to Marea table
      MareaSpec_ls$TABLE = "Marea"
      writeToTable(Mareas_, MareaSpec_ls, Group = GroupName, Index = NULL, envir=envir)
    } else if (G$BzoneSpecified & !G$CzoneSpecified) {
      #Write to Bzone table
      writeToTable(G$Geo_df$Bzone, BzoneSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      AzoneSpec_ls$TABLE = "Bzone"
      writeToTable(G$Geo_df$Azone, AzoneSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      MareaSpec_ls$TABLE = "Bzone"
      writeToTable(G$Geo_df$Marea, MareaSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      writeExtraFields(G$Geo_df,extraFieldSpecs,GroupName,Table="Bzone",envir=envir) # Writes to "Bzone" table
      #Write to Azone table
      AzoneGeo_df <- G$Geo_df[!duplicated(G$Geo_df$Azone),]
      AzoneSpec_ls$TABLE = "Azone"
      writeToTable(AzoneGeo_df$Azone, AzoneSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      MareaSpec_ls$TABLE = "Azone"
      writeToTable(AzoneGeo_df$Marea, MareaSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      rm(AzoneGeo_df)
      #Write to Marea table
      MareaSpec_ls$TABLE = "Marea"
      writeToTable(Mareas_, MareaSpec_ls, Group = GroupName, Index = NULL, envir=envir)
    } else if (G$CzoneSpecified) {
      #Write to Czone table
      writeToTable(G$Geo_df$Czone, CzoneSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      BzoneSpec_ls$TABLE = "Czone"
      writeToTable(G$Geo_df$Bzone, BzoneSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      AzoneSpec_ls$TABLE = "Czone"
      writeToTable(G$Geo_df$Azone, AzoneSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      MareaSpec_ls$TABLE = "Czone"
      writeToTable(G$Geo_df$Marea, MareaSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      writeExtraFields(G$Geo_df,extraFieldSpecs,GroupName,Table="Czone",envir=envir) # Writes to "Czone" table
      #Write to Bzone table
      BzoneGeo_df <- G$Geo_df[!duplicated(G$Geo_df$Bzone), c("Azone", "Bzone")]
      BzoneSpec_ls$TABLE = "Bzone"
      writeToTable(BzoneGeo_df$Bzone, BzoneSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      AzoneSpec_ls$TABLE = "Bzone"
      writeToTable(BzoneGeo_df$Azone, AzoneSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      rm(BzoneGeo_df)
      #Write to Azone table
      AzoneGeo_df <- G$Geo_df[!duplicated(G$Geo_df$Azone),]
      AzoneSpec_ls$TABLE = "Azone"
      writeToTable(AzoneGeo_df$Azone, AzoneSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      MareaSpec_ls$TABLE = "Azone"
      writeToTable(AzoneGeo_df$Marea, MareaSpec_ls, Group = GroupName, Index = NULL, envir=envir)
      rm(AzoneGeo_df)
      #Write to Marea table
      MareaSpec_ls$TABLE = "Marea"
      writeToTable(Mareas_, MareaSpec_ls, Group = GroupName, Index = NULL, envir=envir)
    }
  }
  # Write to log that completed successfully
  Message <- "Geography sucessfully added to datastore."
  writeLog(Message,Level="info")
  TRUE
  # TODO: return the list of specifications used to create the geography
  # TODO: include FILE as geo.csv and INPUTDIR as ParamPath (or more elaborate for spatial database)
}

#LOAD MODEL PARAMETERS
#=====================
#' Load model global parameters file into datastore.
#'
#' \code{loadModelParameters} a visioneval framework control function reads the
#' 'model_parameters.json' file and stores the contents in the 'Global/Model'
#' group of the datastore.
#'
#' This function reads the 'model_parameters.json' file in the 'defs' directory
#' which contains parameters specific to a model rather than to a module. These
#' area parameters that may be used by any module. Parameters are specified by
#' name, value, and data type. The function creates a 'Model' group in the
#' 'Global' group and stores the values of the appropriate type in the 'Model'
#' group. If FlagChanges is TRUE, don't do the creation - just report consistency.
#'
#' @param FlagChanges logical (default=FALSE); if TRUE, inspect the model parameters
#'   and see if what is in the Datastore is consistent.
#' @param envir An environment into which to load ModelState.Rda (default ve.model)
#' @return The function returns TRUE if the model parameters file exists and
#' its values are sucessfully written to the datastore. If FlagChanges, do
#' not write to the Datastore but instead return FALSE if they are different in some
#' way.
#' @export
loadModelParameters <- function(FlagChanges=FALSE,envir=modelEnvironment()) {
  G <- getModelState(envir=envir)
  RunParam_ls <- G$RunParam_ls;

  writeLog("Loading model parameters file.",Level="info")
  ModelParamFile <- getRunParameter("ModelParamFile",Param_ls=RunParam_ls)
  # look first on InputPath, then for file in ParamPath if not found
  ParamFile <- findRuntimeInputFile(ModelParamFile,Dir="InputPath",Param_ls=RunParam_ls,StopOnError=FALSE)
  if ( is.na(ParamFile) ) {
    # Not Found: Try again looking this time in ParamPath (classic location)
    ParamFile <- findRuntimeInputFile(ModelParamFile,Dir="ParamPath",Param_ls=RunParam_ls,StopOnError=FALSE)
    if ( is.na(ParamFile) ) {
      # Still Not Found: Throw an error
      ErrorMsg <- paste0(
        "Model parameters file (",
        ModelParamFile,
        ") could not be located in ",
        paste(RunParam_ls$ParamDir,RunParam_ls$InputDir,collapse=", ")
      )
      stop( writeLog(ErrorMsg,Level="error") )
    } else {
      ParamFile <- ParamFile[1] # may have multiple InputPaths; pick the first found file
    }
  }
 
  Param_df <- jsonlite::fromJSON(ParamFile)
  if ( ! FlagChanges ) {
    Group <- "Global"
    initTable(Table = "Model", Group = "Global", Length = 1)
    for (i in 1:nrow(Param_df)) {
      Type <- Param_df$TYPE[i]
      if (Type == "character") {
        Value <- Param_df$VALUE[i]
      } else {
        Value <- as.numeric(Param_df$VALUE[i])
      }
      # WARNING: Model parameter TYPE and UNITS are not checked
      Spec_ls <-
      list(
        NAME    = Param_df$NAME[i],
        TABLE   = "Model",
        TYPE    = Type,
        UNITS   = Param_df$UNITS[i],
        NAVALUE = ifelse(Param_df$TYPE[i] == "character", "NA", -9999),
        SIZE    = ifelse(
          Param_df$TYPE[i] == "character",
          nchar(Param_df$VALUE[i]),
          0
        ),
        LENGTH  = 1,
        MODULE  = G$Model
      )
      result <- writeToTable(Value, Spec_ls, Group = "Global", Index = NULL)
    }
  } else {
    Warnings_ <- character(0)
    for (i in 1:nrow(Param_df)) {
      name <- Param_df$NAME[i]
      item <- try (
        # readFromTable stops if name not found; we'll turn that into a warning
        readFromTable(name,Table="Model",Group="Global"),
        silent = TRUE
      )
      if ( class(item)=="try-error" ) {
        Warnings_ <- c(
          Warnings_,
          paste("Previous model parameter",name,"failed to load"),
          as.character(item)
        )
        next
      }
      atts <- attributes(item)
      Type <- Param_df$TYPE[i]
      if ( atts$TYPE != Type ) {
        Warnings_ <- c(Warnings_,
          paste("Model parameter",name," has inconsistent TYPE."),
          paste("Datastore:",atts$TYPE,"versus this model:",Type)
        )
      }
      Value <- if (Type == "character") Param_df$VALUE[i] else as.numeric(Param_df$VALUE[i])
      if ( item != Value ) {
        Warnings_ <- c(Warnings_,
          paste("Model parameter",name," has inconsistent VALUE."),
          paste("Datastore:",Value,"versus this model:",item)
        )
      }
      if ( atts$UNITS != Param_df$UNITS[i] ) {
        Warnings_ <- c(Warnings_,
          paste("Model parameter",name," has inconsistent UNITS."),
          paste("Datastore:",atts$UNITS,"versus this model:",Param_df$UNITS[i])
        )
      }
    }
    if (length(Warnings_) != 0) {
      writeLog(Warnings_,Level="warn")
      result <- FALSE
    } else result <- TRUE
  }
  return(result)
}

#PARSE MODEL SCRIPT
#==================
#' Parse model script.
#'
#' \code{parseModelScript} a visioneval framework control function that reads and
#' parses the model script to identify the sequence of module calls and the
#' associated call arguments.
#'
#' This function reads in the model run script and parses the script to
#' identify the sequence of VisionEval modelelement calls. It extracts each call
#' and identifies the values assigned to the function arguments. It creates a
#' list of the calls with their arguments in the order of the calls in the
#' script.
#'
#' The calls include all the legal VisionEval model elements: runModule, runScript, modelStage,
#' initializeModel, and requirePackage. The return from this function is a list of parameters
#' for each element; data.frames for runModule, runScript, modelStage, a list for initializeModel
#' and a vector of package names for requirePackage. See \code{initializeModel} for details on
#' how those return values are used.
#' 
#' @param FilePath A string identifying the model run script file
#' @return A list of parsed parameters for each of the VisionEval model elements found in the script.
#' @export
parseModelScript <- function(FilePath) {
  writeLog(c("Parsing model script",FilePath),Level="debug")
  if (!file.exists(FilePath)) {
    Msg <- c(
      paste0("Specified model script file does not exist."),
      FilePath
    )
    stop( writeLog(Msg,Level="error") )
  }

  Elements <- extractModelElements(parse(FilePath))
  # Shortcut to extract an elementType from the parsed list of VE model elements
  extractElement <- function(elementType) {
    Elements[sapply(
      Elements,
      function(s,seek){seek %in% names(s)},
      seek=elementType
    )]
  }

  ModuleCalls_df <- do.call(
    rbind.data.frame,
    lapply(
      extractElement("runModule"),
      function(x) normalizeElementFields(x$runModule,ModuleCallNames)
    )
  )

  ScriptCalls_df <- do.call(
    rbind.data.frame,
    lapply(
      extractElement("runScript"),
      function(x) normalizeElementFields(x$runScript,ScriptCallNames)
    )
  )

  InitParams_ls      <- lapply(extractElement("initializeModel"),function(x)x$initializeModel)
  if ( length(InitParams_ls) > 0 ) InitParams_ls <- InitParams_ls[[1]] # Ignore more than one
  RequiredVEPackages <- sapply(extractElement("requirePackage"),function(x)x$requirePackage$Package) # Vector of package names

  writeLog("Done parsing model script",Level="debug")
  return(
    list(
      AllCalls_ls        = Elements,
      ModuleCalls_df     = ModuleCalls_df,
      ScriptCalls_df     = ScriptCalls_df,
      RequiredVEPackages = RequiredVEPackages,
      InitParams_ls      = InitParams_ls
    )
  )
}

# VisionEval Model Elements that we know how to parse
ModelElementNames <- c(
  "runModule",
  "runScript",
  "initializeModel",
  "requirePackage"
)
ModelElementsRegex <- paste(ModelElementNames,collapse="|")

ModuleCallNames <- c("ModuleName","PackageName","RunFor","Instance")
ScriptCallNames <- c("Module","Specification","RunFor","ModuleType")

normalizeElementFields <- function(Elements_ls,NeededNames) {
  missingNames <- setdiff(NeededNames,names(Elements_ls)) # names needed but not found
  for ( name in missingNames ) Elements_ls[[name]] <- NA  # will fill in RunFor and Instance, e.g.
  return(Elements_ls) # return a list with all and only NeededNames
}

# Locate VisionEval functions for match.call
normalizeModelElement <- function(ModelElementName,ElementCall) {
  return(
    match.call(
      eval(parse(text=paste0("visioneval::",ModelElementName))),
      ElementCall
    )
  )
}

# screenTypes turns odd R call argument types (like "symbol") into character strings
# the ones named here are things we would like to keep their original type
screenTypes <- function(x) {
  if ( ! mode(x) %in% c("logical","numeric","character","NULL") ) as.character(x) else x
}

# extractModelElements returns a list of length-one named lists
# The inner named lists are named after their model element and their value is a nested list of arguments
# The list of arguments is rectified with match.call so all possible arguments are accounted for
#    Missing or defaulted named arguments will be provided.
#    Additional arguments destined for ... will be expanded to their name or position
extractModelElements <- function(test.expr,depth=0) {
  # use 'grep' to find the calls to VE Model Elements in test.expr
  ve.elements <- grep(ModelElementsRegex,sapply(test.expr,function(s) all.names(s),simplify=FALSE))

  parsed.calls <- list()
#   if ( length(test.expr)==1 && any(ve.elements>1) ) {
#       for ( deeper.call in Recall(test.expr[[1]],depth=depth+1) ) {
#         parsed.calls[[length(parsed.calls)+1]] <- deeper.call
#       }
#   }
  for ( v in ve.elements ) { # iterate through matching elements
    r <- test.expr[[v]]
    r.char <- as.character(r)
    called <- sub("^visioneval::","",r.char[1]) # handle namespace (optional)
    if ( called %in% ModelElementNames ) {
      # top level call has a VE element: add it to the parse list
      r <- normalizeModelElement(called,r)
      parsed.call <- list(lapply(as.list(r[-1]),screenTypes))
      names(parsed.call) <- called
      attr(parsed.call,"Call") <- deparse(r,width.cutoff=80L)
      parsed.calls[[length(parsed.calls)+1]] <- parsed.call
    } else {
      # call contains other calls that have a VE element (arguments or sub-expressions)
      # handle that by recursing into this function
      for ( deeper.call in Recall(r,depth=depth+1) ) {
        parsed.calls[[length(parsed.calls)+1]] <- deeper.call
      }
    }
  }
  return(parsed.calls)
}

#PROCESS INPUT FILES
#===================
#' Locate and Load Input Files to Datastore
#'
#' \code{processInputFiles} will process the model specification list and load
#' any input files that are required
#'
#' @param AllSpecs_ls a list of model specifications (from parseModuleCalls)
#' @return None
#' @export
processInputFiles <- function(AllSpecs_ls) {
  #Set up a list to store processed inputs for all modules
  ve.model <- modelEnvironment()
  ProcessedInputs_ls <- list()
  #Process inputs for all modules and add results to list
  # ORIGINAL VERSION:
  #     for (i in 1:nrow(ModuleCalls_df)) {
  #       Module <- ModuleCalls_df$ModuleName[i]
  #       Package <- ModuleCalls_df$PackageName[i]
  #       EntryName <- paste(Package, Module, sep = "::")
  #       ModuleSpecs_ls <- processModuleSpecs(getModuleSpecs(Module, Package))
  #       #If there are inputs, process them
  #       if (!is.null(ModuleSpecs_ls$Inp)) {
  #         ProcessedInputs_ls[[EntryName]] <- processModuleInputs(ModuleSpecs_ls, Module)
  #         #If module is Initialize process inputs with Initialize function
  #         if (Module == "Initialize") {
  #           if (length(ProcessedInputs_ls[[Module]]$Errors) == 0) {
  #             initFunc <- eval(parse(text = paste(Package, Module, sep = "::")))
  #             InitData_ls <- ProcessedInputs_ls[[EntryName]]
  #             InitializedInputs_ls <- initFunc(InitData_ls)
  #             ProcessedInputs_ls[[EntryName]]$Data <- InitializedInputs_ls$Data
  #             ProcessedInputs_ls[[EntryName]]$Errors <- InitializedInputs_ls$Errors
  #             if (length(InitializedInputs_ls$Warnings > 0)) {
  #               writeLog(InitializedInputs_ls$Warnings,Level="warn")
  #             }
  #           }
  #         }
  #       }
  #     }
  InpErrors_ <- character(0)
  for (Spec in AllSpecs_ls) {
    Module <- Spec$ModuleName
    Package <- Spec$PackageName
    EntryName <- paste(Package, Module, sep = "::")
    ModuleSpecs_ls <- (Spec$Specs)
    #If there are inputs, process them
    if (!is.null(ModuleSpecs_ls$Inp)) {
      ProcessedInputs_ls[[EntryName]] <- processModuleInputs(ModuleSpecs_ls, Module, Package)
      # Process inputs with Initialize function
      if (length(ProcessedInputs_ls[[EntryName]]$Errors) == 0) {
        if (Module == "Initialize") {
          # Run the Initialize function here so we can swap in defaults for Optional inputs
          # and do Package-level cross-file input checking.
          initFunc <- eval(parse(text = paste(Package, Module, sep = "::")))
          InitData_ls <- ProcessedInputs_ls[[EntryName]]
          InitializedInputs_ls <- initFunc(InitData_ls)
          ProcessedInputs_ls[[EntryName]]$Data <- InitializedInputs_ls$Data
          ProcessedInputs_ls[[EntryName]]$Errors <- InitializedInputs_ls$Errors
          if (length(InitializedInputs_ls$Warnings > 0)) {
            writeLog(InitializedInputs_ls$Warnings,Level="warn")
          } else {
            writeLog(paste0("Initialized ",EntryName," with no errors."),Level="info")
          }
        } else {
          if (length(ProcessedInputs_ls[[EntryName]]$Warnings > 0)) {
            writeLog(paste("Input File warnings for",EntryName),Level="warn")
            writeLog(ProcessedInputs_ls[[EntryName]]$Warnings,Level="warn")
          } else if (length(ProcessedInputs_ls[[EntryName]]$Errors > 0)) {
            writeLog(paste("Input File Errors for",EntryName),Level="error")
            writeLog(ProcessedInputs_ls[[EntryName]]$Errors,Level="error")
            stop("Input File Errors")
          } else {
            writeLog(paste0("Initialized ",EntryName," with no errors."),Level="info")
          }
        }
      }
      # The Errors may have been updated by calling ::Initialize
      if ( length(ProcessedInputs_ls[[EntryName]]$Errors) > 0 ) {
        writeLog(paste0("Error in inputs for ",EntryName),Level="error")
        writeLog(ProcessedInputs_ls[[EntryName]]$Errors,Level="error")
        InpErrors_ <- c( InpErrors_, ProcessedInputs_ls[[EntryName]]$Errors )
      }
    }
  }
  #Check whether there are any input errors
  HasErrors <- length(InpErrors_ != 0)
  if (HasErrors) {
    writeLog(InpErrors_,Level="error")
    stop("Input files have errors. Check the log for details.")
  } else {
    writeLog(paste("No errors:",length(InpErrors_)),Level="info")
  }
  rm(InpErrors_)

  #Load model inputs into the datastore
  #------------------------------------
  # Original Process
  #     for (i in 1:nrow(ModuleCalls_df)) {
  #       #Get information on the inputs
  #       Module <- ModuleCalls_df$ModuleName[i]
  #       Package <- ModuleCalls_df$PackageName[i]
  #       EntryName <- paste(Package, Module, sep = "::")
  #       ModuleSpecs_ls <- processModuleSpecs(getModuleSpecs(Module, Package))
  #       #Eliminate writing any new input table to Global group if it already exists
  #       if (!is.null(ModuleSpecs_ls$NewInpTable)) {
  #         NewInpTableSpecs_ls <- ModuleSpecs_ls$NewInpTable
  #         GlobalTableExists_ <- unlist(lapply(NewInpTableSpecs_ls, function(x) {
  #           if (x$GROUP == "Global") {
  #             checkTableExistence(x$TABLE, "Global", ve.model$ModelState_ls$Datastore)
  #           } else {
  #             FALSE
  #           }
  #         }))
  #         if (all(GlobalTableExists_)) {
  #           ModuleSpecs_ls$NewInpTable <- NULL
  #         } else {
  #           ModuleSpecs_ls$NewInpTable <- NewInpTableSpecs_ls[!GlobalTableExists_]
  #         }
  #       }
  #       #Load inputs to datastore
  #       if (!is.null(ModuleSpecs_ls$Inp)) {
  #         inputsToDatastore(ProcessedInputs_ls[[EntryName]], ModuleSpecs_ls, Module)
  #       }
  #     }
  for (Spec in AllSpecs_ls) {
    #Get information on the inputs
    Module <- Spec$ModuleName
    Package <- Spec$PackageName
    EntryName <- paste(Package, Module, sep = "::")
    ModuleSpecs_ls <- (Spec$Specs)
    #Eliminate writing any new input table to Global group if it already exists
    if (!is.null(ModuleSpecs_ls$NewInpTable)) {
      NewInpTableSpecs_ls <- ModuleSpecs_ls$NewInpTable
      GlobalTableExists_ <- unlist(lapply(NewInpTableSpecs_ls, function(x) {
        if (x$GROUP == "Global") {
          checkTableExistence(x$TABLE, "Global", ve.model$ModelState_ls$Datastore)
        } else {
          FALSE
        }
      }))
      if (all(GlobalTableExists_)) {
        ModuleSpecs_ls$NewInpTable <- NULL
      } else {
        ModuleSpecs_ls$NewInpTable <- NewInpTableSpecs_ls[!GlobalTableExists_]
      }
    }
    #Load inputs to datastore
    if (!is.null(ModuleSpecs_ls$Inp)) {
      inputsToDatastore(ProcessedInputs_ls[[EntryName]], ModuleSpecs_ls, Module)
    }
  }
}
