#!/bin/env Rscript

# Author: <PERSON>

script.contents <- c(
  "build.instructions.builder",
  "ve.setup",
  "ve.build",
  "ve.run",
  # "ve.test",
  # the following are used by 02-install.R / ve.make.installer
  "ve.build.config",
  "getBuildEnvironment",
  "makeGitInfo",
  "saveGitInfo"
)  # for "import" package to construct ve.builder pseudo-package

# Build instructions
build.instructions.builder <- function() {
  build.finished <- "VEStart" %in% utils::installed.packages(lib.loc=ve.env$ve.lib)[,"Package"] 
  paste( collapse="\n", c(
    "ve.setup() to select VE_BUILD and VE_RUNTIME prior to (re-)building (optional).",
    paste0("  VE_BUILD is currently ",ve.build.dir),
    if ( ! build.finished ) paste0("  VE_RUNTIME is currently ",ve.runtime) else NULL,
    if ( ! build.finished ) paste0("ve.build() to build a full VisionEval installation into ",ve.lib) else {
      paste0("ve.build() to rebuild updated packages into ",ve.lib)
    },
    if ( build.finished ) paste0("ve.build(<packages>,reset=TRUE) to rebuild specific <packages> or all of them"),
    if ( build.finished ) {
      paste0("ve.run() to start VisionEval in VE_RUNTIME: ",ve.runtime)
    } else NULL
  ) )
}

# Create and return build environment (parameters for build)
# @return the "ve.build.env" environment from the search path
getBuildEnvironment <- function() {
  if ( ! "ve.build.env" %in% search() ) {
    attach(NULL,name="ve.build.env")
  } else {
    as.environment("ve.build.env")
  }
}

# TODO:
#   - Document how to layer VEBuild or VEBootstrap.R on top of an existing VE_HOME
#     - just need ve-lib and ve-build-config (but defaults should work)
#     - will create ve-src plus the various package downloads (just for package
#       being newly built)
#     - need it to use the existing ve-lib from VE_HOME, so navigating ve-lib
#       when we start up VEBuild and sticking with that will be important.

# IMPORTANT:
#   Keep the documentation below in sync with the stub ve.build in VEBuild since
#   that is what people will consult for function documentation.

# Build the "targets", which call functions from the named .build.functions list
# Expects that getwd() == VE_BUILD and ve.home is defined on the search path
# @param packages a character vector of regular expressions naming VE packages to build; default
#   is an empty character string, which will match all packages; see description above
# @param reset a logical; if TRUE, then remove any package artifacts before rebuilding matched
#   packages; default is FALSE (up to date packages will be minimally rebuilt)
# @param check a logical; if TRUE, run R CMD check; otherwise skip those tests
# @param confirm a logical; if TRUE (default for interactive use), ask user to confirm prior to
#   (re-)building each package.
# @param config a list of configuration elements that replace iems in the ve-config.yml file (see
#   documentation for that file elsewhere)
# @param debug if TRUE or numeric non-zero, issue additional debugging messages during build
# @param listtargets if TRUE just report what packages would be built and exit
# @return data.frame of packages and status (unchanged, built, failed)
ve.build <- function(
  targets="",
  reset=FALSE,
  check=reset,
  confirm=interactive(),
  config=list(),
  debug=FALSE,
  listtargets=FALSE
) {

  ve.build.config(config=config,debug=debug) # Loads "ve.build.env"

  pkg.desc <- ve.get.targets(targets,debug=debug)
  if ( listtargets ) {
    return(pkg.desc)
  }

  ve.load.dependencies(pkg.desc,debug=debug)

  ve.build.packages(pkg.desc,reset=reset,check=check,debug=debug)
}

# Build a configuration for building and installing VE, and load the contents into the
# builder environment, which is used to build VE and make installers.
# @param config a named list overriding defaults or contents of ve-build-config.yml
# @param debug a logical; if TRUE, dish more information about the configuration
# @param quite a logical; if TRUE, suppress some warning messages and other information
# @return NULL
ve.build.config <- function(config=list(),debug=FALSE, quiet=FALSE) {
  # Prepare Configuration and setup from ve-build-config.yml and update from config parameter
  # Populate "ve.build.env" environment in search()

  # NOTE: this function expects ve.home, ve.build.dir, CRAN.mirror, and ve.lib set in "ve.env"
  # Usually ve.env$ve.lib will be the same location as ve.lib later set from configuration file; if
  # the user alters the configuration after starting R, it is possible that the new ve.lib will
  # lead to re-downloading stuff when the full dependencies are built. Unlikely to be a problem in
  # practice. VE-Bootstrap.R sets that environment up, as does VEBuild for deeper end-user builds.

  ve.env <- try( silent=TRUE, as.environment("ve.env") )
  if ( ! is.environment(ve.env) ) {
    stop("VisionEval environment is unavailable.\nUse VE-Bootstrap.R or require(VEBuild) to begin.", call. = FALSE)
  }
  setwd(ve.env$ve.home) # Always return to VE_HOME prior to build (to correctly find 'sources' directory etc)
  
  # ve.build.config returns the ve.build.env with elements added for each of the objects
  # created in the expression block below, and accessible as e.g. as.environment('ve.build.env')$config.file
  # The ve.build.env environment provides values later for the build sub-steps.
  if ( ! quiet ) cat("Loading Build environment...\n")
  bld.env <- getBuildEnvironment()

  bld.env$build.type <- .Platform$pkgType
  if ( ! suppressWarnings(requireNamespace("yaml",quietly=TRUE,lib.loc=ve.env$ve.lib)) ) {
    # Used to read configuration files - always get from online source
    utils::install.packages("yaml", lib=ve.env$ve.lib, repos=ve.env$CRAN.mirror, type=bld.env$build.type, quiet=!debug )
    suppressWarnings(requireNamespace("yaml",quietly=TRUE,lib.loc=ve.env$ve.lib))
  }

  bld.env$ve.wantdocs <- TRUE

  config.file <- "ve-build-config.yml"
  if ( exists("ve.home") ) { # look here for build configuration
    config.file <- file.path(ve.home,config.file)
  }
  raw.config <- if ( file.exists(config.file) ) {
    if (debug) cat("Build configuration file:",config.file,"\n")
    yaml::yaml.load_file(config.file)
  } else {
    if (!quiet) cat("No usable",config.file,": Using default build configuration.\n")
    list()
  }

  default.config <- list(
    # bare defaults
    BuildTargets = c(                         # Standard names for folders in VE_BUILD
      ve.lib = "ve-lib",                      # Where VE packages are installed (in ve.home)
      ve.src = "ve-src",                      # Where package to build is developed
      ve.repository = "ve-pkg-repo",          # Repository for built packages (always source and binary)
      ve.dependencies = "dependencies-repo"   # Repository for dependencies (downloaded, only for platform package type)
    ),
    PackageSources = c( "framework", "modules", "optional" ) # Directories (absolute or relative to VE_HOME) with packages to build
    # Can be a single package directory or the parent of many package
    # directories (sought recursively)
  )
  if ( length(raw.config) == 0 || is.null(names(raw.config)) ) {
    raw.config <- default.config
  } else {
    # Force raw.config to have at least the names in default.config
    missing.names <- ! names(default.config) %in% names(raw.config) 
    default.names <- names(default.config)[ missing.names ]
    raw.config[ default.names ] <- default.config[ missing.names ]
  }

  # BuildTargets are names for things like ve-lib or ve-src (see the sample)
  if ( "BuildTargets" %in% names(raw.config) && is.list(raw.config$BuildTargets) ) {
    # YAML brings BuildTargets in as a named list; make it a named character vector
    raw.config$BuildTargets <- unlist(raw.config$BuildTargets)
  }
  if ( ! "CRAN.mirror" %in% names(raw.config) ) {
    raw.config$CRAN.mirror <- "https://cloud.r-project.org"
  }

  # Add command-line configuration parameters (e.g. replacement PackageSources)
  # message("processing config ",exists("config")) # DEBUG
  if ( is.list(config) && !is.null(names(config)) ) {
    raw.config[names(config)] <- config
  }
  # message("processed config") # DEBUG
  bld.env$raw.config <- raw.config

  # Construct actual directory names from Build-Targets
  bld.env$this.R <- paste(c(R.version["major"],R.version["minor"]),collapse=".")
  bld.env$two.digit.R <- tools::file_path_sans_ext(bld.env$this.R)

  # This is the location where the VE packages are built up prior to being built into R packages
  # That will include steps like creating the "data" directory, building module_docs, etc.
  bld.env$ve.src <- file.path(ve.env$ve.build.dir,raw.config$BuildTargets["ve.src"])
  if ( ! dir.exists(bld.env$ve.src) ) dir.create(bld.env$ve.src)

  # Set up the local repositories (basis for later installation)
  bld.env$ve.repository <- file.path(ve.env$ve.build.dir,raw.config$BuildTargets["ve.repository"])
  if ( ! dir.exists(bld.env$ve.repository) ) dir.create(bld.env$ve.repository)
  bld.env$ve.repository.url <- paste0("file:///",bld.env$ve.repository)
  bld.env$build.contriburl <- utils::contrib.url(bld.env$ve.repository, bld.env$build.type)
  bld.env$build.contriburl.src <- utils::contrib.url(bld.env$ve.repository, "source") # Always build VE source package too
  if ( ! dir.exists(bld.env$build.contriburl) ) dir.create(bld.env$build.contriburl,recursive=TRUE)
  if ( ! dir.exists(bld.env$build.contriburl.src) ) dir.create(bld.env$build.contriburl.src,recursive=TRUE)

  bld.env$CRAN.mirror <- raw.config$CRAN.mirror # to simplify access when we start downloading dependencies

  # Find the packages to build from within folders named in raw.config$PackageSources
  # Start by looking for absolute paths or relative to ve.env$ve.sources
  # getwd() will be VE_BUILD and may differ from ve.home(aka VE_HOME)
  bld.env$package.paths <- normalizePath(raw.config$PackageSources,winslash="/",mustWork=FALSE)
  if ( any( missing.paths <- ! dir.exists(bld.env$package.paths) ) ) {
    # retry package paths looking for subdirectories of ve.sources explicitly
    bld.env$package.paths[missing.paths] <- file.path(ve.env$ve.sources,raw.config$PackageSources[missing.paths])
  }
  if ( !quiet && any( missing.paths <- ! dir.exists(bld.env$package.paths) ) ) {
    # Report failed paths as they appear in the ve-build-config.yml, not the expanded path
    cat("Can't locate certain build paths. These will be ignored:\n")
    print(raw.config$PackageSources[missing.paths])
  }
  if ( debug ) {
    cat("Building packages from these paths:\n")
    print(bld.env$package.paths)
  }
  # The following shortcuts get used during build to find obsolete installed packages
  pkgs.info <- utils::installed.packages(lib.loc=ve.env$ve.lib)
  if ( nrow(pkgs.info) == 0 ) 
  pkgs.info <- utils::installed.packages(lib.loc=ve.env$ve.lib)[,c("Package","Version")]
  bld.env$pkgs.installed <- pkgs.info[,"Package"] # list of installed package names (including dependencies)
  bld.env$pkgs.version <- pkgs.info[,"Version"]   # versions of the packages (only checked later for VE packages)
  rm(pkgs.info)
  NULL
}

ve.get.targets <- function(targets,debug=FALSE) {
  # Determine the specific packages to build (from PackageSources)
  # Create pkg.desc as a named list of information about each package
  # The names are the base names of their folders, not the Package in DESCRIPTION
  # This function (or one of the later ones) will report mismatches (and duplicate Package names)

  bld.env <- getBuildEnvironment()

  all.packages <- dir(bld.env$package.paths,pattern="^DESCRIPTION$",recursive=TRUE,full.name=TRUE)
  target.packages <- character(0)
  for ( tgt in targets ) {
    target.packages <- c(target.packages,grep(tgt,all.packages,value=TRUE))
  }
  target.packages <- dirname(unique(target.packages))
  # package.names <- basename(target.packages) # the "real" name is the Package: in DESCRIPTION
  if ( length(target.packages)>0 ) {
    cat("\nBuilding VisionEval packages found in these directories:\n")
    print(target.packages)
  } else {
    stop("No VisionEval packages were found to build. Check PackageSources in ve-build-config.yml.\n")
  }

  # Load descriptions of the target packages...
  if ( ! suppressWarnings(requireNamespace("desc",quietly=TRUE)) ) {
    # Used to read DESCRIPTION file for Package name and Dependencies
    utils::install.packages("desc", lib=ve.lib, repos=CRAN.mirror, type=bld.env$build.type, quiet=TRUE )
    suppressWarnings(requireNamespace("desc",quietly=TRUE))
  }
  pkg.desc <- lapply(
    target.packages,
    function(pkg) {
      ds <- desc::description$new(pkg)
      deps <- ds$get_deps()
      deps <- deps[ deps$package!="R" & deps$type != "Suggests", ]
      list(
        Package=ds$get("Package"),
        Description=ds,
        Dependencies=deps,
        Folder=pkg
      )
    }
  )
  names(pkg.desc) <- as.character(sapply(pkg.desc,FUN=function(pkg) pkg$Package))

  pkg.desc # return list of packages to build for further processing
}

ve.load.dependencies <- function(pkg.desc,debug=FALSE) {
  # Download and install the R package dependencies
  cat("\nLoading dependencies...\n")
  bld.env <- getBuildEnvironment() # as an environment for these commands, providing configured locations
  support.packages <- c("BiocManager","desc","devtools","dplyr","miniCRAN","rcmdcheck","roxygen2","withr","gert","yaml")
  # We'll get some of these now, and ther est later
  if ( ! suppressWarnings(requireNamespace("dplyr",quietly=TRUE)) ) {
    # Used to easily assemble the dependencies into a single list of packages
    utils::install.packages("dplyr", lib=ve.lib, repos=CRAN.mirror, type=bld.env$build.type, quiet=TRUE )
    suppressWarnings(requireNamespace("dplyr",quietly=TRUE))
  }
  if ( ! suppressWarnings(requireNamespace("BiocManager",quietly=TRUE)) ) {
    # The only current (early 2025) use of BioConductor is for the rhdf5 package. Does anyone still use HDF5?
    utils::install.packages("BiocManager", lib=ve.lib, repos=CRAN.mirror, type=bld.env$build.type, quiet=TRUE )
    suppressWarnings(requireNamespace("BiocManager",quietly=TRUE))
  }
  if ( ! suppressWarnings(requireNamespace("miniCRAN",quietly=TRUE)) ) {
    # Used to build local repository to support later building an offline installer
    utils::install.packages("miniCRAN", lib=ve.lib, repos=CRAN.mirror, type=bld.env$build.type, quiet=TRUE )
    suppressWarnings(requireNamespace("miniCRAN",quietly=TRUE))
    # https://cran.r-project.org/web//packages/miniCRAN/vignettes/miniCRAN-introduction.html
  }

  # BiocManager::repositories() will return appropriate download locations for this version of R
  repos.online <- c(CRAN.mirror,BiocManager::repositories())

  # Assemble a complete list of dependencies that are not currently being built
  # Mark out BaseR packages (even if explicitly listed, they will always be there)
  base.lib <- dirname(find.package("base")) # looking for recommended packages
  pkgs.BaseR <- utils::installed.packages(lib.loc=base.lib, priority=c("base", "recommended"))[,"Package"]
  #   message("BaseR Packages:")
  #   print(pkgs.BaseR)

  # Add support.packages to the list in case no one else asks for them
  # That's needed if VEBuild (which has them as dependencies) is not itself being built
  pkg.deps <- unique(dplyr::bind_rows(lapply(pkg.desc,function(pkg) pkg$Dependencies), .id = "BuildPackage")$package)
  pkg.deps <- unique(c(support.packages,pkg.deps))
  pkg.deps <- pkg.deps[ ! pkg.deps %in% c(names(pkg.desc),pkgs.BaseR) ]
  # ignore any BaseR packages that were explicitly listed as dependencies
  available.online <- utils::available.packages(repos=repos.online,type=bld.env$build.type)[,"Package"] # will take a while...
  available.online <- pkg.deps %in% available.online
  pkg.deps.online <- pkg.deps[ available.online ]
  if ( any( ! available.online ) ) {
    # Make sure offline dependencies (VE or locally built packages) are either already installed or
    # scheduled to be built (i.e. present in pkg.desc list of targets)
    local.deps <- pkg.deps[ ! available.online ]
    installed.local.names <- utils::installed.packages(lib.loc=ve.lib)[,"Package"]
    installed.local <- local.deps %in% installed.local.names # this supports building more packages into a runtime installation
    if ( any( ! installed.local ) ) {
      available.local.names <- utils::available.packages(repos=bld.env$ve.repository.url,type=bld.env$build.type)[,"Package"]
      available.local <- local.deps %in% c(installed.local,available.local.names,names(pkg.desc)) # either built already or scheduled to build
      if ( any( ! available.local ) ) {
        cat("Required package(s) are not built and not scheduled to build:\n")
        print( local.deps[ ! available.local ] )
        stop("Re-run ve.build being sure to include those targets")
      }
    } else {
      for ( local.pkg in local.deps ) {
        if ( ! suppressWarnings(requireNamespace(local.pkg,quietly=TRUE)) ) {
          stop("Local package ",local.pkg," could not be loaded. Please rebuild or reinstall it.")
        } else message("Loaded '",local.pkg,"'")
      }
    }
  }        

  # Install VE package dependencies

  # Remove from pkg.deps any that are installed.
  # If a dependency was installed or built outside the current request, we're okay with that.
  # However, be sure to do a complete build with "reset=TRUE" before building an Installer
  inst.pkgs <- utils::installed.packages(lib.loc=ve.lib)[,"Package"]
  pkg.deps <- pkg.deps[ ! pkg.deps %in% inst.pkgs ]

  # Get full set of dependencies (recursively, dependencies of dependencies)
  # This makes a loooong list...
  if ( length(pkg.deps) > 0 ) {
    expanded.deps <- miniCRAN::pkgDep( pkg.deps, repos=repos.online, suggests=FALSE)
  } else {
    expanded.deps <- character(0)
  }

  # Install dependencies into ve-lib for runtime use
  deps.missing <- pkg.deps[ ! pkg.deps %in% inst.pkgs ]
  if ( length(deps.missing) > 0 ) {
    cat("Installing missing dependencies...\n")
    print(deps.missing)
    utils::install.packages(deps.missing, lib=ve.lib, repos=repos.online,type=bld.env$build.type )
  }
}

ve.build.packages <- function(pkg.desc,reset=FALSE,check=TRUE,debug=FALSE) {
  # Process pkg.desc so we cumulatively build VE packages that depend on earlier VE packages

  # Set up to return to original working directory and remove VE_BUILD_RUNNING semaphore
  oldwd <- getwd()
  on.exit(
    {
      setwd(oldwd)
      Sys.unsetenv("VE_BUILD_RUNNING")
    }
  )

  cat("\nRunning package build...\n\n")
  Sys.setenv(VE_BUILD_RUNNING="Yes") # Semaphore that suppresses certain module code when loading Roxygen etc

  pkg.built <- logical(length(pkg.desc)) # fills all with FALSE - we'll loop multiple times over pkg.desc until all is built
  last.pkg.built <- 0
  while ( any( ! pkg.built ) ) {
    for ( i in seq_along(pkg.desc) ) {
      # Build each package one by one; will return FALSE if missing dependencies and will stop on build failure
      pkg.built[i] <- ve.build.one.package(pkg.desc[[i]],reset=reset,check=check,debug=debug) # TRUE if it was built successfully
    }
    built.this.time <- length(which(pkg.built))
    if ( built.this.time > last.pkg.built ) {
      last.pkg.built <- built.this.time # This number should get bigger on every loop through
    } else {
      cat("Still missing packages that can't be built:\n")
      print( names(pkg.desc)[ ! pkg.built ] )
      stop("Scroll up through the build messages to figure out why they failed.")
    }
  }

  # Finalize the ve.repository
  bld.env <- getBuildEnvironment()
  # Packages get built into a local repository; this step updates the Package index
  # so the repository stays well-formed.
  # We'll always rewrite the PACKAGES file even if nothing got built.
  cat("\nFinalizing VisionEval package bundle.\n")
  tools::write_PACKAGES(bld.env$build.contriburl, type=bld.env$build.type)
  if ( bld.env$build.type != "source" ) {
    tools::write_PACKAGES(bld.env$build.contriburl.src, type="source")
  }
}

# pkg is a description object from pkg.desc list
# ve.src (from ve.build.env) is where to assemble the package to build
# ve.repository is the root of the CRAN-like repository to receive the built package
# if reset is TRUE, blow away all traces of the package source before rebuilding
# if check is TRUE, run R CMD Check
# if debug is TRUE/greater than zero, produce more debugging information
ve.build.one.package <- function(pkg,reset=FALSE,check=TRUE,debug=0) {
  # ve.src,ve.repository,bld.env$build.type="binary")
  bld.env <- getBuildEnvironment()

  # The folder containing the package
  pkg.folder <- pkg$Folder
  pkg.name <- basename(pkg.folder)         # Changed below with warning if pkg.folder != pkg$Package
  pkg.src <- file.path(bld.env$ve.src,pkg$Package) # Where to build the package

  # Prepare package to build in ve.src
  # Allows auto-generation of namespace plus VE data estimation and documentation if required
  cat("Building",pkg$Package)
  if ( pkg.name != pkg$Package ) { # Location in PackageSources does not correspond to DESCRIPTION package name
    cat("\nBuilding Package Name",pkg$Package," differs from Folder ",pkg.folder,"\n")
    cat("Output will be in ",pkg.src,"\n")
    pkg.name <- pkg$Package
  }

  # Check that all the dependencies are installed, otherwise gracefully return FALSE
  # Need to look at all libraries as some system packages slip through the cracks (e.g. methods)
  # The dependencies of interest are those like "visioneval" itself, upon which the module
  # packages all depend. If we try building one of the module packages before "visioneval", we'll
  # just skip it on the theory that we'll eventually do "visioneval" itself, then loop back.
  available.dependencies <- utils::installed.packages()[,"Package"]
  pkg.deps <- pkg$Dependencies$package
  if ( any( missing.deps <- (! pkg.deps %in% available.dependencies) ) ) {
    # Already screened for missing dependencies that are not in the list to build
    # missing.deps will either appear in a later build, or eventually we notice that we've
    # been through the build list and they didn't get finished (e.g. because they failed due to errors)
    if ( debug ) {
      cat(" Still missing dependencies:\n") # DEBUG - don't really need to see this until we've been over the list a few times
      print(pkg.deps[missing.deps])         # DEBUG
    }
    return(FALSE) # hopefully try again after building more possible dependencies
  }

  # Gracefully return TRUE if the package is up to date and installed
  if ( ! reset &&
       ! newerThan(pkg.folder,pkg.src ) &&
       pkg.name %in% utils::installed.packages(lib.loc=ve.lib)[,"Package"] &&
       ! newerThan(pkg.folder,file.path(ve.lib,pkg.name))
     ) {
    cat(": already INSTALLED\n")
    return(TRUE)
  }

  # 
  cat(" from",pkg.folder,"\n\n")

  # Step 1: Determine package status (built, installed)
  built.path.src <- utils::contrib.url(bld.env$ve.repository, type="source")
  built.path.binary <- utils::contrib.url(bld.env$ve.repository, type=bld.env$build.type)
  binary.build <- built.path.binary != built.path.src
  src.module <- file.path(built.path.src,modulePath(pkg.name,built.path.src))
  if (reset) {
    cat("+++++++++++++Removing Previous Build Files\n")
    local({
      module.src <- src.module
      if ( length(module.src)>0 ) { # built source package exists
        module.src <- file.path(built.path.src,module.src)
      } else module_src <- character(0)
      if ( length(module.src)>0 ) {
        unlink(module.src);
        cat(module.src,"\n")
      } else if ( debug ) cat(pkg.name,": No Source Package.\n",sep="")

      if ( binary.build ) {
        module.bin <- modulePath(pkg.name,built.path.binary)
        if ( length(module.bin)>0 ) { # built binary package
          module.bin <- file.path(built.path.binary,module.bin)
        } else module.bin <- character(0)
        if ( length(module.bin)>0 ) {
          unlink(module.bin);
          cat(module.bin,"\n")
        } else if ( debug ) cat(pkg.name,": No Binary Package.\n",sep="")
      }
    })
    if ( dir.exists( pkg.src) ) {
      unlink(pkg.src,recursive=TRUE)
      cat("Removed",pkg.src,"\n")
    } else if ( debug ) {
      cat("No Build Directory.\n")
    }
  }

  # Construct list of pkg.files
  cat("+++++++++++++ Identifying Build Elements\n")
  all.files <- dir(pkg.folder,recursive=TRUE,all.files=FALSE) # not hidden files, relative to pkg.folder
  pkg.files <- grep("^data/",all.files,value=TRUE,invert=TRUE) # ignore data directory (recreate later)
  if ( length(all.files)!=length(pkg.files) ) {
    data.files <- setdiff(all.files,pkg.files)
  } else data.files <- character(0)
  # only releevant dot.files are .VEbuildignore and .Rbuildignore
  dot.files <- dir(pkg.folder,pattern="^\\.(VE|R)buildignore$",all.files=TRUE)
  if ( length(dot.files)>0 ) {
    if ( ".Rbuildignore" %in% dot.files ) {
      pkg.files <- c(pkg.files,".Rbuildignore")
    }
    if ( ".VEbuildignore" %in% dot.files ) {
      ignore.files <- ".VEbuildignore"
      # These are patterns to ignore when copying to src/ folder for build
      # Generally a subset of .Rbuildignore (keeping things like the VEModel walkthrough)
    } else {
      ignore.files <- ".Rbuildignore"
      # Do not copy anything that will be ignored during the R build
    }
    read.dot.files <- file.path(pkg.folder,ignore.files)
    ignore.patterns <- readLines(read.dot.files)
    # empty lines in .Rbuildignore would blow away everything
    ignore.patterns <- grep("^[[:space:]]*$",ignore.patterns,invert=TRUE,value=TRUE)
    if ( debug>2 ) {
      cat("Ignoring ",ignore.files," patterns:\n")
      print(ignore.patterns)
    }
    for ( pattern in ignore.patterns ) {
      if ( debug>2 ) {
        cat("Ignoring:",pattern,"; Before:\n")
        print(pkg.files)
      }
      pkg.files <- grep(pattern=pattern,pkg.files,value=TRUE,invert=TRUE)
      if ( debug>2 ) {
        cat("After:\n")
        print(pkg.files)
      }
    }
  } else {
    cat("No .Rbuildignore found in ",pkg.folder,"\n")
    if ( debug>2 ) {
      print(dir(pkg.folder,recursive=TRUE,all.files=FALSE))
      message("dot.files")
      print(dot.files)
      message("pkg.files")
      print(pkg.files)
    }
  }

  # See what is already built and installed
  check.dir <- file.path(pkg.src,paste0(pkg.name,".Rcheck"))
  if ( debug>2 ) cat( pkg.src,"exists:",dir.exists(pkg.src),"\n")
  package.built <- if ( binary.build ) {
    # On Windows, the package is already built if:
    #   a. Binary package is present, and
    #   b. Source package is present, and
    #   c. package source is not newer than ve.src copy of source
    #   d. check.dir exists (previous built test will verify age of check.dir)
    #   e. Binary package is newer than source package
    me <- sc <- de <- ck <- nt <- vr <- as.logical(NA)
    is.built <- (me <- moduleExists(pkg.name, built.path.binary)) &&
    (sc <- moduleExists(pkg.name, built.path.src)) &&
    (de <- ( dir.exists(pkg.src) && ! newerThan(pkg.folder,pkg.src,quiet=(!debug))) ) &&
    (nt <- ! newerThan( quiet=(!debug),
      pkg.folder, # don't use pkg.files here: file lists will be different
      file.path(built.path.binary,
        modulePath(pkg.name,built.path.binary))) ) &&
    (vr <- samePkgVersion(pkg.folder,getPathVersion(pkg.src),debug=debug) )
    if ( ! is.built && debug ) {
      cat("Status of unbuilt",pkg.name,paste0("(",is.built,")"),"\n")
      cat("Module",me)
      # Some of the test results won't exist since && short-circuits
      if ( !is.na(sc) ) cat(" Src",sc)
      if ( !is.na(de) ) cat(" Dir",de)
      if ( !is.na(nt) ) cat(" Newer",nt)
      cat(" Inst",(pkg.name %in% bld.env$pkgs.installed))
      if ( is.na(vr) ) cat(" Ver",vr)
      cat("\n")
      if ( exists("de") && ( is.na(de) || ! de ) ) {
        cat(pkg.src,ifelse(dir.exists(pkg.src),"Exists","Does not exist"),"\n")
        cat(check.dir,ifelse(dir.exists(check.dir),"Exists","Does not exist"),"\n")
        cat("Newer than on directory (want FALSE):",newerThan(pkg.folder,pkg.src,quiet=FALSE),"\n")
      }
    }
    is.built
  } else {
    # If Source build, the package is "built" if:
    #   a. package source is not newer than ve.src copy of source
    (
      ! is.na(src.module) &&
      dir.exists(pkg.src) &&
      ! newerThan( pkg.name, pkg.files=pkg.files, pkg.src ) &&
      samePkgVersion(pkg.name,getPackageVersion(src.module),debug=(debug>1))
    )
  }
  if ( ! package.built ) {
    cat(pkg.name,"will be built\n")
  } else {
    cat(pkg.name,"is already BUILT\n")
  }

  # Package is installed if it is built and is an available installed package
  package.installed <- (
    package.built &&
    ! is.na( bld.env$pkgs.installed[pkg.name] ) &&
    samePkgVersion(pkg.folder,bld.env$pkgs.version[pkg.name],debug=(debug>1))
  )
  if ( ! package.installed ) {
    if ( package.built ) {
      cat(pkg.name,"will be installed\n")
    }
    if ( pkg.name %in% bld.env$pkgs.installed ) {
      cat("Removing obsolete module package version:",bld.env$pkgs.version[pkg.name],"\n")
      try( {
        base::unloadNamespace(pkg.name)
        utils::remove.packages(pkg.name,lib=ve.lib)
      }
      ) # ignore any errors
    } else {
      cat(pkg.name,"is NOT INSTALLED\n")
    }
  } else {
    cat(pkg.name,"is INSTALLED\n")
  }

  # Step 3: If package is not built, (re-)copy package source to ve.src
  # On Windows: ve.src copy is used to build source and binary packages and to run tests
  # For Source build: ve.src copy is used to build source package
  if ( ! package.built ) {
    if ( debug>1 ) {
      # Dump list of package source files if debugging
      show.pkg.files <- file.path(pkg.folder,dir(pkg.folder,recursive=TRUE,all.files=TRUE))
      if ( ! any(grepl("Rbuildignore",show.pkg.files)) ) warning("No .Rbuildignore for package ",pkg.name)
      cat(paste("Copying",show.pkg.files,"to",pkg.src,"\n",sep=" "),sep="")
    } else {
      cat("++++++++++ Copying module source",pkg.folder,"to build environment...\n")
    }
    if ( is.null(reset) ) reset <- TRUE
    if ( reset ) {
      if ( dir.exists(pkg.src) || file.exists(pkg.src) )
      unlink(pkg.src,recursive=TRUE) # Get rid of the build directory and start fresh
    }
    pkg.dirs <- c(dirname(pkg.files),"data") # recreate a data directory with nothing in it
    # R build process will remove that data directory if it is still empty at the end of the build
    lapply( grep("^\\.$",invert=TRUE,value=TRUE,unique(file.path(pkg.src,pkg.dirs))),
      FUN=function(x) { dir.create(x, showWarnings=FALSE, recursive=TRUE ) } )
    if ( debug ) {
      cat("Copying package files:\n")
      print(pkg.files)
    }
    invisible(
      file.copy(
        from=file.path(pkg.folder,pkg.files),
        to=file.path(pkg.src,pkg.files),
        overwrite=TRUE, recursive=FALSE
      )
    )

    ###### HACK ALERT
    # Code above prevents the build from looking at the Github 'data' directory, since it is too
    # hard to ensure that such data gets updated when new source data is provided. We will rebuild
    # the data directory in all cases.
    #     HOWEVER:
    # VETravelDemandMM includes pre-estimated data files based on confidential NHTS that have to go
    # into the 'data' directory - they are found in 'data-raw/estimated', so we'll just copy them
    # into place...
    ######
    withr::with_dir(pkg.src,{
      MM.estimated <- dir("data-raw/estimated",full.names=TRUE)
      if ( length(MM.estimated)>0 ) {
        file.copy(MM.estimated,"data")
      }
    })
    ###### END HACK

    if ( ! dir.exists(pkg.src) ) {
      stop("Failed to create build/test environment:",pkg.src)
    }
    # Compare newest dates to see if pkg.src is up to date
    if ( newerThan(pkg.folder,pkg.src,quiet=(!debug)) ) {
      # Not sure if this would ever happen in practice...
      stop("After copying, build/test environment is still older than package.paths")
    }

    saveGitInfo(makeGitInfo(pkg.folder),pkg.src) # save to pkg.src/DESCRIPTION
  }

  # Step 4: Run devtools::document() separately to rebuild the /data directory
  # TODO for VE 4.0 - this is where we will load the modules and run their estimation functions
  if ( ! package.built ) {
    cat("++++++++++ Pre-build / Document ",pkg.name,"\n",pkg.src,"\n",sep="")

    # Roxygen will build NAMESPACE and .Rd docs
    if ( bld.env$ve.wantdocs ) { # optionally build .Rd docs
      # Try the build twice since current (9/2025) version of Roxygen fails sometimes to build
      # the NAMESPACE on the first try.
      message("Building namespace and Roxygen docs")
      te <- try( withr::with_dir(pkg.src,roxygen2::roxygenise(roclets=c("collate","namespace","rd"))), silent=TRUE )
      if ( class(te)=="try-error" ) {
        message("Retrying namespace and documents build")
        te <- try( withr::with_dir(pkg.src,roxygen2::roxygenise(roclets=c("collate","namespace","rd"))), silent=TRUE )
        if ( class(te)=="try-error" ) {
          stop(paste("Documentation error (full docs):\n",te))
        }
      }
    } else {
      message("Building namespace only")
      te <- try( withr::with_dir(pkg.src,roxygen2::roxygenise(roclets=c("collate","namespace"))), silent=TRUE)
      if ( class(te)=="try-error" ) {
        message("Retrying namespace build")
        te <- try( withr::with_dir(pkg.src,roxygen2::roxygenise(roclets=c("collate","namespace"))), silent=TRUE)
        if ( class(te)=="try-error" ) {
          stop(paste("Documentation error (namespace only):\n",te))
        }
      }
    }

    if ( check || ( ! reset && ! dir.exists(check.dir) ) ) {
      # Always run check if reset (building from scratch), otherwise only if there is no trace of a prior check.
      cat("++++++++++ Checking and pre-processing ",pkg.name,"\nin ",pkg.src,"\n",sep="")
      # Run the module check (prior to building anything)
      # Set working directory outside devtools:check, or it gets very confused about where to put generated /data elements.
      # Need to set "check.dir" location explicitly to "check_dir=pkg.src" (otherwise lost in space)
      # Also need to make sure that Suggested packages are also loaded (e.g. VE2001NHTS) (cran=FALSE)
      chk.args <- "--no-tests" # Never do build-time tests
      check.results <- withr::with_dir(  pkg.src,
        devtools::check(
          ".",
          check_dir=pkg.src, # what we call check.dir gets recreated by devtools::check
          document=FALSE,
          args=chk.args,
          cran=FALSE,
          error_on="error"
        )
      )
      cat("++++++++++ Check results\n")
      print(check.results)
    }

    # devtools::document with load_pkgload method leaves the package loaded to a temporary library
    # Therefore we need to explicitly detach it so we can install it properly later on
    if ( (bogus.package <- paste("package:",pkg.name,sep="")) %in% search() ) {
      if (debug) cat("Detaching",bogus.package,"\n")
      detach(bogus.package,character.only=TRUE,unload=TRUE)
      if (debug) print(search())
    }

    # Then get rid of the temporary (and possibly obsolete) source package that is left behind
    # Must build again rather than use that built package, because the results of devtools::check
    #   updates (but does not include) any files in /data
    tmp.build <- file.path(pkg.src,modulePath(pkg.name,pkg.src))
    if ( length(tmp.build)>0 && file.exists(tmp.build) ) unlink(tmp.build)
  }

  # If not built, rebuild the source module from pkg.src (this time, with updated /data)
  # and place the result in built.path.src (the VE package repository we're building)
  if ( ! package.built ) {
    obsolete <- dir(built.path.src,pattern=paste0(pkg.name,"*_"))
    if ( debug && length(obsolete)>0 ) cat("obsolete:",obsolete,"\n")
    unlink( file.path(built.path.src,obsolete) )
    src.module <- devtools::build(pkg.src, path=built.path.src)
  }

  # Step 6: Build the binary package (Windows or Mac) and install the package
  tryCatch(
    {
      # VE_BUILD_PHASE="BUILD" says remove package datasets from R/ space (see visioneval/R/module.R)
      # Tells visioneval::savePackageDataset to remove the dataset object rather than save it again
      # Running devtools::document() will have already saved the dataset for the old-style modules
      # New style modules (e.g. VETravelDemandMM) have pre-built data which gets copied into data/
      # above (see the 'hack' which will eventually become standard procedure). So they don't use
      # visioneval::savePackageDataset and don't need/are immune to this flag.
      # In VE 4.0, the code above will run estimation functions to populate /data
      Sys.setenv(VE_BUILD_PHASE="BUILD")
      if ( binary.build ) {
        # Binary build and install works a little differently from source build/install
        if ( ! package.built ) {
          # Rebuild the binary package from the ve.src folder
          # We do this on Windows (rather than building from the source package) because
          # we want to use devtools::build, but a bug in devtools prior to R 3.5.3 or so
          # prevents devtools:build from correctly building from a source package (it
          # requires an unpacked source directory, which we have in pkg.src)
          if ( debug ) cat("building",pkg.name,"from",pkg.src,"as",bld.env$build.type,"\n")
          if ( debug ) cat("building into",built.path.binary,"\n")

          obsolete <- dir(built.path.binary,pattern=paste0(pkg.name,"*_"))
          if ( debug && length(obsolete)>0 ) cat("obsolete:",obsolete,"\n")
          unlink( file.path(built.path.binary,obsolete) )
          built.package <- devtools::build(pkg.src,path=built.path.binary,binary=TRUE)
          if ( length(built.package) > 1 ) { # Fix weird bug that showed up in R 3.6.2 devtools::build
            built.package <- grep("zip$",built.package,value=TRUE)
          cat("++++++++++ BUILT","binary package:",pkg.name,"\n")
          }
        } else {
          cat("++++++++++ BUILT","binary package:",pkg.name,ifelse(package.installed,"(Already Installed)",""),"\n")
          built.package <- file.path(built.path.binary, modulePath(pkg.name, built.path.binary))
        }
        if ( ! package.installed ) {
          # On Windows, install from the binary package
          cat("++++++++++ Installing built package:",built.package,"\n")
          utils::install.packages(built.package, repos=NULL, lib=ve.lib, type=bld.env$build.type) # so they will be available for later modules
          package.installed <- TRUE
        }

      } else { # source build
        # Just do installation directly from source package (no binary package created)
        if ( ! package.installed ) {
          cat("++++++++++ Installing source package:",src.module,"\n")
          utils::install.packages(src.module, repos=NULL, lib=ve.lib, type="source")
          package.installed <- TRUE
        }
      }
      cat("++++++++++ DONE",pkg.name,"\n\n")
    }, # we define no handlers: conditions are just passed through to the parent after calling finally
    finally = Sys.unsetenv("VE_BUILD_PHASE")
  )
  return( package.installed ) # errors should be manifest in the console log
}

#' @param ve.runtime Directory to override standard runtime location search
ve.run <- function(ve.runtime=NULL,setupHome=FALSE) {
  if ( ! suppressMessages(require(VEStart,quietly=TRUE)) ) {
    stop("VEStart is not available - have you run ve.build()?")
  }
  ve.env <- try( silent=TRUE, as.environment("ve.env") )
  if ( ! is.environment(ve.env) ) stop("VisionEval environment is unavailable; please restart")
  existing.runtime <- missing(ve.runtime) || is.null(ve.runtime)
  if ( existing.runtime ) {
    if ( exists("ve.runtime",ve.env,inherits=FALSE) ) {
      ve.runtime <- ve.env$ve.runtime
    } else {
      ve.runtime <- Sys.getenv("VE_RUNTIME",NA)
      if ( is.na(ve.runtime) ) {
        ve.runtime <- file.path(ve.env$ve.home,"runtime")
      }
    }
  }
  if ( ! dir.exists(ve.runtime) ) dir.create(ve.runtime,recursive=TRUE)
  if ( ! dir.exists(ve.runtime) ) stop("Could not establish runtime at '",ve.runtime,"'")

  # startVisionEval will unload VEModel and visioneval
  # it will also create or update .Renviron in ve.runtime
  VEStart::startVisionEval(ve.runtime=ve.runtime,setupHome=setupHome)
}

# \code{makeGitInfo} gets Git repository information for folder \code{from} if that
# is contained in a Git repository, otherwise limited information about the file folder itself.
# @param from a directory path whose Git information is to be reported
# @return a named list of information to pass on to saveGitInfo
makeGitInfo <- function(from) {

  # Collect Git information for DESCRIPTION
  today <- date()
  build.info <- try(repo.info <- gert::git_info(from))
  build.info <- if ( class(build.info) != "try-error" ) {
    list(
      VEBuildDate=today,
      VEBranch=repo.info$shorthand,
      VECommit=gert::git_commit_id(repo=from),  # perhaps we just need the last 8 digits?
      VERemoteURL=gert::git_remote_info(repo.info$remote,repo=from)$url,
      VEUpstreamBranch=repo.info$upstream,
      VELocalRepoPath=repo.info$path
    )
  } else {
    list(
      VEBuildDate=today,
      VEBranch="Not from Git repository",
      VELocalRepoPath=from
    )
  }
  return(build.info)
}

# \code{saveGitInfo} places a list of information into a DCF file like DESCRIPTION or MANIFEST
# It is used by the build process (adding Git info to package descriptions) and in the
# make installer process to generate the MANIFEST describing the installer.
# @param info.list a named list of entries to place in filename
# @param filename a DCF file into which to append the list of values
# @return NULL
saveGitInfo <- function(info.list,to,filename="DESCRIPTION") {
  # info.list is a names list of elements to place in filename
  nms <- names(info.list)
  fn <- file.path(to,filename)
  if ( filename=="DESCRIPTION" ) {
    for ( info in seq_along(info.list) ) {
      # expecting info list to be a set of custom line items for the package DESCRIPTION
      desc::desc_set(nms[info],info.list[[info]],file=fn,normalize=TRUE)
    }
  } else {
    write.dcf(info.list,file=file.path(to,filename))
  }
}

#### Remainder of file contains helper functions

findMissingPackages <- function( required.packages, repos, repo.type=.Platform$pkgType ) {
  # Determine if any packages are missing from the pkg-repository
  # compared to the required.packages passed in.
  #
  # Args:
  #   required.packages: a character vector containing names of packages
  #                      we hope to find in pkg-repository
  #
  # Returns:

  #   A character vector of package names that are missing from the
  #   build.type section of the pkg-repository compared to the
  #   required.packages
  
  apb <- utils::available.packages(repos=repos, type=repo.type)
  return( setdiff( required.packages, apb[,"Package"]) )
}

# The following two helpers extract modules from built packages
# Used in the scripts to detect whether a module has been built yet.
modulePath <- function( module, path ) {
  # determine which module in a vector of names is present in the
  # path
  #
  # Args:
  #   module: a character vector of module names to look for
  #   path: a file system path to look for the modules
  #
  # Returns:
  #   A character vector of the file system names (include version
  #   number strings) for the corresponding packages in module,
  #   if any
  mods <- dir(path)
  # result <- mods[grep(paste("^", basename(module), "_", sep=""), mods)]
  matching <- paste("^", basename(module), "_", sep="")
  test<-sapply(matching,FUN=function(x){ grep(x,mods) },simplify=TRUE,USE.NAMES=FALSE)
  if ( class(test)=="list" ) test <- integer(0) # weirdness of sapply(simplify=TRUE) when empty
  result <- mods[test]
}

moduleExists <- function( module, path ) {
  # determine if modulePath found any 'modules' in 'path'
  #
  # Args:
  #   module: a character vector of module names to look for
  #   path: a file system path to look for the modules
  #
  # Returns:
  #   TRUE if any matching modules were found in path, else FALSE
  #
  # Let us genuflect briefly toward a coding standard that calls for
  # a dozen lines of documentation for a one line "alias"
  found <- modulePath(module,path)
  found.test <- length(found)>0
}

# Helper function to compare package path (source) to a built target (modification date)
newerThan <- function( srcpath, tgtpath, pkg.files=character(0), quiet=TRUE ) {
  # Compare modification time for a set of files to a target file
  #
  # Args:
  #   srcpath - a single folder containing a bunch of files that might be newer, or a vector of files
  #   tgtpath - one (or a vector) of files that may be older, or may not exist
  #   pkg.files - if provided, make sure the same files are present in both places
  #   quiet - if TRUE, then print a message about what is being tested
  #
  # Value: TRUE if the most recently modified source file is newer
  #        than the newest target file
  if (!quiet) cat("Comparing",srcpath,"to",paste(tgtpath,collapse="\n"),"\n")
  if ( any(is.null(srcpath)) || any(is.na(srcpath)) || any(nchar(srcpath))==0 || ! file.exists(srcpath) ) return(TRUE)
  if ( any(is.null(tgtpath)) || any(is.na(tgtpath)) || any(nchar(tgtpath))==0 || ! file.exists(tgtpath) ) return(TRUE)
  if ( dir.exists(srcpath) ) {
    srcfiles <- dir(srcpath,recursive=TRUE,all.files=TRUE)
    if ( length(pkg.files)>0 ) {
      srcfiles <- srcfiles[ srcfiles %in% pkg.files ]
    }
    srcpath <- file.path(srcpath,srcfiles)
  }
  if ( dir.exists(tgtpath) ) {
    tgtfiles <- dir(tgtpath,recursive=TRUE,all.files=TRUE)
    if ( length(pkg.files)>0 ) {
      tgtfiles <- tgtfiles[ tgtfiles %in% pkg.files ]
    }
    tgtpath <- file.path(tgtpath,tgtfiles)
  }
  if ( length(tgtpath) < 1 ) {
    if (!quiet) cat("Newer: target files do not exist\n")
    return(TRUE)
  }
  if ( length(pkg.files)>0 && length(srcpath) > length(tgtpath) ) {
    # Only check for same length file list if pkg.files is provided
    if (!quiet) {
      cat("Newer: target files different length than source\n")
      print( srcfiles[ ! srcfiles %in% tgtfiles ] )
    }
    return(TRUE)
  }
  source.time <- file.mtime(srcpath)
  target.time <- file.mtime(tgtpath)
  source.newest <- order(source.time,decreasing=TRUE)
  target.newest <- order(target.time,decreasing=TRUE)
  if (!quiet) cat("Source:",srcpath[source.newest[1]],strftime(source.time[source.newest[1]],"%d/%m/%y %H:%M:%S"),"\n")
  if (!quiet) cat("Target:",tgtpath[target.newest[1]],strftime(target.time[target.newest[1]],"%d/%m/%y %H:%M:%S"),"\n")
  newer <- source.time[source.newest[1]] > target.time[target.newest[1]]
  if (!quiet) cat("Newer:",newer,"\n")
  return(newer)
}

samePkgVersion <- function( pkg.path, version, debug=FALSE ) {
  # Compare version from package path to a target version (already built)
  #
  # Args:
  #   pkg.path: path to root of a package containing DESCRIPTION
  #   version: a Version string from some other package
  #   debug: print a message
  #
  # Returns:
  #   TRUE if the versions are the same, else FALSE

  # The "all" will handle pathological cases where version is a vector longer than 1
  result <- all((old.version<-getPathVersion(pkg.path)) == version)
  if (debug) {
    cat("samePkgVersion checks",pkg.path,old.version,"against",version,":",result,"\n")
  }
  return( result )
}

getPathVersion <- function( path ) {
  # Extract version string from DESCRIPTION on path
  #
  # Args:
  #   path: path to root of a package containing DESCRIPTION
  #         error if no DESCRIPTION on that path
  #
  # Returns:
  #   Version string from DESCRIPTION file
  desc.path <- file.path(path,"DESCRIPTION")
  if ( ! file.exists(desc.path) ) stop("getPathVersion: Did not find package at",desc.path)
  return ( read.dcf(file=desc.path)[1,"Version"] )
}

getPackageVersion <- function( package ) {
  # Extract version string from a built source module (using version encoded in its name)
  #
  # Args:
  #   package: path to a source or binary package (with version encoded)
  #
  # Returns:
  #   Version string from package file name

  # Eliminate package compression formats
  version <- sapply(strsplit(substr(package,1,regexpr(".(\\.tar\\.gz|\\.zip)",package)),"_"),FUN=function(x)x[2],simplify=TRUE)
  return( version )
}

# IMPORTANT:
#   Keep the documentation below in sync with the stub ve.setup in VEBuild since
#   that is what people will consult for function documentation.

#' Set up VEBuild locations (VE_RUNTIME, VE_BUILD, VE_SOURCE)
#'
#' The VEBuild package loads a separate searchable environment and namespace which contains the
#'   true machinery of ve.setup. The function here exists for documentation purposes and will just
#'   call the ve.setup function in the "ve.builder" pseudo-package.
#' By default, building VE Packages will take place in a \code{built} subdirectory of VE_HOME.
#' This function presents a dialog for selecting a new VE_RUNTIME, VE_BUILD and VE_SOURCE location.
#'   VE_SOURCE is used to locate packages to be built, typically from "VE_HOME/sources".
#'   It exists so a different VE version can be built from another tree while
#'   running R and VEBuild in the original location.
#' If directories selected in the dialog do not exist (which may be the case for the default
#'   VE_BUILD or VE_RUNTIME, for example) they will be created when the dialog values are selected.
#'   VE_SOURCE will not be created and must point to an existing directory.
#'
#' @param ve.runtime default dialog value for VE_RUNTIME (default to VE_HOME/runtime)
#' @param ve.build.dir default dialog value for VE_BUILD (default to VE_HOME/built)
#' @param ve.sources default dialog value for VE_SOURCE (usually VE_HOME/sources)
#' @return named character vector for selected existing directories for VE_BUILD and VE_RUNTIME
use.tcltk <- isTRUE(capabilities()["tcltk"])

ve.setup <- function() {
  # Can just have a series of directory browsers, with sensible defaults based on and updated with
  # VE_HOME (if the others are still their defaults)
  # ve.sources should point to the core repository (or the "sources" folder within it)
  # ve.build.dir will hold the transient artifacts of building
  # ve.runtime will hold the models folder
  # This will update .Renviron in VE_HOME and in VE_RUNTIME
  # Identify UI Script
  if ( use.tcltk ) {
    # Default values are set by VE-Bootstrap.R or .Renviron or VEStart
    locations <- ve.setup.dialog(ve.build.dir=ve.build.dir,ve.runtime=ve.runtime)
    if ( is.character(locations) ) {
      # Update the .Renviron file
      renv.file      <- file.path(ve.home,".Renviron")
      # If .Renviron exists, read read its lines
      if ( file.exists(renv.file) ) {
        renv.txt <- readLines(renv.file,warn=FALSE)
        renv.txt <- grep("^(VE_BUILD|VE_RUNTIME)=",renv.txt,value=TRUE,invert=TRUE) # Overwrite these lines below
      } else renv.txt <- character(0)
      renv.txt <- c(
        renv.txt,
        paste0("VE_BUILD=",locations["VE_BUILD"]),
        paste0("VE_RUNTIME=",locations["VE_RUNTIME"])
      )
      writeLines(renv.txt,renv.file)
    }
  }
}

# Dialog for setting VE_BUILD and VE_SOURCE
# This function does not touch VE_HOME, which is always set during installation and startup
#   as the location where ve-lib exists (or where VE-Bootstrap.R is run to initiate a build).
# VE_RUNTIME can be changed here or provided manually to startVisionEval
# VE_BUILD says where to put the build artifacts
# Each will be (re-)written into VE_HOME/.Renviron
#   And they will propagate via VEStart::startVisionEval into VE_RUNTIME itself
ve.setup.dialog <- function(ve.build.dir, ve.runtime) {

  if ( ! suppressMessages(require(tcltk,quietly=TRUE)) ) {
    message("tcltk is not available for setup dialog")
    message("Please edit .Renviron.")
    return(character(VE_BUILD=ve.build.dir,VE_RUNTIME=ve.runtime))
  }

  # Helper function for making an info button in setup dialog
  question_button_font <- tcltk::tkfont.create(size = 11)
  info_button <- function(parent.frame,popup.text) {
    # Use \u2753 Unicode character for question mark
    tcltk::tkbutton(
      parent.frame,
      text = "\u2753", font = question_button_font, fg="green",
      command = function() {
        tcltk::tkmessageBox(message = popup.text, icon = "info")
      }
    )
  }

  tt <- tcltk::tktoplevel()
  tcltk::tkwm.title(tt, "Select VE directories")
  tcltk::tkwm.maxsize(tt, 800, 10000) # we don't expect to expand vertically

  tcl_VE_BUILD          <- tcltk::tclVar(ve.build.dir)
  tcl_VE_RUNTIME        <- tcltk::tclVar(ve.runtime)
  cancel_ve_setup       <- tcltk::tclVar(0)
  popup_open            <- tcltk::tclVar(0)

  select_build_dir <- function() {
    if (tcltk::tclvalue(popup_open)==0) {
      tcltk::tclvalue(popup_open) <- 1
      tcltk::tkconfigure(build_button,state="disable")
      dir_path <- tcltk::tclvalue(tcltk::tkchooseDirectory(initialdir=ve.build.dir,title="Select VE_BUILD"))
      tcltk::tclvalue(tcl_VE_BUILD) <- if (dir_path != "") {
        dir_path
      } else {
        ve.build.dir
      }
      tcltk::tclvalue(popup_open) <- 0
      tcltk::tkconfigure(build_button,state="normal")
    }
  }

  select_runtime_dir <- function() {
    if (tcltk::tclvalue(popup_open)==0) {
      tcltk::tclvalue(popup_open) <- 1
      tcltk::tkconfigure(runtime_button,state="disable")
      dir_path <- tcltk::tclvalue(tcltk::tkchooseDirectory(initialdir=ve.runtime,title="Select VE_RUNTIME"))
      tcltk::tclvalue(tcl_VE_RUNTIME) <- if (dir_path != "") {
        dir_path
      } else {
        ve.runtime
      }
      tcltk::tclvalue(popup_open) <- 0
      tcltk::tkconfigure(runtime_button,state="normal")
    }
  }

  build_button <- tcltk::tkbutton(tt, text = "Change VE_BUILD", command = select_build_dir)
  tcltk::tkgrid(build_button, column = 0, row = 0, sticky = "e", padx = 5, pady = 5)

  build_frame <- tcltk::tkframe(tt, borderwidth = 2, relief = "groove")
  build_label <- tcltk::tklabel(build_frame,textvariable=tcl_VE_BUILD, justify="left")
  tcltk::tkpack(build_label,anchor="w",padx=5,pady=5) # pack inside frame

  runtime_button <- tcltk::tkbutton(tt, text = "Change VE_RUNTIME", command = select_runtime_dir)
  tcltk::tkgrid(runtime_button, column = 0, row = 1, sticky = "e", padx = 5, pady = 5)

  runtime_frame <- tcltk::tkframe(tt, borderwidth = 2, relief = "groove")
  runtime_label <- tcltk::tklabel(runtime_frame,textvariable=tcl_VE_RUNTIME, justify="left")
  tcltk::tkpack(runtime_label,anchor="w",padx=5,pady=5) # pack inside frame

  tcltk::tkgrid(build_frame,   column = 1, row = 0, sticky="ew", padx = 5, pady = 5)
  tcltk::tkgrid(runtime_frame, column = 1, row = 1, sticky="ew", padx = 5, pady = 5)

  # Help for VE_BUILD
  tcltk::tkgrid(
    info_button(tt,
      paste(sep="",
        "VE_BUILD is the directory where VisionEval build artifacts will be stored. ",
        "The default is the 'built' sub-directory of VE_HOME (directory where VisionEval library ve-lib is located). ",
        "You can use the 'Change VE_BUILD' button to pick a different directory (or create a new on) for your installation.\n\n"
      )
    ), column=2,row=0,padx=5,pady=5
  )

  # Help for VE_RUNTIME
  tcltk::tkgrid(
    info_button(tt,
      paste(sep="",
        "VE_BUILD is the directory where VisionEval models will be stored. ",
        "The default is the 'runtime' sub-directory of VE_HOME (directory where VisionEval library ve-lib is located). ",
        "You can use the 'Change VE_BUILD' button to pick a different directory (or create a new on) for your installation.\n\n"
      )
    ), column=2,row=0,padx=5,pady=5
  )

  # OK and Cancel buttons
  onOK <- function() {
    tcltk::tkdestroy(tt)
  }
  onCancel <- function() {
    tcltk::tclvalue(cancel_ve_setup) <- 1
    tcltk::tkdestroy(tt)
  }

  button_frame <- tcltk::tkframe(tt)
  ok_button <- tcltk::tkbutton(button_frame, text = "OK", command = onOK)
  cancel_button <- tcltk::tkbutton(button_frame, text="Cancel", command = onCancel)
  tcltk::tkgrid(ok_button,column=0,row=0,sticky="e",padx=5)
  tcltk::tkgrid(cancel_button,column=1,row=0,sticky="w",padx=5)
  tcltk::tkgrid(button_frame, column = 0, row = 2, columnspan=2, sticky="ew", padx = 5, pady = 5)

  tcltk::tkgrid.columnconfigure(tt, 1, weight = 1)        # Make the second column expandable.
  tcltk::tkgrid.columnconfigure(button_frame,0, weight=1) # Do we need both of these configures?
  tcltk::tkgrid.columnconfigure(button_frame,1, weight=1)

  tcltk::tkbind(tt, "<Return>", function() {
    onOK()
  })

  tcltk::tkwait.window(tt)

  return (
    if ( tcltk::tclvalue(cancel_ve_setup) > 0 ) {
      NULL
    } else {
      c(
        "VE_BUILD"=tcltk::tclvalue(tcl_VE_BUILD),
        "VE_RUNTIME"=tcltk::tclvalue(tcl_VE_RUNTIME)
      )
    }
  )
}

# ve.test <- function(VEPackage,tests="test.R") # changeRuntime=TRUE,usePkgload=NULL,use.git=NULL,use.env=TRUE) {
#   # Run with the walkthrough if no package provided (or "walkthrough")
#   # Then we'll redirect to other locations as needed
#   # TODO: Only available after doing ve.build(). Will call ve.run() to start
#   # TODO: simpler runtime handling:
#   # TODO: Look for "walkthrough" subdirectory of VE_RUNTIME and create/populate it from VEModel
#   # TODO: Where to find walkthrough scripts depends on whether we are loading VEModel or some other package
#   walkthroughScripts = character(0)
#   if ( missing(VEPackage) || tolower(VEPackage)=="walkthrough" ) { # load the walkthrough
#     # TODO: figure out where to get "walkthrough" files
#     # It should go into whatever ve.runtime is set to, which suggests starting by doing "ve.run()"
#     # Look there for a folder called "walkthrough"
#     # Populate it with the walkthrough scripts obtained from (search in order, first found)
#     # In general, VE_SOURCE will already be set to one of the next two options if source is available
#     #   - VE_SOURCE/framework/VEModel/inst/walkthrough
#     #   - VE_HOME/sources/framework/VEModel/inst/walkthrough
#     #   - VE_HOME/build-source/sources/framework/VEModel/inst/walkthrough
#     #   - system.file("walkthrough","VEModel")
#     if ( changeRuntime ) {
#       # If ve.runtime has not moved away from ve.root (i.e. ve.run() was not yet called)
#       # then do the default ve.run first. Otherwise, we'll place the walkthrough in ve.runtime.
#       # TODO: default ve.run runs startVisionEval, which may be tricky. May need to factor out
#       #   setting ve.runtime from startVisionEval.
#       cur.dir <- getwd()
#       changeDir <- cur.dir==ve.home || ! grepl("walkthrough",cur.dir)
#       # Do walkthrough below current runtime directory
#       if ( changeDir ) {
#         # TODO: The various parameters are probably obsolete and over-complicated
#         # Do the walkthrough file copy here (the copyFiles argument was intended to support relocating
#         #   a runtime including models folder to a different VE_RUNTIME)
#         ve.runtime <- ve.run(changeDir=changeDir,copyFiles="walkthrough",use.git=use.git,use.env=use.env)
#         setwd(file.path(ve.runtime,"walkthrough"))
#       } else {
#         # TODO: A better approach is to always do ve.run(), then change further into "walkthrough"
#         # Trying to avoid recursive creation of "walkthrough" subdirectories if we re-run ve.test
#         # in the same R session without first calling exit.walkthrough()
#         ve.runtime <- ve.run()
#       }
#     } else {
#       # TODO: This works just like the runtime walkthrough except we run inside
#       #   the source code. ve.home is not the right location. We need to do
#       #   build.loader type things to locate VEModel in the source hierarchy.
#       message("Running in VEModel source (for developing walkthrough)")
#       setwd(file.path(ve.sources,"framework/VEModel/inst/walkthrough"))
#     }
#     if ( ! file.exists("00-setup.R") ) {
#       # Make sure directory is aligned right if user is restarting from within
#       # walkthrough runtime.
#       # TODO: take care of this by always repositioning to VE_RUNTIME then finding walkthrough
#       if ( file.exists("../00-setup.R") ) setwd("..")
#     }
#     if ( ! file.exists("00-setup.R") ) {
#       stop("No walkthrough 00-setup.R in ",getwd())
#     } else {
#       # 00-setup.R will create or use a temporary runtime in "walkthrough"
#       message("Loading walkthrough from ",normalizePath("00-setup.R",winslash="/"))
#       source("00-setup.R") # will create shadow runtime directory in "walkthrough"
#       # TODO: double check that it's still working okay. Just need a "models" folder, then
#       # temporarily orient runtime around that location.
#       # TODO: Question: is ve.runtime ever more than the working directory after loading VE?
#     }
#     # Running now in temporary runtime of "walkthrough"
#     walkthroughScripts = grep("00-setup.R",invert=TRUE,value=TRUE,dir("..",pattern="^[01].*\\.R$",full.names=TRUE))
#     if ( is.logical(usePkgload) && usePkgload ) {
#       # TODO: can we automate use of usePkgload if ve.sources
#       # Do a compatible test load of VEModel itself -- useful for using
#       # walkthrough to test (and fix) VEModel.
#       VEPackage = "VEModel"         # debug VEModel
#       changeRuntime = FALSE         # run in location selected above
#       ve.runtime <- getwd()         # override global ve.runtime; TODO: must be able to reset via ve.run()
#       usePkgload = NULL             # revert to default pkgload behavior
#       tests = character(0)          # don't load the VEModel tests
#       # Fall through to do the equivalent of the following while running in the walkthrough runtime
#       # ve.test("VEModel",tests=character(0),changeRuntime=FALSE,usePkgload=NULL)
#     } else {
#       require(VEModel,quietly=TRUE)      # Walkthrough requires VEModel
#       VEModel::setRuntimeDirectory(ve.runtime<-getwd()) # shift ve.runtime to walkthrough runtime
#       message("\nWalkthrough scripts:")
#       print(walkthroughScripts)
#       return(invisible(walkthroughScripts))
#     }
#   } else {
#     # Set the base runtime
#     # TODO: could do this right at the top with a call to ve.run()
#     ve.runtime <- get.ve.runtime(use.git=use.git,use.env=use.env) # use standard runtime for non-walkthrough testing
#   }
# 
#   # Make sure pkgload is available
#   if ( ! suppressWarnings(requireNamespace("pkgload",quietly=TRUE,lib.loc=ve.env$ve.lib)) ) {
#     stop("Missing required package: 'pkgload'")
#   }
#   VEPackage <- VEPackage[1] # can only pkgLoad one at a time
# 
#   # Make sure we start looking from the Github root
#   # TODO: probably want to navigate via ve.sources
#   setwd(ve.home)
# 
#   # Locate the full path to VEPackage
#   framework.package <- FALSE
#   if (
#     ! grepl("/|\\\\",VEPackage) && (
#       ( framework.package <- file.exists( VEPackage.path <- file.path("sources","framework",VEPackage) ) ) ||
#       ( file.exists( VEPackage.path <- file.path("sources","modules",VEPackage) ) )
#     )
#   ) {
#     VEPackage.path <- normalizePath(VEPackage.path,winslash="/",mustWork=FALSE)
#     message("Found ",VEPackage.path)
#   } else if ( file.exists(VEPackage) ) {
#     VEPackage.path <- VEPackage
#     VEPackage <- basename(VEPackage.path)
#   } else {
#     stop("Could not locate ",VEPackage)
#   }
#   message("Testing ",VEPackage," in ",VEPackage.path)
# 
#   # expand "tests" to the full path of each test file
#   # (1) prepend file.path(VEPackage.path,"tests") to all files with separators
#   # (2) check each file for existing and report those that don't exist
#   # (3) normalize all the test paths
#   setwd(VEPackage.path)
#   if ( length(tests) > 0 ) {
#     print(tests)
#     expand.tests <- ! grepl("/|\\\\",tests)
#     tests[expand.tests] <- file.path(VEPackage.path,"tests",tests[expand.tests])
#     tests[!expand.tests] <- normalizePath(tests[!expand.tests],winslash="/",mustWork=FALSE) # relative to VEPackage.path
#     tests <- tests[ file.exists(tests) ]
#   }
# 
#   # Locate the runtime folder where the tests will run
#   # TODO: if changing runtime, create a sub-folder of ve.runtime in which to run
#   if ( changeRuntime ) {
#     # Use a runtime associated specifically with the tested package
#     setwd(VEPackage.path)
#     if ( dir.exists("tests") ) { # in VEPackage.path
#       ve.runtime <- grep("^(tests/)runtime.*",list.dirs("tests"),value=TRUE)[1]
#       if ( dir.exists(ve.runtime) ) {
#         ve.runtime <- normalizePath(ve.runtime,winslash="/",mustWork=FALSE)
#       }
#     } else {
#       dir.create("tests")
#       ve.runtime <- normalizePath(tempfile(pattern="runtime",tmpdir="tests"),winslash="/",mustWork=FALSE)
#     }
#     if ( ! dir.exists(ve.runtime) ) {
#       ve.runtime <- normalizePath(tempfile(pattern="runtime",tmpdir="tests"),winslash="/",mustWork=FALSE)
#       dir.create(ve.runtime)
#     }
#     model.path <- file.path(ve.runtime,"models")
#     if ( ! dir.exists(model.path) ) dir.create(model.path)
#     # TODO: is this too complicated? It's meant for working on visioneval with VEModel and its tests loaded
#     # Use changeRuntime=FALSE to debug this module in a different module's runtime directory
#     # Load the other module with changeRuntime=TRUE
#     # then the new module with changeRuntime=FALSE
#     message("Testing in Package runtime: ",ve.runtime)
#   } else {
#     # Use the standard runtime folder
#     message("Testing in Existing runtime: ",ve.runtime)
#   }
# 
#   # Detach and unload VE-like Packages
#   pkgsLoaded <- names(utils::sessionInfo()$otherPkgs)
#   VEpackages <- grep("(^VE)|(^visioneval$)",pkgsLoaded,value=TRUE)
#   if ( length(VEpackages)>0 ) {
#     VEpackages <- paste0("package:",VEpackages)
#     for ( pkg in VEpackages ) {
#       message("detaching ",pkg)
#       detach(pkg,character.only=TRUE)
#     }
#   }
#   nameSpaces <- names(utils::sessionInfo()$loadedOnly)
#   VEpackages <- grep("^VE",nameSpaces,value=TRUE)
#   if ( length(VEpackages)>0 ) {
#     # sessionInfo keeps packages in the reverse order of loading (newest first)
#     # so we can hopefully unload in the order provided and not trip over dependencies
#     for ( pkg in VEpackages ) {
#       backstop = 0
#       repeat {
#         message("trying to unload package ",pkg)
#         try.unload <- try( unloadNamespace(pkg), silent=TRUE )
#         if ( inherits(try.unload,"try-error") && backstop < 2 ) {
#           try.first <- sub("^.*imported by .([^']+). so cannot be unloaded","\\1",trimws(try.unload))
#           message("Trying first to unload package ",try.first)
#           try( unloadNamespace(try.first), silent=TRUE )
#           backstop <- backstop + 1
#         } else break
#       }
#     }
#   }
#   message("unloading visioneval")
#   unloadNamespace("visioneval")
# 
#   if ( ! is.logical(usePkgload) ) usePkgload <- framework.package
#   if ( usePkgload ) {
#     # Use pkgload::load_all to load up the VEPackage (setwd() to the package root first)
#     message("pkgload::",VEPackage.path)
#     pkgload::load_all(VEPackage.path)
#   } else {
#     # Don't want to use pkgload with module packages since it will re-estimate them
#     # Expect them to be built and loaded; we'll still grab their tests
#     require(VEModel,quietly=TRUE)
#     eval(expr=parse(text=paste0("require(",VEPackage,",quietly=TRUE)"))) # Use the built package
#   }
# 
#   # (Delete and Re-)Create an environment for the package tests ("test.VEPackage) on the search
#   # path sys.source each of the test files into that environment
#   if ( length(tests) > 0 ) {
#     env.testPackage <- "test.VEPackage"
#     if ( env.testPackage %in% search() ) {
#       detach(env.testPackage,character.only=TRUE)
#     }
#     test.env <- attach(NULL,name=env.testPackage)
#     for ( test in tests ) {
#       # Set environment variable with path to test file.
#       # Inside the test file that can be used to load auxiliary files
#       # (e.g. testquery.VEqry in VEModel/tests/test.R)
#       Sys.setenv(VE_test_source=dirname(test))
#       sys.source(test,envir=test.env)
#     }
#     Sys.unsetenv("VE_test_source")
#   }
# 
#   # TODO: Verify that the following will start VEModel in suitable test runtime
#   # TODO: When re-running ve.run() after doing a walkthrough, need to unload any
#   #   visioneval, VEModel, etc. Probably want a function unloadVisionEval to do that.
#   setwd(ve.runtime)
#   if ( ! "setRuntimeDirectory" %in% getNamespaceExports("VEModel") ) {
#     # Hack to support pkgload from source folder which does not have a Namespace
#     if ( "package:VEModel" %in% search() ) {
#       vem <- as.environment("package:VEModel")
#       vem$setRuntimeDirectory(ve.runtime)
#     } else stop("package:VEModel failed to load!")
#   } else {
#     VEModel::setRuntimeDirectory(ve.runtime)
#   }
# 
#   # A list of the objects loaded from the "tests" will be displayed after loading
#   if ( length(walkthroughScripts)>0 ) {
#     message("\nWalkthrough scripts:")
#     print(walkthroughScripts)
#     return(invisible(walkthroughScripts))
#   } else {
#     tests <- objects(test.env)
#     if ( length(tests)==0 ) stop(paste0("No test objects defined for ",VEPackage))
#     message("\nTest functions:")
#     print( tests )
#     return(invisible(tests))
#   }
# } # end of ve.test function definition
