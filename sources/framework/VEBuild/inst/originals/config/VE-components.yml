# Identify VisionEval Components and Dependencies

Components:

# "Type: module" components are built and installed as R packages
# "Type: framework" works like a module, but is built first; also it has a different implied
#        documentation location
# The test script(s) are copied to the "tools/tests/<PackageName> folder
#   Though many packages include tests/scripts they have not been maintained
#   Also, there are cross dependencies. What should be listed here are
#   files that create functions that will run tests (they can reach back into
#   the package for related information) - more to come on that
# The group captures the VE internal dependencies and is used to establish the order of building
#   Module packages are built in order by their Group number, and with Group in the order they are listed here
#   Group 0 will typically be framework module packages (visioneval, VEModel)
#   Group 1 are those without dependencies
#   Group 2 depend on Group 1 packages
#   Group 3 depend on Group 2 etc (if any)
# Docs target is one (or a vector) of locations from which markdown files will be processed into PDFs
#   Build target PDFs will be placed in a name derived (for the framework) from the first element in the  Docs path,
#   and (for the modules) in a folder named after the module:
#       framework [Type]:
#       +api # contains any .md files found at the top level as PDFs
#       +function_docs # contains any .md files found in the indicated folder as PDFs
#       modules
#       + [ModuleName] # contains inst/module_docs as PDFs
  visioneval:
    Type: framework
    Path: sources/framework
    Test:
      Group: 0
      Script: test.R            # relative to visioneval/tests
    CRAN: 
      - futile.logger
      - jsonlite
      - yaml
      - stringr
      - reshape2
      - knitr
      - bookdown            # for rendering documentation
    BioC:
      - rhdf5
  VEModel: # End-user R API
    Type: module
    Path: sources/framework
    Test:
      Group: 0
      Script: test.R
    CRAN:
      - dplyr # simplifies export functions
      - futile.logger
      - jsonlite
      - yaml
      - stringr
      - R6
      - parallelly
      - future
      - future.callr
      - jrc
      - methods
      - DBI
      - RSQLite
      - data.table
  VEBase:
    Type: framework
    Path: sources/framework
    Test:
      Group: 0
      Script: test.R
    CRAN:
      - knitr
  VEBuild:
    Type: framework
    Path: sources/framework
    Test:
      Group: 0
      Script: test.R
    CRAN: # Needs to include all the dev packages...
      - miniCRAN
      - yaml
  VESnapshot: # Sample module
     Type: module
     Path: sources/framework
     Test:
       Group: 0
       Script: test.R
     CRAN:
        - knitr

# Core Modules for building models
# Group 1

  VESimHouseholds:
    Type: module
    Path: sources/modules
    Test:
      Group: 1
#     Script: tests/scripts/test.R
  VESimLandUseData:
    Type: module
    Path: sources/modules
    Test:
      Group: 1
    CRAN:
      - stringr
      - tidycensus
  VESyntheticFirms:
    Type: module
    Path: sources/modules
    Test:
      Group: 1
    CRAN:
      - reshape
  VETransportSupply:
    Type: module
    Path: sources/modules
    Test:
      Group: 1
  VETransportSupplyUse:
    Type: module
    Path: sources/modules
    Test:
      Group: 1
  VE2001NHTS:
    # VE2001NHTS is only used internally for estimating other models
    # Rethink how it is packaged and used for local re-estimation (future change)
    Type: module
    Path: sources/modules
    Test:
      Group: 1

# Group 2 packages
  VEHouseholdTravel:
    Type: module
    Path: sources/modules
    Test:
      Group: 2
    CRAN:
      - filesstrings
      - data.table
      - pscl
    VE:
      - VE2001NHTS
  VEHouseholdVehicles:
    Type: module
    Path: sources/modules
    Test:
      Group: 2
    CRAN:
      - ordinal
      - reshape2
    VE:
      - VE2001NHTS
  VELandUse:
    Type: module
    Path: sources/modules
    Test:
      Group: 2
    CRAN:
      - fields
      - geosphere
    VE:
      - VE2001NHTS
      - VESimHouseholds

# Group 3 Packages
  VEPowertrainsAndFuels:
    Type: module
    Path: sources/modules
    Test:
      Group: 3
    CRAN:
      - data.table
    VE:
      - VE2001NHTS
      - VEHouseholdTravel
  VESimLandUse:
    Type: module
    Path: sources/modules
    Test:
      Group: 3
    CRAN:
      - plot3D
    VE:
      - VELandUse
      - VESimLandUseData

# Group 4 Packages
  VETravelPerformance:
    Type: module
    Path: sources/modules
    Test:
      Group: 4
    CRAN:
      - filesstrings
      - stringr
      - dplyr
    VE:
      - VEHouseholdTravel
      - VEPowertrainsAndFuels
  VESimTransportSupply:
    Type: module
    Path: sources/modules
    Test:
      Group: 4
    VE:
      - VESimLandUse
      - VETransportSupply

# Module / Model packages (include installable sample models)

# Group 5 Packages (model packages, expecting other VE packages)
# May eventually abstract VERSPM from the VEModel package...
# VERSPM:
#   Type: module
#   Path: sources/modules
#   Test:
#     Group: 5
#     Script: tests/scripts/test.R
#   # VERSPM has no package dependencies (only an installable model)
#   VE:
#      - # TODO: list all the packages used in VERSPM model
  VETravelDemandMM:
    Type: module
    Path: sources/modules
    Test:
      Group: 5
    CRAN:
      - dplyr
      - purrr
      - tidyr
      - splines
      - MASS
      - pscl
    VE:
      - VEHouseholdTravel
      - VEHouseholdVehicles
      - VELandUse
      - VEPowertrainsAndFuels
      - VESimHouseholds
      - VETransportSupply
      - VETravelPerformance
  VEState:
    Type: module
    Path: sources/modules
    Test:
      Group: 5
    # VEState has no package dependencies (only an installable model)
    VE:
      - VEHouseholdTravel
      - VEHouseholdVehicles
      - VEPowertrainsAndFuels
      - VESimHouseholds
      - VESimLandUse
      - VESimTransportSupply
      - VETravelPerformance
  VERPAT:
    Type: module
    Path: sources/modules
    Test:
      Group: 5
    CRAN:
      - filesstrings
      - jsonlite
    VE:
      - VEHouseholdTravel
      - VEHouseholdVehicles
      - VELandUse
      - VESimHouseholds
      - VESyntheticFirms
      - VETransportSupply
      - VETransportSupplyUse

# VE NextGen does not install models, just an empty-ish "models" directory in runtime
  Readme-Models.md:
    # Explains how to install a model
    Type: model
    Path: sources/models

# "Type: model" components are copied to "models" folder of runtime under their name
# This type is deprecated and will be removed from future configurations
# Use "script" types instead and set the target to "models/folder"

# "Type: test" components are copied verbatim to "src" environment under its name
# Thos type is deprecated and will be removed from (or changed in) future configurations
# May be used to provide pre-built data for module tests, but we will want to
# run models to build that data, and it will probably not be of interest to
# end-users
# Use "script" types instead for anything to be delivered to the end user runtime
# and set a suitable Target directory

# "Type: runtime" components are copied recursively to the runtime root
# Their name is used to find them under the Path but unlike "script" components
#   no subdirectory is created

  runtime:
    Type: runtime
    Path: sources # copies sources/runtime/* to ve.runtime recursively

# "Type: script" components are copied to the runtime root under their name
  LICENSE:
    Type: script
    Path: "."
# No longer distributing the archaic tools folder
#   tools:
#     Type: script
#     Path: sources
#     CRAN:
#       - import
#       - jsonlite
# Walkthrough will set itself up from within VEModel
#   walkthrough/[01]*.R: # Walkthrough script files
#     Type: script
#     Path: sources/framework/VEModel/inst
#   Readme.md: # Walkthrough detailed instructions
#     Type: script
#     Path: sources/framework/VEModel/inst/walkthrough
#     Target: walkthrough              # output path relative to "ve.runtime" for scripts
  api:                               # "package" for 'docs' type is the last element (can be file name)
    Type: docs
    Path: "."                        # base directory below root to gather docs
    Target: visioneval/api           # output path relative to "ve.docs" for docs
  RStudio.md:
    Type: docs
    Path: build
    Target: visioneval
