Title: VisionEval
Date: 2019-04-26
Description: Include a parallel draw-down of the wiki (and use VE-wiki-components.yml)
Roots:                              # Require ve.root and ve.output at a minimum
    ve.root: ..                     # relative to "build" folder (should be git root for VisionEval-dev)
    ve.docbook: ../../VisionEval-docs  # path to clone of VisionEval-docs repository
    ve.pkg.online: ../../VisionEval-Packages # local clone of drat repository for deployment
Components:                         # Where to find component configurations
    ve.root:
        Root: ve.installer          # refers to ve.root/build
        Config: config/VE-components.yml
#        Exclude:                   # can use to skip building a certain element in VE-components.yml
#          - VEGUI                  # VEGUI is long-since gone
    ve.docbook:
        Root: ve.installer
        Config: config/VE-doc-components.yml

# Intended to add .md documents as PDFs in the end-user installer.

Locations: # "Root" elements are variables defined in "Roots" section
    # path is relative to "augmented" root (depending on "augment" element)
    ve.dependencies:  # Required
        root: ve.output   # ve.output is set to VE_BUILD/built
                          # VE_BUILD is an environment variable that defaults to ve.root
        augment: branch   # "root" uses "root + path"
                          # "branch" uses "root + branch + path" (e.g. non-packages)
                          # "global" uses "root + branch + path + (R)version"
                          # "version" uses "root + branch + (R)version + path"
        path: dependencies-repo
        description: repo for downloaded and built external dependencies
    ve.repository:    # Required
        root: ve.output
        augment: branch
        path: ve-pkg-repo
        description: src/binary repo built from VE packages
    ve.publish:       # Required for publishing to drat/github
        root: ve.pkg.online
        augment: root
        path: docs
        description: location to copy VE packages for online deployment
    ve.pkgs:          # Required
        root: ve.output
        augment: branch
        path: ve-pkg
        description: copy src tree here from ve.dependencies + ve.repository for full installer for particular R version
    ve.lib:           # Required
        root: ve.output
        augment: version
        path: ve-lib
        description: install bin tree here from ve.repository (automatically suffix getRVersion())
    ve.src:           # Required
        root: ve.output
        augment: branch  # Previously "version"
        path: src
        description: build source packages here (plus tests, if requested)
    ve.runtime:       # Required
        root: ve.output
        augment: version
        path: runtime
        description: local runtime environment + base for installers
    ve.zipout:        # Required
        root: ve.output
        augment: branch
        path: installers
        description: location for installers for various R versions
    ve.docs:          # Required
        root: ve.output
        augment: branch
        path: docs
        description: install framework and module docs here
    ve.external:      # Where to clone Github repository (default ve.root+external+RepoName)
        root: ve.root
        augment: root
        path: external
