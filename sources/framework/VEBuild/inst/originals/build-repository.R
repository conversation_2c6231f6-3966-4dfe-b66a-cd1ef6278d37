#!/bin/env Rscript

# Author: <PERSON>

# This script downloads required R packages from CRAN and BioConductor into the
# local pkg-repository. It will only download formats required to build a runtime
# binary on the current architecture (the one on which the build is running)

# Load runtime configuration
if ( ! exists("ve.installer" ) ) ve.installer <- getwd()
source(file.path(ve.installer,"scripts","get-runtime-config.R"))

# uncomment the following line on Windows if you just want the pre-compiled
# binaries otherwise, if RTools is installed the newer sources packages will be
# compiled.  You should allow compilation to happen if there is discrepancy in
# behavior between a Windows installation and a source (e.g. Linux/Docker)
# installation
options(install.packages.compile.from.source="never")

# Load required libraries
if ( ! suppressWarnings(require("miniCRAN",quietly=TRUE)) ) {
  install.packages("miniCRAN", lib=dev.lib, repos=CRAN.mirror, dependencies=NA, type=.Platform$pkgType)
}

require(tools)

message("========== GATHER DEPENDENCIES (CRAN / BioConductor) ==========")

# BioConductor setup
# if ( ! require("BiocManager") ) {
#     install.packages("BiocManager", repos=CRAN.mirror)
# }
# bioc <- BiocManager::repositories()
bioc <- BioC.mirror

if ( ! exists("pkgs.CRAN") || ! exists("pkgs.BioC") || ! exists("pkgs.db") ) {
  stop("Please run build-config.R to build dependency lists")
}

# Base R packages (so we can ignore those as dependencies)
base.lib <- dirname(find.package("MASS")) # looking for recommended packages; picking one that is required
pkgs.BaseR <- as.vector(installed.packages(lib.loc=base.lib, priority=c("base", "recommended"))[,"Package"])

# This script will only generate the packages needed for a binary installation in the
# current architecture.

# A couple of helper functions
# havePackages: check for presence of basic repository structure
# findMissingPackages: list packages not present in a particular sub-repository

havePackages <- function(repos=ve.dependencies,repo.type=ve.build.type) {
  # Determine if pkg-repository is well-formed
  #
  # repos is the destination directory for dependency packages
  #   Parameterize since BioConductor will go to ve.pkg, not ve.dependencies
  # Returns:
  #   TRUE/FALSE depending on existence of pkg-repository file tree
  #
  # If the tree is there, don't need to build the miniCRAN from scratch
  bin.contrib <- contrib.url(repos, type=repo.type)
  got.bin <- FALSE
  if ( dir.exists(bin.contrib) ) {
    if ( ! file.exists(file.path(bin.contrib, "PACKAGES")) ) {
      cat("Updating VE repository ",repo.type," PACKAGES files\n")
      got.bin <- (write_PACKAGES(bin.contrib, type=repo.type)>0)
    } else {
      got.bin <- TRUE
    }
  }
  return( got.bin )
}

findMissingPackages <- function( required.packages, repos=ve.deps.url, repo.type=ve.build.type ) {
  # Determine if any packages are missing from the pkg-repository
  # compared to the required.packages passed in.
  #
  # Args:
  #   required.packages: a character vector containing names of packages
  #                      we hope to find in pkg-repository
  #
  # Returns:

  #   A character vector of package names that are missing from the
  #   ve.build.type section of the pkg-repository compared to the
  #   required.packages
  
  apb <- available.packages(repos=repos, type=repo.type)
  return( setdiff( required.packages, apb[,"Package"]) )
}

# cat("Computing dependencies.\n")
pkgs.CRAN.lst <- pkgs.db$Package[pkgs.CRAN]
pkgs.CRAN.lst <- setdiff(pkgs.CRAN.lst, pkgs.BaseR) # don't search for base package dependencies
pkgs.CRAN.all <- pkgs.CRAN.lst <- miniCRAN::pkgDep( pkgs.CRAN.lst, repos=CRAN.mirror, suggests=FALSE)
pkgs.CRAN.lst <- setdiff(pkgs.CRAN.lst, pkgs.BaseR) # don't keep base packages
# cat("pkgs.CRAN.all\n")
# print(sort(pkgs.CRAN.all))

# Note that this scheme may fail if any of the BioConductor packages have CRAN dependencies
# For now (2025-02-04) it's working fine.
pkgs.BioC.lst <- pkgs.db$Package[pkgs.BioC]
pkgs.BioC.all <- pkgs.BioC.lst <- miniCRAN::pkgDep( pkgs.BioC.lst, repos=bioc, suggests=FALSE)
pkgs.BioC.lst <- setdiff( pkgs.BioC.lst, pkgs.CRAN.lst ) # Possible risk here: don't double-install packages
# cat("pkgs.BioC.all\n")
# print(sort(pkgs.BioC.all))

stated.dependencies <- as.character(c(pkgs.CRAN.lst, pkgs.BioC.lst))
all.dependencies <- setdiff(as.character(c(pkgs.CRAN.all, pkgs.BioC.all)),pkgs.BaseR)
save(stated.dependencies, all.dependencies, file=ve.all.dependencies)

cat("Repository location:",ve.dependencies,"\n")
# Attempt a minimal build of the repository (adding just new packages if we already have the whole thing)
# We won't attempt to delete - cleanup just by rebuilding when cruft gets to be too much.
if ( havePackages() ) {
  pkgs.missing.CRAN <- findMissingPackages(pkgs.CRAN.lst)
  up.to.date = TRUE
  if ( any(sapply(pkgs.missing.CRAN, length)) > 0 ) {
    if ( length(pkgs.missing.CRAN ) > 0 ) {
      up.to.date = FALSE
      cat("Updating VE dependency repository to add from CRAN:\n")
      print(pkgs.missing.CRAN)
      miniCRAN::addPackage(pkgs.missing.CRAN, path=ve.dependencies, repos=CRAN.mirror, type=ve.build.type, deps=FALSE)
    }
  }
  if ( up.to.date ) {
    cat("VE",ve.build.type,"dependency repository up to date with CRAN\n")
  } else {
    cat("Updating CRAN",ve.build.type,"dependency packages...\n")
    miniCRAN::updatePackages(path=ve.dependencies, repos=CRAN.mirror, type=ve.build.type, oldPkgs=pkgs.CRAN.lst, ask=FALSE)
  }
}

# Process BioC dependencies (stash top-level BioConductor packages in VE Package repository)
# Note that we don't always get source for standard dependencies. We need them here in order
# to have a complete VE package repository (VE packages are delivered in source and binary forms).
# We put rhdf5 and helpers into that repository to avoid a dependency on BioConductor in the
# bootstrapper. HDF5 is a bit of a pain to maintain...
for ( repo.type in unique(c(ve.build.type,"source")) ) {
  # Make sure there's a source repository to receive BioC source packages
  src.contrib <- contrib.url(ve.repository, repo.type)
  if ( ! dir.exists(src.contrib) ) {
    dir.create( src.contrib, recursive=TRUE, showWarnings=FALSE )
  }
  if ( havePackages(repos=ve.repository,repo.type=repo.type) ) {
    pkgs.missing.BioC <- findMissingPackages(pkgs.BioC.lst,repos=ve.repo.url,repo.type=repo.type) # store BioC packages in ve-pkg, not pkg-dependencies
    if ( any(sapply(pkgs.missing.BioC, length)) > 0 ) {
      if ( length(pkgs.missing.BioC) > 0 ) {
        up.to.date = FALSE
        cat("Updating VE package repository to add from BioConductor:\n")
        print(pkgs.missing.BioC)
        miniCRAN::addPackage(pkgs.missing.BioC, path=ve.repository, repos=bioc, type=repo.type, deps=FALSE)
      }
    }
    if ( up.to.date ) {
      cat("VE",repo.type,"package repository up to date with BioConductor\n")
    } else {
      cat("Updating BioConductor",repo.type,"dependency packages...\n")
      ignore <- miniCRAN::updatePackages(path=ve.repository, repos=bioc, type=repo.type, oldPkgs=pkgs.BioC.lst, ask=FALSE)
    }
  } else {
    cat("Building VE",repo.type,"repository from scratch from BioConductor packages\n")
    miniCRAN::makeRepo(pkgs.BioC.lst, path=ve.repository, repos=bioc, type=repo.type, deps=FALSE)

    cat("Adding",repo.type,"BioConductor packages to new VE repository\n")
    # BioConductor depends on some CRAN packages - no need to download those twice, so deps=FALSE
    miniCRAN::addPackage(pkgs.BioC.lst, path=ve.repository, repos=bioc, type=repo.type, deps=FALSE)
  }
}

# Verify the VE Repository with the following independent cross-check of dependencies

# pkgs.VE <- c(pkgs.CRAN.lst, pkgs.BioC.lst)
# ap <- available.packages(repos=ve.deps.url)
# getDependencies <- function(x) {
#   # Used in apply below to avoid a painfully long one-liner
#   # Extracts package names from a standard list of R dependencies
#   strsplit(split=", [ \n]*", paste( (y<-x[c("Package", "Depends", "Imports", "Extends", "LinkingTo")])[!is.na(y)], collapse=", "))
# }
# pkg <- sort(unique(unlist(apply(ap, 1, getDependencies))))
# pkg <- unique(sub("( |\\n)*\\(.*\\)", "", pkg))
# pkg <- setdiff(pkg, c(pkgs.BaseR, "R")) # Kill the BaseR packages from the list of dependencies, as well as dependency on R itself
# if ( length(setdiff(pkgs.VE, pkg)) > 0 ) {
#   cat("Discrepancy:\n")
#   print(setdiff(pkgs.VE, pkg))
# } else if (length(setdiff(pkg, pkgs.VE)) > 0 ) {
#   cat("Discrepancy:\n")
#   print(setdiff(pkg, pkgs.VE))
# } else {
#   cat("VE repository contents are complete\n")
# }
