Notes:
  - Some static scenarios implemented as ModelStages
  - Stage directory goes onto the InputPath
  - Dir element of each stage is optional (if directory has name of stage)
  - Better to make explicit in order to document that input files are being changed
  - If stage has no Dir and Dir does not exist, it's no error and stage gets only the StartFrom inputs
  - Notice from the example stages that only inputs that deviate from the StartFrom stage need to be
    included. It is bad practice to include an unmodified file in the scenario ModelStages inputs
  - ScenarioCategories / ScenarioElements are auto-generated for the visualizer - a single Category,
    with one level mapping directly to each ModelStage
  - StartFrom stage provides InputPath and Datastore (it is usually the Base Year stage)
  - BaseScenario just provides InputPath (and it is the "null case" for Category Scenarios)

StartFrom    : "stage-pop-base"       # Provides data elements
BaseScenario : "stage-pop-future"     # Provides InputPath (optional: if not present use StartFrom or BaseModel)

ModelStages:
  "Design-1":
     # Inherit year, base year, etc plus the model scripts from stage-pop-future
     Scenario: Design Level 1
     Description: All the Design Level 1 input adjustments
     Dir: Design-Level-1
  "Design-2":
     Scenario: Design Level 2
     Description: All the Design Level 2 input adjustments
     Dir: Design-Level-2
  "Design-3":
     Scenario: Design Level 3
     Description: All the Design Level 3 input adjustments
     Dir: Design-Level-3
  "Pricing-1":
     Scenario: Pricing Level 1
     Description: All the Pricing Level 1 input adjustments
     Dir: Pricing-Level-1
  "Pricing-2":
     Scenario: Pricing Level 2
     Description: All the Pricing Level 2 input adjustments
     Dir: Pricing-Level-2
