# Define a single object (list), not nessarily named PMSpecifications
# that contains a series of lists that are specifications

# This file contains an extensive list of example measures. You can make
# new measures by copying and editing any of the existing ones. Be careful
# to give each Measure a unique name.

# Here's what a Specification consists of:
# It's a list with named elements, developed from the following list:
#
# REQUIRED:
# "Name" - the name for the measure in the spreadsheet.
#          Be careful to give each measure a unique name.
# "Units" - the OUTPUT Units in which the measure is expressed (can be converted/derived)
# "Description" - the description of this measure (what it intends to do...)
#    In the examples below, Units and Description come at the end. It doesn't
#    matter what order they are in, as long as they are at the right "list level"
#    i.e. there's also a "Units" in the Summarize spec, which are the INPUT units
#
# EXACTLY ONE OF
# EITHER
#    "Function" - a string containing an R expression working on measures that were
#    previously specified.
# OR
#    "Summarize" - a list that specifies a call to summarizeDatasets; see below.
#
# The Summarize specification is a list with these named elements (see the docs
# for summarizeDatastore for more information
#
#  REQUIRED:
#    "Expr" - the summarizeDatasets expressiom (q.v.)
#    "Units" - for each Dataset name referred to in Expr or By, the (possibly converted)
#           units of the corresponding dataset.
#    "By" - a vector of Datasets along which to break out the Measure. Usually just "Marea".
#           If doing Breaks, a vector of two, with the first one being the sub-breaks (e.g.
#           Income).
#    "Table" - a vector of one or more tables that will be joined; If Table has more than one
#           entry, you must specify "Key" to say how they are joined.
#  OPTIONAL
#    "Breaks" - used to turn a numeric Dataset (field) into categories
#    "BreakNames" (must also have "Breaks", otherwise ignored) - Nice names to append to the
#           MeasureName for the categories
#    "Key" (if Table contains more than one Table) - the Dataset or field in all listed Tables
#           that will be used to join them.
#
# There are examples of all of these below
#
# Note that VEReports::summarizeDatasets has additional parameters (e.g. Group and QueryPrep_ls) that are
# automatically supplied by the script. You can put them here, but they will be ignored.

# It doesn't matter what name you use for the variable you put the list in, so long as this file
# contains exactly one such object definition.
# So it could also be something like "MyMareaSpecifications <- list("

QuerySpec <- list(

  #### DVMT
  #Urban area household DVMT
  #--------------------
  list(
    Name = "UrbanHhDvmt",
    Summarize = list(
      Expr = "sum(UrbanHhDvmt)",
      Units = c(
        UrbanHhDvmt = "MI/DAY",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Miles per day",
    Description = "Daily vehicle miles traveled by households residing in the urban area"
  ), # every specification (sub-list) has to have a comma after it, except the last one


# EXAMPLE: the Expr can look at specific subsets when generating the metric

#   #Urban area household DVMT in RVMPO county
#   list(
#     Name = "UrbanHhDvmtAz",
#     Summarize = list(
#       Expr = "sum(Dvmt[Azone == 'RVMPO' & LocType == 'Urban'] )",
#       Units = c(
#         Dvmt = "MI/DAY",
#         LocType = "",
#         Azone = "",
#         Marea = ""
#       ),
#       By = "Marea",
#       Table = "Household"
#     ),
#     Units = "Miles per day",
#     Description = "Daily vehicle miles traveled by households residing in the urban area in RVMPO county"
#   ),

  #Urban area household DVMT in mix used
  list(
    Name = "UrbanHhDvmt_MixNbrhd",
    Summarize = list(
      Expr = "sum(Dvmt[LocType == 'Urban' & IsUrbanMixNbrhd == '1'])",
      Units = c(
        Dvmt = "MI/DAY",
        LocType = "",
        IsUrbanMixNbrhd = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "Miles per day",
    Description = "Daily vehicle miles traveled by households residing in mixed use in the urban area"
  ),

  #Urban area public transit 'van' DVMT
  #-------------------------------
  list(
    Name = "UrbanVanDvmt",
    Summarize = list(
      Expr = "sum(VanDvmt)",
      Units = c(
        VanDvmt = "MI/DAY",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Miles per day",
    Description = "Daily vehicle miles traveled by on-demand transit vans in the Urban area."
  ),

  #Urban area commercial service vehicle DVMT
  #-------------------------------------
  list(
    Name = "UrbanComSvcDvmt",
    Summarize = list(
      Expr = "sum(ComSvcUrbanDvmt)",
      Units = c(
        ComSvcUrbanDvmt = "MI/DAY",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Miles per day",
    Description = "Commercial service vehicle daily vehicle miles traveled attributable to the demand of households and businesses located in the urban area"
  ),

  #Urban area light-duty vehicle DVMT
  list(
    Name = "UrbanLdvDvmt",
    Function = "UrbanHhDvmt + UrbanVanDvmt + UrbanComSvcDvmt",
    Units = "Miles per day",
    Description = "Sum of daily vehicle miles traveled by households residing in the urban area, commercial service travel attributable to the demand of urban area households and businesses, and on-demand transit van travel in the urban area."
  ),

  #Population residing in "Urban" LocType
  #-------------------
  list(
    Name = "UrbanHhPop",
    Summarize = list(
      Expr = "sum(HhSize[LocType == 'Urban'])",
      Units = c(
        HhSize = "",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "Persons",
    Description = "Number of persons residing in urban area location type"
  ),

  #Urban Population, broken out by income
  #-------------------
  list(
    Name = "UrbanHhPopLowInc",
    Summarize = list(
      Expr = "sum(HhSize[LocType == 'Urban'])",
      Units = c(
        HhSize = "",
        LocType = "",
        Income = "USD",
        Marea = ""
      ),
      By = c(
        "Income",
        "Marea"),
      Breaks = list(
        Income = c(20000, 40000, 60000, 80000, 100000)
      ),
      BreakNames = list(
        Income = c("20000", "40000", "60000", "80000", "100000")
      ),
      Table = "Household"
    ),
    Units = "Persons",
    Description = "Number of persons in urban location by income strata"
  ),

  #DVMT per Capita in Marea
  list(
    Name = "UrbanLdvDmvtPerCap",
    Function = "UrbanLdvDvmt / UrbanHhPop",
    Units = "Dvmt per person",
    Description = "daily vehicle miles traveled per person residing in the urban location."
  ),

  #Urban area Bus DVMT
  #-------------------------------
  list(
    Name = "UrbanBusDvmt",
    Summarize = list(
      Expr = "sum(BusFwyDvmt)+ sum(BusArtDvmt) + sum(BusOthDvmt)",
      Units = c(
        BusFwyDvmt = "MI/DAY",
        BusArtDvmt = "MI/DAY",
        BusOthDvmt = "MI/DAY",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Miles per day",
    Description = "Daily vehicle miles traveled by Bus in the Urban area."
  ),

  #Urban area Hvy Trk DVMT
  #-------------------------------
  list(
    Name = "UrbanHvyTrkDvmt",
    Summarize = list(
      Expr = "sum(HvyTrkFwyDvmt)+ sum(HvyTrkArtDvmt) + sum(HvyTrkOthDvmt)",
      Units = c(
        HvyTrkFwyDvmt = "MI/DAY",
        HvyTrkArtDvmt = "MI/DAY",
        HvyTrkOthDvmt = "MI/DAY",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Miles per day",
    Description = "Daily vehicle miles traveled by Heavy Truck in the Urban area."
  ),

  #Urban area Rail DVMT
  #-------------------------------
  list(
    Name = "UrbanRailDvmt",
    Summarize = list(
      Expr = "sum(RailDvmt)",
      Units = c(
        RailDvmt = "MI/DAY",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Miles per day",
    Description = "Daily vehicle miles traveled by Rail in the Urban area."
  ),

  #############################################################################################################################################################################################################################################################################   
  ###Annual Fuel Use
  #Household fuel consumption for Urban
  #------------------------------------
  list(
    Name = "UrbanHhGGE",
    Summarize = list(
      Expr = "sum(DailyGGE[LocType == 'Urban'])",
      Units = c(
        DailyGGE = "GGE/DAY",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "Gas gallon equivalents per day",
    Description = "Average daily fuel consumption for the travel of households residing in the Urban"
  ),

  #Commercial service fuel consumption for Urban
  #---------------------------------------------
  list(
    Name = "UrbanComSvcGGE",
    Summarize = list(
      Expr = "sum(ComSvcUrbanGGE )",
      Units = c(
        ComSvcUrbanGGE = "GGE/DAY",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Gas gallon equivalents per day",
    Description = "Average daily fuel consumption for commercial services vehicle travel arising from households and businesses located in the Urban"
  ),

  #Public transit van fuel consumption for Urban area
  #---------------------------------------------
  list(
    Name = "UrbanVanGGE",
    Summarize = list(
      Expr = "sum(VanGGE)",
      Units = c(
        VanGGE = "GGE/DAY",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Gas gallon equivalents per day",
    Description = "Average daily fuel consumption for public transit van in the urban area"
  ),

  #Light-duty vehicle fuel consumption for urban area
  #---------------------------------------------
  list(
    Name = "UrbanLdvGGE",
    Function = "UrbanHhGGE + UrbanComSvcGGE + UrbanVanGGE",
    Units = "Gas gallon equivalents per day",
    Description = "Average daily fuel consumption for light-duty vehicle travel attributable to households and businesses in the urban area"
  ),

  #Bus fuel consumption for urban area
  #---------------------------------------------
  list(
    Name = "UrbanBusGGE",
    Summarize = list(
      Expr = "sum(BusGGE)",
      Units = c(
        BusGGE = "GGE/DAY",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Gas gallon equivalents per day",
    Description = "Average daily fuel consumption for Bus in the urban area"
  ),

  #Rail fuel consumption for urban area
  #---------------------------------------------
  list(
    Name = "UrbanRailGGE",
    Summarize = list(
      Expr = "sum(RailGGE)",
      Units = c(
        RailGGE = "GGE/DAY",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Gas gallon equivalents per day",
    Description = "Average daily fuel consumption for Rail in the urban area"
  ),

  #Heavy truck fuel consumption for Urban area
  #---------------------------------------------
  list(
    Name = "UrbanHvyTrkGGE",
    Summarize = list(
      Expr = "sum(HvyTrkUrbanGGE)",
      Units = c(
        HvyTrkUrbanGGE = "GGE/DAY",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Gas gallon equivalents per day",
    Description = "Average daily fuel consumption for heavy truck in the urban area"
  ),

  #####################################################################################################################################################################################################################################################################    

  ### Household vehicle ownership
  #Number of households in urban area
  #--------------------------------------
  list(
    Name = "UrbanHhNum",
    Summarize = list(
      Expr = "count(HhSize[LocType == 'Urban'])",
      Units = c(
        HhSize = "",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "Households",
    Description = "Number of households residing in urban area"
  ),

  #Household Number of vehicles in urban area
  #------------------
  list(
    Name = "UrbanHhVehicles",
    Summarize = list(
      Expr = "sum(NumAuto[LocType == 'Urban']) + sum(NumLtTrk[LocType == 'Urban'])",
      Units = c(
        NumAuto = "VEH",
        NumLtTrk = "VEH",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "Household light-duty vehicles",
    Description = "Total number of light-duty vehicles owned/leased by households residing in urban area"
  ),

  #Average number of vehicles per household in urban area
  #----------------------------------------
  list(
    Name = "UrbanHhAveVehPerHh",
    Function = "UrbanHhVehicles / UrbanHhNum",
    Units = "Household light-duty vehicles per household",
    Description = "Average number of light-duty vehicles owned/leased by households residing in urban area"
  ),

  #Number of workers in urban
  #--------------------------
  list(
    Name = "UrbanHhWorkers",
    Summarize = list(
      Expr = "sum(Workers[LocType == 'Urban'])",
      Units = c(
        Workers = "PRSN",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "Workers",
    Description = "Number of workers residing in urban"
  ),


  #Number of drivers in urban
  #--------------------------
  list(
    Name = "UrbanHhDrivers",
    Summarize = list(
      Expr = "sum(Drivers[LocType == 'Urban'])",
      Units = c(
        Drivers = "PRSN",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "Drivers",
    Description = "Number of drivers residing in urban"
  ),

  #Number of households in urban-mixed neighborhoods
  #-------------------------------------------------
  list(
    Name = "NumUrbanMixHh",
    RequireNot = c(Dataset="LocType",Table="Bzone"),
    Summarize = list(
      Expr = "sum(IsUrbanMixNbrhd)",
      Units = c(
        IsUrbanMixNbrhd = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "Households",
    Description = "Number of households residing in urban-mixed neighborhoods in urbanized area"
  ),

  ########################################################################################################################################################################################################################################################################    
  ### CO2
  #Household CO2e for urban area
  #------------------------
  list(
    Name = "UrbanHhCO2e",
    Summarize = list(
      Expr = "sum(DailyCO2e[LocType == 'Urban'])",
      Units = c(
        DailyCO2e = "MT/YR",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "Metric tons CO2e per year",
    Description = "Average annual production of greenhouse gas emissions from light-duty vehicle travel by households residing in the urban area"
  ),

  #Commercial service CO2e for urban area
  #---------------------------------
  list(
    Name = "UrbanComSvcCO2e",
    Summarize = list(
      Expr = "sum(ComSvcUrbanCO2e)",
      Units = c(
        ComSvcUrbanCO2e = "MT/YR",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Metric tons CO2e per year",
    Description = "Average annual production of greenhouse gas emissions from commercial service light-duty vehicle travel attributable to households and businesses in the urban area"
  ),

  #Van CO2e for urban area
  #------------------
  list(
    Name = "UrbanVanCO2e",
    Summarize = list(
      Expr = "sum(VanCO2e)",
      Units = c(
        VanCO2e = "MT/YR",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Metric tons CO2e per year",
    Description = "Average annual production of greenhouse gas emissions from public transit van travel in the urban area"
  ),

  #Light-duty vehicle CO2e for urban area
  #---------------------------------
  list(
    Name = "UrbanLdvCO2e",
    Function = "UrbanHhCO2e + UrbanVanCO2e + UrbanComSvcCO2e",
    Units = "Metric tons CO2e per year",
    Description = "Average annual production of greenhouse gas emissions from light-duty vehicle travel of households and businesses in the urban area"
  ),

  #Light-duty vehicle CO2e Rate for urban area
  #---------------------------
  list(
    Name = "UrbanLdvCO2eRate",
    Function = "UrbanLdvCO2e / (UrbanLdvDvmt * 365)",
    Units = "Grams CO2e per mile",
    Description = "Average greenhouse gas emissions per mile of light duty vehicle travel in the urban area"
  ),

  #Bus CO2e for urban area
  #---------------------------
  list(
    Name = "UrbanBusCO2e",
    Summarize = list(
      Expr = "sum(BusCO2e)",
      Units = c(
        BusCO2e = "MT/YR",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Metric tons CO2e per year",
    Description = "Average annual production of greenhouse gas emissions from public transit bus travel in the urban area"
  ),

  #Rail CO2e for urban area
  #---------------------------
  list(
    Name = "UrbanRailCO2e",
    Summarize = list(
      Expr = "sum(RailCO2e)",
      Units = c(
        RailCO2e = "MT/YR",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Metric tons CO2e per year",
    Description = "Average annual production of greenhouse gas emissions from Rail travel in the urban area"
  ),

  #Bus CO2e Rate for urban area
  #---------------------------
  list(
    Name = "UrbanBusCO2eRate",
    Function = "(UrbanBusCO2e * 1000000) / (UrbanBusDvmt * 365)",
    Units = "grams CO2e per mile",
    Description = "Average greenhouse gas emissions per mile of public transit bus travel in the urban area"
  ),

  #Heavy Truck CO2e for Urban
  #---------------------------
  list(
    Name = "UrbanHvyTrkCO2e",
    Summarize = list(
      Expr = "sum(HvyTrkUrbanCO2e)",
      Units = c(
        HvyTrkUrbanCO2e = "MT/YR",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Metric tons CO2e per year",
    Description = "Average annual production of greenhouse gas emissions from heavy truck travel in the urban area"
  ),

  #Heavy Truck CO2e Rate for urban area
  #---------------------------
  list(
    Name = "UrbanHvyTrkAveCO2eRate",
    Function = "(UrbanHvyTrkCO2e * 1000000) / (UrbanHvyTrkDvmt * 365)",
    Units = "Grams CO2e per mile",
    Description = "Average greenhouse gas emissions per mile of heavy truck travel in the urban area"
  ),

  #################################################################################################################################################################################################################################################################    
  ###Trips, Delay, Speed and Mode shift 
  #Walk Trips in Urban area          
  list(
    Name = "UrbanWalkTrips",
    Summarize = list(
      Expr = "sum(WalkTrips[LocType == 'Urban'])",
      Units = c(
        WalkTrips = "TRIP/DAY",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "Trips per Day",
    Description = "Average number walk trips per day in urban area"
  ),

  #Bike Trips in Urban area          
  list(
    Name = "UrbanBikeTrips",
    Summarize = list(
      Expr = "sum(BikeTrips[LocType == 'Urban'])",
      Units = c(
        BikeTrips = "TRIP/DAY",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "Trips per Day",
    Description = "Average number bike trips per day in urban area"
  ),

  #Transit Trips in Urban area          
  list(
    Name = "UrbanTransitTrips",
    Summarize = list(
      Expr = "sum(TransitTrips[LocType == 'Urban'])",
      Units = c(
        TransitTrips = "TRIP/DAY",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "Trips per Day",
    Description = "Average number transit trips per day in urban area"
  ),

  #Mode shift Trips in urban area
  #------------------
  list(
    Name = "UrbanModeShiftTrips",
    Function = "UrbanWalkTrips+UrbanBikeTrips+UrbanTransitTrips",
    Units = "Trips per Day",
    Description = "Average number mode shift trips (Bike, Walk, & Transit) per day in urban area"
  ),

  ###Hours of congestion (total delay of ldv and hvy trk)
  #total delay of ldv in urban area
  #---------------------------
  list(
    Name = "UrbanLdv_TotalDelay",
    Summarize = list(
      Expr = "sum(LdvTotDelay)",
      Units = c(
        LdvTotDelay = "HR",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Hours",
    Description = "Total light-duty vehicle delay (hours per day) on urban area roads"
  ),

  #total delay of hvy trk in urban area
  #---------------------------
  list(
    Name = "UrbanHvyTrk_TotalDelay",
    Summarize = list(
      Expr = "sum(HvyTrkTotDelay)",
      Units = c(
        HvyTrkTotDelay = "HR",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Hours",
    Description = "Total heavy truck vehicle delay (hours per day) on urban area roads"
  ),

  #total delay of ldv and hvy trk in urban area
  #---------------------------
  list(
    Name = "UrbanLdv_HvyTrk_TotalDelay",
    Function = "UrbanLdv_TotalDelay + UrbanHvyTrk_TotalDelay",
    Units = "Hours",
    Description = "Total light duty vehicle and heavy truck delay (hours per day) on the urban area roads"
  ),

  #Light duty vehicle speed in urban area
  #---------------------------
  list(
    Name = "UrbanLvdAveSp",
    Summarize = list(
      Expr = "mean(LdvAveSpeed)",
      Units = c(
        LdvAveSpeed = "MI/HR",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Miles per Hour",
    Description = "Average speed (miles per hour) of light-duty vehicle travel on urban area roads"
  ),

  #Heavy truck speed in urban area
  #---------------------------
  list(
    Name = "UrbanHvyTrkAveSp",
    Summarize = list(
      Expr = "mean(HvyTrkAveSpeed)",
      Units = c(
        HvyTrkAveSpeed = "MI/HR",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Miles per Hour",
    Description = "Average speed (miles per hour) of heavy truck travel on urban area roads"
  ),

  #Bus speed in urban area
  #---------------------------
  list(
    Name = "UrbanBusAveSp",
    Summarize = list(
      Expr = "mean(BusAveSpeed)",
      Units = c(
        BusAveSpeed = "MI/HR",
        Marea = ""
      ),
      By = "Marea",
      Table = "Marea"
    ),
    Units = "Miles per Hour",
    Description = "Average speed (miles per hour) of bus travel on urban area roads"
  ),

  ###########################################################################################################################################################################################################################################################################     
  ###Household Transportation Costs as Percentage of Income      
  #Vehicle ownership cost in urban area
  #------------------
  list(
    Name = "UrbanVehOwnershipCost",
    Summarize = list(
      Expr = "mean(OwnCost[LocType == 'Urban'])",
      Units = c(
        OwnCost = "USD",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "USD per year",
    Description = "Average annual household vehicle ownership cost (depreciation, finance, insurance, taxes) in urban area"
  ),

  #Vehicle average out-of-pocket cost in dollars per mile of vehicle travel in urban area
  #------------------
  list(
    Name = "UrbanAveVehCostPM",
    Summarize = list(
      Expr = "mean(AveVehCostPM[LocType == 'Urban'])",
      Units = c(
        AveVehCostPM = "USD",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "USD",
    Description = " Average out-of-pocket cost in dollars per mile of vehicle travel in urban area"
  ),

  #Vehicle Operating cost in urban area
  #------------------
  list(
    Name = "UrbanVehOperatingCost",
    Summarize = list(
      Expr = "mean(AveVehCostPM[LocType == 'Urban']) * mean(Dvmt[LocType == 'Urban'])*365",
      Units = c(
        AveVehCostPM = "USD",
        Dvmt = "MI/DAY",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "USD per year",
    Description = " Average annual household vehicle operating cost in urban area"
  ),

  #Total annual household income in urban area
  #------------------
  list(
    Name = "UrbanTotalHhIncome",
    Summarize = list(
      Expr = "sum(Income[LocType == 'Urban'])",
      Units = c(
        Income = "USD",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "USD per year",
    Description = " Total annual household income in urban area"
  ),

  #Average annual household income in urban area
  #---------------------------------
  list(
    Name = "UrbanAveHhIncome",
    Function = "UrbanTotalHhIncome / UrbanHhNum",
    Units = "USD per year",
    Description = "Average annual household income in the urban area"
  ),

  #Total annual low household income in urban area
  #------------------
  list(
    Name = "UrbanTotalHhIncomeLowInc",
    Summarize = list(
      Expr = "sum(Income[LocType == 'Urban'])",
      Units = c(
        Income = "USD",
        LocType = "",
        Marea = ""
      ),
      By = c(
        "Income",
        "Marea"
      ),
      Breaks = list(
        Income = c(20000,40000,60000,80000,100000)
      ),   
      BreakNames = list(
        Income = c("20000", "40000", "60000", "80000", "100000")
      ),
      Table = "Household"
    ),
    Units = "USD per year",
    Description = " Total annual low household income (0to20K 2010$) in urban area"
  ),

  #Number of low income households in urban area
  #--------------------------------------
  list(
    Name = "UrbanHhNumLowInc",
    Summarize = list(
      Expr = "count(HhSize[LocType == 'Urban'])",
      Units = c(
        HhSize = "",
        LocType = "",
        Income = "USD",
        Marea = ""
      ),
      By = c(
        "Income",
        "Marea"
      ),
      Breaks = list(
        Income = c(20000, 40000, 60000, 80000, 100000)
      ),    
      BreakNames = list(
        Income = c("20000", "40000", "60000", "80000", "100000")
      ),
      Table = "Household"
    ),
    Units = "Households",
    Description = "Number of low income (0to20K 2010$) households residing in urban area"
  ),

  #Average annual low household income in urban area
  #---------------------------------
  list(
    Name = "UrbanAveHhIncomeLowInc",
    Function = "UrbanTotalHhIncomeLowInc / UrbanHhNumLowInc",
    Units = "USD per year",
    Description = "Average annual low household income (0to20K 2010$) in the urban area"
  ),

  #Average annual household DVMT in urban area
  #---------------------------------
  list(
    Name = "UrbanAveHhDVMT",
    Function = "UrbanHhDvmt / UrbanHhNum",
    Units = "Vehicle Mile Travel",
    Description = "Average household DVMT in urban area"
  ),

  # Average Household operating cost for low income - Inc0to20K
  list(
    Name = "UrbanVehOperatingCostLowInc",
    Summarize = list(
      Expr = "mean(AveVehCostPM[LocType == 'Urban'])* mean(Dvmt[LocType == 'Urban'])*365",
      Units = c(
        AveVehCostPM = "USD",
        LocType = "",
        Income = "USD",
        Dvmt = "MI/DAY",
        Marea = ""
      ),
      By = c(
        "Income",
        "Marea"
      ),
      Breaks = list(
        Income = c(20000,40000,60000,80000,100000)
      ),
      BreakNames = list(
        Income = c("20000", "40000", "60000", "80000", "100000")
      ),
      Table = "Household"
    ),
    Units = "USD per year",
    Description = "Average annual household vehicle operating cost for low income (0to20K 2010$) in urban area"
  ),

  # Average Household vehicle ownership cost for low income - Inc0to20K
  list(
    Name = "UrbanVehOwnershipCostLowInc",
    Summarize = list(
      Expr = "mean(OwnCost[LocType == 'Urban'])",
      Units = c(
        OwnCost = "USD",
        LocType = "",
        Income = "USD",
        Marea = ""
      ),
      By = c(
        "Income",
        "Marea"
      ),
      Breaks = list(
        Income = c(20000,40000,60000,80000,100000)
      ),
      BreakNames = list(
        Income = c("20000", "40000", "60000", "80000", "100000")
      ),
      Table = "Household"
    ),
    Units = "USD per year",
    Description = "Average annual household vehicle ownership cost for low income(0to20K 2010$) in urban area"
  ),

  #low income Hh Dvmt in urban area
  list(
    Name = "UrbanHhDvmtLowInc",
    Summarize = list(
      Expr = "sum(Dvmt[LocType == 'Urban'])",
      Units = c(
        Dvmt = "MI/DAY",
        LocType = "",
        Income = "USD",
        Marea = ""
      ),
      By = c(
        "Income",
        "Marea"
      ),
      Breaks = list(
        Income = c(20000,40000,60000,80000,100000)
      ),   
      BreakNames = list(
        Income = c("20000", "40000", "60000", "80000", "100000")
      ),
      Table = "Household"
    ),
    Units = "Miles per day",
    Description = "Daily vehicle miles traveled by low income (0to20K 2010$) households residing in the urban area"
  ),

  #Low income household vehicle Own & out-of-pocket costs share of HH low Income total (all low income HHs)
  list(
    Name = "HhVehTravCostShareLowInc",
    Function = "(UrbanVehOperatingCostLowInc  + UrbanVehOwnershipCostLowInc * UrbanHhNumLowInc)/ UrbanTotalHhIncomeLowInc",
    Units = "Proportion",
    Description = "Low income (0to20K 2010$) household vehicle Own & out-of-pocket costs share of HH low income total (all low income HHs)in urban area"
  ),

  #household vehicle Own & out-of-pocket costs share of HH income total (all HHs)
  list(
    Name = "HhVehTravCostShare",
    Function = "((UrbanVehOperatingCost + UrbanVehOwnershipCost)* UrbanHhNum) / UrbanTotalHhIncome",
    Units = "Proportion",
    Description = "Household vehicle Own & out-of-pocket costs share of HH income total (all HHs) in urban area"
  ),

  #urban DVMT per Capita in LowInc HHs
  list(
    Name = "UrbanLdvDmvtPerCapLowInc",
    Function = "UrbanHhDvmtLowInc / UrbanHhPopLowInc",
    Units = "Dvmt per person",
    Description = "daily vehicle miles traveled per person in low income (0to20K 2010$) households residing in the urban area."
  ),

  #Average car service light truck proportion of car service DVMT
  #--------------------------------------------------------------
  list(
    Name = "MareaCarSvcLtTrkDvmtProp",
    Summarize = list(
      Expr = "sum(DvmtProp[VehicleAccess != 'Own' & Type == 'LtTrk']) / sum(DvmtProp[VehicleAccess != 'Own'])",
      Units = c(
        DvmtProp = "",
        VehicleAccess = "",
        Type = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Vehicle"
    ),
    Units = "Proportion",
    Description = "Average proportion car service vehicle DVMT in light trucks used by households residing in the Marea"
  ),

  #Average population density
  #--------------------------
  list(
    Name = "UrbanAvePopDen",
    Summarize = list(
      Expr = "sum(UrbanPop) / sum(UrbanArea)",
      Units = c(
        UrbanArea = "ACRE",
        UrbanPop = "PRSN",
        Marea = ""
      ),
      By = "Marea",
      Table = "Bzone"
    ),
    Units = "Persons per acre",
    Description = "Average number of persons per acre in the urbanized area"
  ),

  #Median Bzone population density
  #-------------------------------
  list(
    Name = "MedianBzonePopDen",
    RequireNot = c(Dataset="LocType",Table="Bzone"),
    Summarize = list(
      Expr = "median(UrbanPop[UrbanArea!=0] / UrbanArea[UrbanArea!=0])",
      Units = c(
        UrbanPop = "PRSN",
        UrbanArea = "ACRE",
        Marea = ""
      ),
      By = "Marea",
      Table = "Bzone"
    ),
    Units = "Persons per acre",
    Description = "Median Bzone population density in urbanized area"
  ),

  #Average activity density
  #------------------------
  list(
    Name = "AveActivityDen",
    Require = c(Dataset="LocType",Table="Bzone"),
    Summarize = list(
      Expr = "sum(NumHh[LocType == 'Urban'] + TotEmp[LocType == 'Urban']) / sum(UrbanArea)",
      Units = c(
        NumHh = "HH",
        TotEmp = "PRSN",
        UrbanArea = "ACRE",
        LocType = "category",
        Marea = ""
      ),
      By = "Marea",
      Table = "Bzone"
    ),
    Units = "Households and jobs per acre",
    Description = "Average number of households and jobs per acre in the urbanized area"
  ),

  #Proportion of households in urban-mixed neighborhoods
  #-----------------------------------------------------
  list(
    Name = "PropUrbanMixHh",
    Function = "NumUrbanMixHh / UrbanHhNum",
    Units = "Proportion of Households",
    Description = "Proportion of urbanized area households that reside in urban-mixed neighborhoods"
  ),

  #Total daily work parking cost
  #-----------------------------
  list(
    Name = "HhTotDailyWkrParkingCost",
    Summarize = list(
      Expr = "sum(ParkingCost[LocType == 'Urban'])",
      Units = c(
        ParkingCost = "",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = list(
        Worker = c("ParkingCost"),
        Household = c("Marea", "LocType")
      ),
      Key = "HhId"
    ),
    Units = "USD per day",
    Description = "Total daily work parking expenditures by households living in the urbanized portion of the Marea"
  ),

  #Total daily non-work parking cost
  #---------------------------------
  list(
    Name = "HhTotDailyOthParkingCost",
    Summarize = list(
      Expr = "sum(OtherParkingCost[LocType == 'Urban'])",
      Units = c(
        OtherParkingCost = "",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "USD per day",
    Description = "Total daily non-work parking expenditures by households living in the urbanized portion of the Marea"
  ),

  #Proportion of single-family dwelling units
  #------------------------------------------
  list(
    Name = "PropSFDU",
    RequireNot = c(Dataset="LocType",Table="Bzone"),
    Summarize = list(
      Expr = "sum(SFDU) / (sum(NumHh))",
      Units = c(
        SFDU = "DU",
        NumHh = "HH",
        Marea = ""
      ),
      By = "Marea",
      Table = "Bzone"
    ),
    Units = "Proportion of Households",
    Description = "Proportion of urbanized area households that reside in single-family dwellings"
  ),

  #Vehicle trips in urban
  #---------------------------------
  list(
    Name = "UrbanVehicleTrips",
    Summarize = list(
      Expr = "sum(VehicleTrips[LocType == 'Urban'])",
      Units = c(
        VehicleTrips = "TRIP/DAY",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "Trips per day",
    Description = "Average number of vehicle trips per day by household members in urban"
  ),

  #Average Vehicle trip length in urban
  #---------------------------------
  list(
    Name = "UrbanVehTripLen",
    Summarize = list(
      Expr = "sum(AveVehTripLen[LocType == 'Urban'])",
      Units = c(
        AveVehTripLen = "MI",
        LocType = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Household"
    ),
    Units = "Miles",
    Description = "Average household vehicle trip length in miles in urban"
  ),

  #Average car service auto proportion of car service DVMT
  #--------------------------------------------------------------
  list(
    Name = "MareaCarSvcAutoDvmtProp",
    Summarize = list(
      Expr = " sum(DvmtProp[VehicleAccess != 'Own' & Type == 'Auto']) / sum(DvmtProp[VehicleAccess != 'Own'])",
      Units = c(
        DvmtProp = "",
        VehicleAccess = "",
        Type = "",
        Marea = ""
      ),
      By = "Marea",
      Table = "Vehicle"
    ),
    Units = "Proportion",
    Description = "Average proportion car service vehicle DVMT in autos used by households residing in the Marea"
  ),

  #Household car service DVMT in Marea
  list(
    Name = "MareaHouseholdCarSvcDvmt",
    Summarize = list(
      Expr = "sum(Dvmt[VehicleAccess != 'Own' ] * DvmtProp[VehicleAccess != 'Own' ])",
      Units = c(
        Dvmt = "MI/DAY",
        DvmtProp = "",
        VehicleAccess = "",
        Marea = ""
      ),
      By = "Marea",
      Table = list(
        Household = c("Dvmt", "Marea"),
        Vehicle = c("DvmtProp", "VehicleAccess")
      ),
      Key = "HhId"
    ),
    Units = "miles per day",
    Description = "Total DVMT in car service vehicles of persons in households and non-institutional group quarters in Marea"
  ) 
  # no comma after the last specification

)