# Variant is selected when installing
# Sample data is also installed
# Eventually need to document or provide helper function for reducing to template
variants:
  base:
    # "description" is used to echo back what is being installed
    description: VERSPM 3.0
    # Configuration is all in the model root directory
    config: visioneval-base.cnf
    scripts: scripts-base
    inputs: inputs-base
    defs: defs
    queries: queries
  classic: # no config, standard run_model.R script
    description: Classic VERSPM (run_model.R)
    scripts: scripts-classic
    inputs: inputs-base
    defs: defs-classic
    # queries: queries
  year:
    # inputs differ by stage
    # script is common to all years
    description: VERSPM 3.0 Staged by Year
    config: visioneval-year.cnf
    inputs: inputs-base # Some files are overridden in stages
    defs: defs
    scripts: scripts-base # uses standard VERSPM script
    queries: queries
  pop:
    # scripts, inputs and years all differ by stage
    description: VERSPM 3.0 Staged Population Synthesis
    scripts: scripts-pop
    config: visioneval-pop.cnf
    inputs: inputs-base # Some files are overridden in stages
    defs: defs
    queries: queries
    stages: # copied as they are
        - stage-pop-synth
        - stage-pop-base
        - stage-pop-future
  scenarios-ms:
    # Similar to pop but includes "scenarios" for ModelStages
    description: VERSPM 3.0 with ModelStage Scenarios
    scripts: scripts-pop
    config: visioneval-pop.cnf
    inputs: inputs-base # Some files are overridden in stages
    defs: defs
    queries: scenario-queries
    scenarios: scenarios-ms
    stages: # copied as they are
        - stage-pop-synth
        - stage-pop-base
        - stage-pop-future
  scenarios-cat:
    # Similar to pop but includes "scenarios" for Category/Scenario
    description: VERSPM 3.0 with Category Combination Scenarios
    scripts: scripts-pop
    config: visioneval-pop.cnf
    inputs: inputs-base # Some files are overridden in stages
    defs: defs
    queries: scenario-queries
    scenarios: scenarios-cat
    stages: # copied as they are
        - stage-pop-synth
        - stage-pop-base
        - stage-pop-future
  private:
    # illustrates private model configuration, but just reproduces base model
    private: true
    description: VERSPM 3.0 (private version)
    config: visioneval-base.cnf
    scripts: scripts-base
    inputs: inputs-base
    defs: defs
    queries: queries
