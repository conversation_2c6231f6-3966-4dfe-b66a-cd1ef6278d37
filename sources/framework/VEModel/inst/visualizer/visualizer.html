<!DOCTYPE html>
<html lang='en'>

<head>

    <meta charset='utf-8'>

    <title>Scenario Viewer</title>

    <!--
    LOAD REQUIRED JAVASCRIPT LIBRARIES
    -->
    <script src='js/d3.min.js'></script>
    <script src='js/crossfilter.min.js'></script>
    <script src='js/dc.min.js'></script>
    <script src='js/jquery-1.10.2.js'></script>
    <script src='js/bootstrap.min.js'></script>
    <script src='js/colorbrewer.js'></script>

    <!--
    SET STYLES
    <link href='css/bootstrap.css' rel='stylesheet' type='text/css' >
    -->
    <link href='css/bootstrap.min.css' rel='stylesheet' type='text/css'>
    <link href='css/dc.css' rel='stylesheet' type='text/css'>
    <link href='css/colorbrewer.css' rel='stylesheet' type='text/css'>
    <link href='css/style.css' rel='stylesheet' type='text/css'>
</head>

<body>

    <!--
    SET UP HEADING AND BUTTONS TO SHOW QUICK START AND DETAILED EXPLANATION BOOTSTRAP MODALS
    -->
    <div style="text-align:center">
        <h2>VisionEval Scenario Viewer</h2>
    </div>
    <div style="text-align:center">
        <button class="btn btn-primary btn-lg" data-toggle="modal" data-target="#about-modal">
        About
        </button>
        <button class="btn btn-primary btn-lg" data-toggle="modal" data-target="#quick-start-modal">
        Quick Start
        </button>
        <button class="btn btn-primary btn-lg" data-toggle="modal" data-target="#detail-modal">
        Detailed Instructions
        </button>
    </div>

    <!--
    ABOUT THIS EFFORT BOOTSTRAP MODAL
    -->
    <div class="modal fade" id="about-modal" tabindex="-1" role="dialog" aria-labelledby="about-label" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h3 class="modal-title" id="about-label">About This Effort</h3>
            </div>
            <div class="modal-body">
                <h3>About the Scenario Viewer</h3>
                <p>
                This web page enables you to explore the results of multiple VERPSM model runs for sensitivity testing purposes. With it, you can select different combinations of input factors and find out how various future outcomes are affected by your selection. You can also select desired outcomes and find out what factors cause those outcomes to occur. By experimenting with the selections and observing the results, you will get a better understanding of the relationships between policy choices and context factors and future outcomes.
                </p>
                <h3>Copyright, Attribution and Acknowledgements</h3>
                <p>
                Copyright 2016 Oregon Department of Transportation, 2018 AASHTO
                </p>
                <p>
                Licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0" target="_blank">Apache License, Version 2.0</a> (the "License"); you may not use this file except in compliance with the License.
                <p>Author(s): Brian Gregor (<EMAIL>), Ben Stabler (<EMAIL>), Aditya Gore
                   (<EMAIL>), Jeremy Raw (<EMAIL>).</p>
                <p>Many thanks go to the authors of the d3.js, dc.js, crossfilter.js, bootstrap.js, jquery.js, and colorbrewer.js libraries without which this data visualization would have been much more challenging to develop.</p>
             </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
            </div>
        </div>
    </div>

    <!--
    QUICK START BOOTSTRAP MODAL
    -->
    <div class="modal fade" id="quick-start-modal" tabindex="-1" role="dialog" aria-labelledby="quick-start-label" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h3 class="modal-title" id="quick-start-label">Scenario Viewer Quick Start Guide</h3>
            </div>
            <div class="modal-body">
              <div>
                Select different combinations of factors and find out how various future outcomes are affected by your selection. You can also select desired outcomes and find out what factors cause those outcomes to occur. By experimenting with the selections and observing the results, you will get a better understanding of the relationships between policy choices, context factors and future outcomes.
              </div>
                <div>
                  <ol>
                    <li><h4>Select Scenarios</h4></li>
                      <ul>
                        <li><strong>Scenario Bar Charts</strong> - choose level of ambition for policies and context factors. Click on a bar to toggle between selection and deselection (light gray color). More than one level can be selected. Level 1 always reflects the region’s adopted plans for the future.</li>
                          <ul>
                            <li><strong>Level 1</strong>: reference case reflecting funded adopted plans</li>
                            <li><strong>Level 0</strong>: a retreat from current plans/forecasts </li>
                            <li><strong>Levels 2-3</strong>: more ambitious policies </li>
                          </ul>
                        <li><strong>Bar Charts</strong> - click and drag mouse to select desired range of outcome measure. Change a selected range by clicking in the middle and dragging to move the entire selection or by clicking and dragging handles at either end of the range.</li>
                      </ul>
                        <li><h4>View Results</h4></li>
                          <ul>
                            <li>Scenario Bar Charts (factors) and Bar Charts (outcomes) represent the values of the selected scenarios</li>
                            <li>Table shows attributes (factor levels and outcomes) of the selected scenarios</li>
                          </ul>
                    </ol>
                    <ul>
                        <li><h4>Other</h4></li>
                        <ul>
                            <li>"Clear All Selections" restores original selections</li>
                            <li>Mouse over bar chart titles for short descriptions</li>
                            <li>"Detailed Instructions" button gives further information</li>
                        </ul>
                    </ul>
                  </div>
             </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
            </div>
        </div>
    </div>

    <!--
    DETAILED EXPLANATION BOOTSTRAP MODAL
    -->
    <div class="modal fade" id="detail-modal" tabindex="-1" role="dialog" aria-labelledby="detail-label" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h3 class="modal-title" id="detail-label">Detailed Instructions</h3>
            </div>
            <div class="modal-body">
                <h3>Categories</h3>
                <p>Many factors such as land use and fuel price affect light duty vehicle travel. Because the number of factors considered is large, they were grouped into 6 categories as follows:</p>
                <h4>Policy Factors: These categories represent factors within local and state control.</h4>
                <ul>
                    <li><strong>Community Design</strong> Policies that seek to enable shorter trips and alternate modes such as promotion of mixed use land use, transit service, bicycling, and <a href="http://www.arb.ca.gov/planning/tsaq/cashout/cashout.htm" target="_blank">parking management</a>.</li>
                    <li><strong>Marketing & Incentives</strong> Policies that improve driving efficiency such as<a href="http://www.ecodrive.org/" target="_blank"> ecodriving,</a>and <a href="http://www.oregon.gov/ODOT/hwy/its/Pages/its_devices.aspx" target="_blank">Intelligent Transportation System</a> efforts, as well as programs that reduce auto demand such as <a href="http://oregonstate.edu/sustainability/carshare" target="_blank"> carsharing,</a> and home or work-based <a href="http://drivelesssavemore.com/" target="_blank">transportation demand management</a>.</li>
                    <li><strong>Pricing</strong> Policies that move towards true cost pricing such as <a href="http://www.oregon.gov/ODOT/HWY/RUFPP/Pages/ruc_overview.aspx" target="_blank">road user fees</a> to pay for the cost of operating, maintaining and improving roads, <a href="http://en.wikipedia.org/wiki/Usage-based_insurance" target="_blank"> pay-as-you-drive (PAYD) insurance,</a> and environmental impact fees such as a <a href="http://www.fin.gov.bc.ca/tbs/tp/climate/A1.htm" target="_blank">carbon tax</a>.</li>
                    <li><strong>Vehicles & Fuels</strong> Factors representing the anticipated changes to future vehicles and fuels, in addition to those resulting from existing federal and state laws, including market changes such as the shift to electric vehicles or more fuel efficient vehicles, reduced <a href="http://www.deq.state.or.us/aq/cleanFuel/qa.htm" target="_blank">carbon intensity</a> of fuels, pace of vehicle turnover, and the light truck share of vehicles. Since these shifts would primarily result from additional federal and state policies promoting market changes, these scenarios assume supportive local actions that advocate for more amitious policies along with local efforts, such as installing charging stations and using landfill captured gases (renewable natural gas) to fuel buses.</li>
                </ul>
                <h4>Context Factors: These categories represent factors outside our control, but help evaluate the robustness of policies in the face of uncertain future conditions.</h4>
                <ul>
                <li><strong>Fuel Price</strong> The assumed market price of gasoline and other fuels (exclusive of fuel taxes).</li>
                <li><strong>Income Growth</strong> The assumed growth of average per capita income, representing the health of the economy.</li>
                </ul>
                <h3>Levels</h3>
                <p>Several levels were defined for each of the categories. These levels are numbered to indicate the amount of change from a <em>reference case</em> which represents the continuation of adopted local plans, policies and trends.</p>
                <ul>
                    <li><strong>Level 1:</strong> Corresponds to the <em>reference case</em>.</li>
                    <li><strong>Level 0:</strong> Represents a retreat from current plans (such as lower parking fees or less bicycling than anticipated), or lower context forecasts (lower fuel price or lower income).</li>
                    <li><strong>Levels 2-3:</strong> Representing more ambitious policies or higher context forecasts (higher fuel price or higher income).</li>
                </ul>
                <p>The levels are displayed in category bar charts, one for each category, as shown in the following illustration. </p>
                <img src="img/barchartscen.png" width="95%">
                <p>Each bar chart has a legend showing the color associated with each level.  The sizes of the bars show the proportions of the selected scenarios in each level. The number of selected scenarios in each level is shown in the corresponding bar. You can select (or deselect) the scenarios in a level by either clicking on the bar. The selected level is colored and the non-selected levels are grayed-out. The total count of unfiltered scenarios is shown in light gray.</p>
                <h3>Outcomes</h3>
                <p>Given the chosen category inputs, the web page also shows future year outcomes for the following performance measures:</p>
                <ul>
                    <li><strong>GHG Target Reduction:</strong> 2005-2038 percentage reduction in light-duty vehicle GHG emissions (beyond what is anticipated to occur due to baseline assumptions regarding improvements to vehiles and fuels). RVMPO has a 2005 - 2035 state-set GHG reduction target of 19%</li>
                    <li><strong>DVMT Per Capita:</strong> daily vehicle miles of travel of residents divided by population.</li>
                    <li><strong>Bike Travel Per Capita:</strong> annual miles of resident bike travel (not including recreational travel) divided by population.</li>
                    <li><strong>Walk Travel Per Capita:</strong> annual residents' walk trips (not including recreation or walk to transit) divided by population.</li>
                    <li><strong>Air Pollution Emissions:</strong> daily metric tons of pollutants emitted from all light-duty vehicle travel (including hydrocarbons, carbon monoxide, nitrogen dioxide, and particulates).</li>
                    <li><strong>Annual Fuel Use:</strong> annual million gallons of gasoline and other fuels consumed by all light-duty vehicle travel.</li>
                    <li><strong>Annual Household Vehicle Cost:</strong> average annual household cost (thousand dollars) for owning and operating light-duty vehicles (including gas, taxes, parking, registration, depreciation, maintenance, and financing).</li>
                    <li><strong>Truck Delay:</strong> daily vehicle-hours of delay for heavy truck travel on area roads.</li>
                </ul>
                <p>Each outcome is illustrated in a bar chart showing the range of outcome values for the selected scenarios.  The bar height indicates the number of selected scenarios with that outcome value. The value for the 2038 adopted plans scenario is shown above the bar chart.  The following illustrations shows what the <em>CO2e Target Reduction</em>, <em>DVMT Per Capita</em>, <em>Bike Travel per Capita</em>, and <em>Walk Travel Per Capita</em> bar charts look like when all scenarios are selected.</p>
                <img src="img/barchart1.png" width="95%">
                <p>The bar charts can also be used to make a selection of scenarios.  This can be done by hovering the mouse cursor over the bar at one end of the desired selection range.  When the cursor is in the shape of a crosshairs, click and drag the mouse cursor to the other end of the desired range.  The bar chart will change to show the selected range.  In addition, handles will appear at the ends of the selected range.  You can click one of these handles and drag it to alter the selection.  You can also click on the middle of the selection (when the shape of the cursor is a crossed arrow) to drag the whole selection to a different location on the bar chart.  As you make a selection in one bar chart, all the other bar charts will change to show outcomes for just the selected scenarios.  The category bar charts will also change accordingly to show the numbers and proportions of the selected scenarios in each level.  The following illustration shows a selection made on the <em>GHG Target Reduction</em> bar chart and the corresponding values for the other bar charts in the row.  You can see in the picture the crossed-arrow cursor shape which means that the whole selection can be dragged to a new position.</p>
                <img src="img/barchart2.png" width="95%">
                <p>Selections can be made simultaneously on multiple scenario bar charts and output bar charts.  As more selections are made, all of the charts will be updated to show the selections that meet all of the selection conditions.  To clear all of the selections and return to the starting conditions, just click on one of the <em>Clear All Selections</em> links on the web page.</p>
                <h3>Selected Scenario Table</h3>
                <p>Finally, at the bottom of the web page is a table which shows the data for the selected scenarios, including both the input factor levels and the outcome measures.  The values in this table can be copied and saved in a spreadsheet.  To select the data to copy, double-click in the top left-hand cell, press the <em>Shift</em> key, and then click in the bottom right-hand cell.  Once the data has been selected, copy it by pressing the <em>Ctrl c</em> keys for a Windows computer or the <em>Command c</em> keys for an Apple Macintosh computer.  Paste as unicode text into the spreadsheet of your choice.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
            </div>
        </div>
    </div>

    <!--
    SET UP CONTAINER FOR DISPLAYS OF CATEGORIES (BAR CHARTS)
    -->
    <hr>
    <div style="text-align:center">
        <h3 id="ScenarioDisplayTitle"></h3>
    </div>
    <hr>
    <div class='container'>
        <!-- HEADING AND RESET LINK -->
        <div class='row'>
            <h3>
                <span>Scenario Input Levels</span>
                <span><a href="javascript:history.go(0)"> | Clear All Selections</a></span> <!-- RESET BY RELOADING PAGE -->
            </h3>
        </div>
        <!-- INPUT CATEGORY PIE CHARTS -->
        <div class='container' id='ScenarioInputCharts'>         
           <!-- Javacript inserts input bar charts here -->
        </div>
    </div>

    <!--
    SET UP CONTAINER FOR DISPLAYS OF OUTPUTS (BAR CHARTS)
    -->
    <hr>
    <div class='container'>
        <!-- HEADING AND RESET LINK -->
        <div class='row'>
            <h3>
                <span>Model Outputs:</span>
                <span class="num-selected-scenarios">
                    <span class="filter-count" id="filter"></span>
                    scenarios selected out of
                    <span class="total-count" id="total"></span>
                    scenarios |
                    <a href="javascript:history.go(0)">Clear All Selections</a>
                </span>
             </h3>
        </div>
        <!-- OUTPUT MEASURE BAR CHARTS -->
        <div class='container' id='OutputInputCharts'>
           <!-- Javacript inserts output bar charts here -->
        </div>    
    <hr>

    <!--
    SET UP CONTAINER FOR DISPLAYS OF OUTPUT DATA TABLE
    -->
    <div class='container col-sm-12' style='font: 12px sans-serif;'>
        <div class='row'>
            <h3>Scenario Data Table</h3>
        </div>
        <div class='row'>
            <div class='col-sm-12'>
                <table class='table table-hover' id='Scenario-Results'>
                    <thead>
                        <tr class='header'>                      
                        </tr>
                   </thead>
                </table>
            </div>
        </div>
    </div>

    <!--
    LOAD CONFIGURATION AND SCENARIO DATA (ALL-IN-ONE)
    -->
    <script src='visualizer.js'
        onError='myFunc=function(){}; myFunc();'
           type='text/javascript'></script>
    <!-- Obsolete individual inputs
    <script src='data/scenario-cfg.js' type='text/javascript'></script>  scenconfig
    <script src='data/category-cfg.js' type='text/javascript'></script>  catconfig
    <script src='data/output-cfg.js'   type='text/javascript'></script>  outputconfig
    <script src='data/vedata.js'       type='text/javascript'></script>  VEdata
    -->
    
    <!--
    LOAD AND RUN SCRIPT TO POPULATE THE USER INTERFACE
    -->
    <script src='js/VisualVE.js' type="text/javascript"></script>

</body>
