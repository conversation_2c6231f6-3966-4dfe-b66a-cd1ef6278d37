## VisionEval 3.0: The Next Generation

### Walkthrough Instructions

The VisionEval Next Generation (VNG, aka VisionEval 3.0) "walkthrough" is an interactive tutorial
and illustration of what VNG can do. The `walkthrough.R` script is commented and illustrates how to
take advantage the new VNG features and functions.

To use the walkthrough, just start VisionEval and enter this function:

```
walkthrough()
```

It will instruct you to visit several other files in turn from which you can run commands to see
how VisionEval runs and how to work with it. Much more extensive runnable examples and complete
explanations of the various commands and functions are on the
[VisionEval Documentation site](https://visioneval.org/docs).

In brief, the walkthrough in `00-walkthrough.R` includes the following sections:

- Installation of base models: `01-install.R`
  + Demonstrates installing built-in models that you can use as the basis for your own models

- Basic elements of running models: `02-running.R`
- Extracting results: `03-extract.R`
  + Shows how to extract raw results from a model into tabular form that you can analyze in another system (or continue to analyze in R)

- Run scenarios: `04-scenarios.R`

- Develop and run queries: `05-queries.R`