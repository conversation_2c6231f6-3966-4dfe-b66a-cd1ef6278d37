Package: VEModel
Type: Package
Title: Utilities for Managing VisionEval Models and Samples
Version: 3.1.1
Date: 2024-01-23
Author: <PERSON> [aut, cre]
Maintainer: <PERSON> <<EMAIL>>
Copyright: None
Description: Contains R6 object management code providing functions
  and objects to set up VisionEval models, run them, and query or extract
  their output.
License: file LICENSE
Depends: R (>= 4.0.0)
Imports:
    visioneval,
    R6,
    stringr,
    yaml,
    futile.logger,
    jsonlite,
    parallelly,
    future,
    future.callr,
    jrc,
    methods,
    DBI,
    RSQLite,
    data.table
Suggests:
    knitr,
    pkgload,
    markdown,
    RMariaDB
Encoding: UTF-8
RoxygenNote: 7.2.3
Collate: 
    'environment.R'
    'export.R'
    'models.R'
    'results.R'
    'query.R'
    'scenarios.R'
    'zzz.R'
