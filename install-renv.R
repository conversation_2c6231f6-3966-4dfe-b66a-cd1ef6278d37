#!/usr/bin/env Rscript

# VisionEval Linux Installation Bootstrap (renv-based)
#
# This script prepares a project-local renv library on Linux, discovers
# package dependencies across the VisionEval source tree, installs them
# into the renv library, and snapshots the resulting lockfile so future
# sessions can run `renv::restore()`.
#
# Usage:
#   Rscript install-renv.R [path/to/VisionEval-4]
#
# The optional path argument defaults to the directory that contains
# this script (or the current working directory when run from an
# interactive session).

args <- commandArgs(trailingOnly = TRUE)
script.path <- tryCatch({
  cmd <- commandArgs(trailingOnly = FALSE)
  script <- cmd[grepl("^--file=", cmd)]
  if (length(script)) normalizePath(sub("^--file=", "", script)) else NA_character_
}, error = function(e) NA_character_)

project <- if (length(args) >= 1) {
  normalizePath(args[[1]], winslash = "/", mustWork = TRUE)
} else if (!is.na(script.path)) {
  normalizePath(dirname(script.path), winslash = "/", mustWork = TRUE)
} else {
  normalizePath(getwd(), winslash = "/", mustWork = TRUE)
}

cran.mirror <- Sys.getenv("VE_CRAN_MIRROR", unset = "https://cloud.r-project.org")
options(repos = c(CRAN = cran.mirror))

if (!requireNamespace("renv", quietly = TRUE)) {
  message("Installing renv from CRAN at ", cran.mirror)
  install.packages("renv")
}

suppressPackageStartupMessages(library(renv))
activate.file <- file.path(project, "renv", "activate.R")
lockfile <- file.path(project, "renv.lock")

if (!file.exists(activate.file)) {
  message("Initialising renv project at ", project)
  renv::init(project = project, bare = TRUE, force = TRUE)
} else {
  renv::activate(project = project)
}

for (pkg in c("devtools", "here")) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    message("Installing ", pkg, " from CRAN at ", cran.mirror)
    install.packages(pkg)
  }
}

ve_pkgs <- c("sources/framework/visioneval", 
    "sources/framework/VEModel", 
    "sources/modules/VE2001NHTS", 
    "sources/modules/VETransportSupplyUse", 
    "sources/modules/VEHouseholdTravel",
    "sources/modules/VELandUse", 
    "source/modules/VEScenario",
    "source/modules/VESimTransportSupply",
    "source/modules/VETravelDemandMM",
    "source/modules/VEPowertrainsAndFuels",
    "source/modules/VESimHouseholds",
    "source/modules/VESyntheticFirms",
    "source/modules/VETravelPerformance",
    "source/modules/VEHouseholdVehicles",
    "source/modules/VESimLandUse",
    "source/modules/VETransportSupply",
    "source/modules/VEReports"
    )

for (pkg in ve_pkgs) {
    pkg_name <- gsub(".*/", "", pkg)
  if (!requireNamespace(pkg, quietly = TRUE)) {
    message("Installing ", pkg, " from local source")
    devtools::install(here::here(pkg), 
                    dependencies = TRUE,
                    upgrade = "never",
                    build = TRUE
                    )
  }
}
