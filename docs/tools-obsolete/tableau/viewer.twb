<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20204.21.0114.0916                               -->
<workbook original-version='18.1' source-build='2020.4.1 (20204.21.0114.0916)' source-platform='mac' version='18.1' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.MarkAnimation.true...MarkAnimation />
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
    <SheetIdentifierTracking />
    <WindowsPersistSimpleIdentifiers />
  </document-format-change-manifest>
  <preferences>
    <preference name='ui.encoding.shelf.height' value='24' />
    <preference name='ui.shelf.height' value='26' />
  </preferences>
  <datasources>
    <datasource caption='for-tableau' inline='true' name='federated.0b4eq4k0c5dawy13ueqiy1b7ch6t' version='18.1'>
      <connection class='federated'>
        <named-connections>
          <named-connection caption='for-tableau' name='textscan.1vlsv0l0koejk1169kxpv03pixbx'>
            <connection class='textscan' directory='/Users/<USER>/Documents/GitHub/client_fhwa_vision_eval/tableau-update/output' filename='for-tableau.csv' password='' server='' />
          </named-connection>
        </named-connections>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation connection='textscan.1vlsv0l0koejk1169kxpv03pixbx' name='for-tableau.csv' table='[for-tableau#csv]' type='table'>
          <columns character-set='UTF-8' header='yes' locale='en_US' separator=','>
            <column datatype='string' name='Scenario' ordinal='0' />
            <column datatype='integer' name='B' ordinal='1' />
            <column datatype='integer' name='L' ordinal='2' />
            <column datatype='integer' name='T' ordinal='3' />
            <column datatype='integer' name='V' ordinal='4' />
            <column datatype='integer' name='C' ordinal='5' />
            <column datatype='integer' name='M' ordinal='6' />
            <column datatype='real' name='UrbanLdvDmvtPerCap' ordinal='7' />
            <column datatype='real' name='AnnualHouseholdVehicleCosts' ordinal='8' />
            <column datatype='real' name='UrbanTransitTrips' ordinal='9' />
            <column datatype='real' name='TotalGHG' ordinal='10' />
            <column datatype='real' name='Annual_Fuel_PC' ordinal='11' />
            <column datatype='real' name='UrbanLdv_TotalDelay' ordinal='12' />
            <column datatype='real' name='UrbanHhAveVehPerHh' ordinal='13' />
            <column datatype='real' name='HhVehTravCostShare' ordinal='14' />
            <column datatype='string' name='Bike_Diversion' ordinal='15' />
            <column datatype='string' name='Parking_and_Demand_Management' ordinal='16' />
            <column datatype='string' name='Land_Use' ordinal='17' />
            <column datatype='string' name='Energy_Costs_and_Pricing' ordinal='18' />
            <column datatype='string' name='Transit' ordinal='19' />
            <column datatype='string' name='Vehicles_&amp;_Fuels' ordinal='20' />
            <column datatype='integer' name='Strategy_Land_Use' ordinal='21' />
            <column datatype='integer' name='Strategy_Bikes_or_Light_Vehicles' ordinal='22' />
            <column datatype='integer' name='Strategy_Energy_Costs_and_Pricing_Policies' ordinal='23' />
            <column datatype='integer' name='Strategy_Transit_Service_and_Vehicles' ordinal='24' />
            <column datatype='integer' name='Strategy_Parking_and_Demand_Managment' ordinal='25' />
            <column datatype='integer' name='Strategy_Vehicles_and_Fuels' ordinal='26' />
            <column datatype='integer' name='scenario_index' ordinal='27' />
          </columns>
        </_.fcp.ObjectModelEncapsulateLegacy.false...relation>
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation connection='textscan.1vlsv0l0koejk1169kxpv03pixbx' name='for-tableau.csv' table='[for-tableau#csv]' type='table'>
          <columns character-set='UTF-8' header='yes' locale='en_US' separator=','>
            <column datatype='string' name='Scenario' ordinal='0' />
            <column datatype='integer' name='B' ordinal='1' />
            <column datatype='integer' name='L' ordinal='2' />
            <column datatype='integer' name='T' ordinal='3' />
            <column datatype='integer' name='V' ordinal='4' />
            <column datatype='integer' name='C' ordinal='5' />
            <column datatype='integer' name='M' ordinal='6' />
            <column datatype='real' name='UrbanLdvDmvtPerCap' ordinal='7' />
            <column datatype='real' name='AnnualHouseholdVehicleCosts' ordinal='8' />
            <column datatype='real' name='UrbanTransitTrips' ordinal='9' />
            <column datatype='real' name='TotalGHG' ordinal='10' />
            <column datatype='real' name='Annual_Fuel_PC' ordinal='11' />
            <column datatype='real' name='UrbanLdv_TotalDelay' ordinal='12' />
            <column datatype='real' name='UrbanHhAveVehPerHh' ordinal='13' />
            <column datatype='real' name='HhVehTravCostShare' ordinal='14' />
            <column datatype='string' name='Bike_Diversion' ordinal='15' />
            <column datatype='string' name='Parking_and_Demand_Management' ordinal='16' />
            <column datatype='string' name='Land_Use' ordinal='17' />
            <column datatype='string' name='Energy_Costs_and_Pricing' ordinal='18' />
            <column datatype='string' name='Transit' ordinal='19' />
            <column datatype='string' name='Vehicles_&amp;_Fuels' ordinal='20' />
            <column datatype='integer' name='Strategy_Land_Use' ordinal='21' />
            <column datatype='integer' name='Strategy_Bikes_or_Light_Vehicles' ordinal='22' />
            <column datatype='integer' name='Strategy_Energy_Costs_and_Pricing_Policies' ordinal='23' />
            <column datatype='integer' name='Strategy_Transit_Service_and_Vehicles' ordinal='24' />
            <column datatype='integer' name='Strategy_Parking_and_Demand_Managment' ordinal='25' />
            <column datatype='integer' name='Strategy_Vehicles_and_Fuels' ordinal='26' />
            <column datatype='integer' name='scenario_index' ordinal='27' />
          </columns>
        </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
        <metadata-records>
          <metadata-record class='capability'>
            <remote-name />
            <remote-type>0</remote-type>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias />
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='character-set'>&quot;UTF-8&quot;</attribute>
              <attribute datatype='string' name='collation'>&quot;en_US&quot;</attribute>
              <attribute datatype='string' name='field-delimiter'>&quot;,&quot;</attribute>
              <attribute datatype='string' name='header-row'>&quot;true&quot;</attribute>
              <attribute datatype='string' name='locale'>&quot;en_US&quot;</attribute>
              <attribute datatype='string' name='single-char'>&quot;&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Scenario</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Scenario]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>Scenario</remote-alias>
            <ordinal>0</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>B</remote-name>
            <remote-type>20</remote-type>
            <local-name>[B]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>B</remote-alias>
            <ordinal>1</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>L</remote-name>
            <remote-type>20</remote-type>
            <local-name>[L]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>L</remote-alias>
            <ordinal>2</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>T</remote-name>
            <remote-type>20</remote-type>
            <local-name>[T]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>T</remote-alias>
            <ordinal>3</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>V</remote-name>
            <remote-type>20</remote-type>
            <local-name>[V]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>V</remote-alias>
            <ordinal>4</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>C</remote-name>
            <remote-type>20</remote-type>
            <local-name>[C]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>C</remote-alias>
            <ordinal>5</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>M</remote-name>
            <remote-type>20</remote-type>
            <local-name>[M]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>M</remote-alias>
            <ordinal>6</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UrbanLdvDmvtPerCap</remote-name>
            <remote-type>5</remote-type>
            <local-name>[UrbanLdvDmvtPerCap]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>UrbanLdvDmvtPerCap</remote-alias>
            <ordinal>7</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>AnnualHouseholdVehicleCosts</remote-name>
            <remote-type>5</remote-type>
            <local-name>[AnnualHouseholdVehicleCosts]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>AnnualHouseholdVehicleCosts</remote-alias>
            <ordinal>8</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UrbanTransitTrips</remote-name>
            <remote-type>5</remote-type>
            <local-name>[UrbanTransitTrips]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>UrbanTransitTrips</remote-alias>
            <ordinal>9</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TotalGHG</remote-name>
            <remote-type>5</remote-type>
            <local-name>[TotalGHG]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>TotalGHG</remote-alias>
            <ordinal>10</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Annual_Fuel_PC</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Annual_Fuel_PC]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>Annual_Fuel_PC</remote-alias>
            <ordinal>11</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UrbanLdv_TotalDelay</remote-name>
            <remote-type>5</remote-type>
            <local-name>[UrbanLdv_TotalDelay]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>UrbanLdv_TotalDelay</remote-alias>
            <ordinal>12</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UrbanHhAveVehPerHh</remote-name>
            <remote-type>5</remote-type>
            <local-name>[UrbanHhAveVehPerHh]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>UrbanHhAveVehPerHh</remote-alias>
            <ordinal>13</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>HhVehTravCostShare</remote-name>
            <remote-type>5</remote-type>
            <local-name>[HhVehTravCostShare]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>HhVehTravCostShare</remote-alias>
            <ordinal>14</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Bike_Diversion</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Bike_Diversion]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>Bike_Diversion</remote-alias>
            <ordinal>15</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Parking_and_Demand_Management</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Parking_and_Demand_Management]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>Parking_and_Demand_Management</remote-alias>
            <ordinal>16</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Land_Use</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Land_Use]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>Land_Use</remote-alias>
            <ordinal>17</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Energy_Costs_and_Pricing</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Energy_Costs_and_Pricing]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>Energy_Costs_and_Pricing</remote-alias>
            <ordinal>18</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Transit</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Transit]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>Transit</remote-alias>
            <ordinal>19</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Vehicles_&amp;_Fuels</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Vehicles_&amp;_Fuels]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>Vehicles_&amp;_Fuels</remote-alias>
            <ordinal>20</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Strategy_Land_Use</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Strategy_Land_Use]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>Strategy_Land_Use</remote-alias>
            <ordinal>21</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Strategy_Bikes_or_Light_Vehicles</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Strategy_Bikes_or_Light_Vehicles]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>Strategy_Bikes_or_Light_Vehicles</remote-alias>
            <ordinal>22</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Strategy_Energy_Costs_and_Pricing_Policies</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Strategy_Energy_Costs_and_Pricing_Policies]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>Strategy_Energy_Costs_and_Pricing_Policies</remote-alias>
            <ordinal>23</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Strategy_Transit_Service_and_Vehicles</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Strategy_Transit_Service_and_Vehicles]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>Strategy_Transit_Service_and_Vehicles</remote-alias>
            <ordinal>24</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Strategy_Parking_and_Demand_Managment</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Strategy_Parking_and_Demand_Managment]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>Strategy_Parking_and_Demand_Managment</remote-alias>
            <ordinal>25</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Strategy_Vehicles_and_Fuels</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Strategy_Vehicles_and_Fuels]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>Strategy_Vehicles_and_Fuels</remote-alias>
            <ordinal>26</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>scenario_index</remote-name>
            <remote-type>20</remote-type>
            <local-name>[scenario_index]</local-name>
            <parent-name>[for-tableau.csv]</parent-name>
            <remote-alias>scenario_index</remote-alias>
            <ordinal>27</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
        </metadata-records>
      </connection>
      <aliases enabled='yes' />
      <column caption='Annual Household Vehicle Costs' datatype='real' name='[AnnualHouseholdVehicleCosts]' role='measure' type='quantitative' />
      <column caption='Annual Fuel PC' datatype='real' name='[Annual_Fuel_PC]' role='measure' type='quantitative' />
      <column caption='Bike Diversion' datatype='string' name='[Bike_Diversion]' role='dimension' type='nominal' />
      <column caption='Energy Costs and Pricing' datatype='string' name='[Energy_Costs_and_Pricing]' role='dimension' type='nominal' />
      <column caption='Hh Veh Trav Cost Share' datatype='real' name='[HhVehTravCostShare]' role='measure' type='quantitative' />
      <column caption='Land Use' datatype='string' name='[Land_Use]' role='dimension' type='nominal' />
      <column caption='Parking and Demand Management' datatype='string' name='[Parking_and_Demand_Management]' role='dimension' type='nominal' />
      <column caption='Strategy Bikes or Light Vehicles' datatype='integer' name='[Strategy_Bikes_or_Light_Vehicles]' role='measure' type='quantitative' />
      <column caption='Strategy Energy Costs and Pricing Policies' datatype='integer' name='[Strategy_Energy_Costs_and_Pricing_Policies]' role='measure' type='quantitative' />
      <column caption='Strategy Land Use' datatype='integer' name='[Strategy_Land_Use]' role='measure' type='quantitative' />
      <column caption='Strategy Parking and Demand Managment' datatype='integer' name='[Strategy_Parking_and_Demand_Managment]' role='measure' type='quantitative' />
      <column caption='Strategy Transit Service and Vehicles' datatype='integer' name='[Strategy_Transit_Service_and_Vehicles]' role='measure' type='quantitative' />
      <column caption='Strategy Vehicles and Fuels' datatype='integer' name='[Strategy_Vehicles_and_Fuels]' role='measure' type='quantitative' />
      <column aggregation='None' caption='Total GHG (bin)' datatype='integer' name='[Total GHG (bin)]' role='dimension' type='ordinal'>
        <calculation class='bin' decimals='-3' formula='[TotalGHG]' peg='0' size='0.005' />
      </column>
      <column caption='Total GHG' datatype='real' name='[TotalGHG]' role='measure' type='quantitative' />
      <column caption='Urban Transit Trips (bin)' datatype='integer' name='[Urban Transit Trips (bin)]' role='dimension' type='ordinal'>
        <calculation class='bin' decimals='2' formula='[UrbanTransitTrips]' peg='0' size='100' />
      </column>
      <column caption='Urban Hh Ave Veh Per Hh' datatype='real' name='[UrbanHhAveVehPerHh]' role='measure' type='quantitative' />
      <column caption='Urban Ldv Dmvt Per Cap' datatype='real' name='[UrbanLdvDmvtPerCap]' role='measure' type='quantitative' />
      <column caption='UrbanLdv TotalDelay' datatype='real' name='[UrbanLdv_TotalDelay]' role='measure' type='quantitative' />
      <column caption='Urban Transit Trips' datatype='real' name='[UrbanTransitTrips]' role='measure' type='quantitative' />
      <column caption='Vehicles &amp; Fuels' datatype='string' name='[Vehicles_&amp;_Fuels]' role='dimension' type='nominal' />
      <_.fcp.ObjectModelTableType.true...column caption='for-tableau.csv' datatype='table' name='[__tableau_internal_object_id__].[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]' role='measure' type='quantitative' />
      <column caption='Scenario Index' datatype='integer' name='[scenario_index]' role='measure' type='quantitative' />
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' show-structure='true' />
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
      </semantic-values>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='for-tableau.csv' id='for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B'>
            <properties context=''>
              <relation connection='textscan.1vlsv0l0koejk1169kxpv03pixbx' name='for-tableau.csv' table='[for-tableau#csv]' type='table'>
                <columns character-set='UTF-8' header='yes' locale='en_US' separator=','>
                  <column datatype='string' name='Scenario' ordinal='0' />
                  <column datatype='integer' name='B' ordinal='1' />
                  <column datatype='integer' name='L' ordinal='2' />
                  <column datatype='integer' name='T' ordinal='3' />
                  <column datatype='integer' name='V' ordinal='4' />
                  <column datatype='integer' name='C' ordinal='5' />
                  <column datatype='integer' name='M' ordinal='6' />
                  <column datatype='real' name='UrbanLdvDmvtPerCap' ordinal='7' />
                  <column datatype='real' name='AnnualHouseholdVehicleCosts' ordinal='8' />
                  <column datatype='real' name='UrbanTransitTrips' ordinal='9' />
                  <column datatype='real' name='TotalGHG' ordinal='10' />
                  <column datatype='real' name='Annual_Fuel_PC' ordinal='11' />
                  <column datatype='real' name='UrbanLdv_TotalDelay' ordinal='12' />
                  <column datatype='real' name='UrbanHhAveVehPerHh' ordinal='13' />
                  <column datatype='real' name='HhVehTravCostShare' ordinal='14' />
                  <column datatype='string' name='Bike_Diversion' ordinal='15' />
                  <column datatype='string' name='Parking_and_Demand_Management' ordinal='16' />
                  <column datatype='string' name='Land_Use' ordinal='17' />
                  <column datatype='string' name='Energy_Costs_and_Pricing' ordinal='18' />
                  <column datatype='string' name='Transit' ordinal='19' />
                  <column datatype='string' name='Vehicles_&amp;_Fuels' ordinal='20' />
                  <column datatype='integer' name='Strategy_Land_Use' ordinal='21' />
                  <column datatype='integer' name='Strategy_Bikes_or_Light_Vehicles' ordinal='22' />
                  <column datatype='integer' name='Strategy_Energy_Costs_and_Pricing_Policies' ordinal='23' />
                  <column datatype='integer' name='Strategy_Transit_Service_and_Vehicles' ordinal='24' />
                  <column datatype='integer' name='Strategy_Parking_and_Demand_Managment' ordinal='25' />
                  <column datatype='integer' name='Strategy_Vehicles_and_Fuels' ordinal='26' />
                  <column datatype='integer' name='scenario_index' ordinal='27' />
                </columns>
              </relation>
            </properties>
          </object>
        </objects>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
  </datasources>
  <worksheets>
    <worksheet name='Total GHG'>
      <table>
        <view>
          <datasources>
            <datasource caption='for-tableau' name='federated.0b4eq4k0c5dawy13ueqiy1b7ch6t' />
          </datasources>
          <datasource-dependencies datasource='federated.0b4eq4k0c5dawy13ueqiy1b7ch6t'>
            <column caption='Bike Diversion' datatype='string' name='[Bike_Diversion]' role='dimension' type='nominal' />
            <column caption='Energy Costs and Pricing' datatype='string' name='[Energy_Costs_and_Pricing]' role='dimension' type='nominal' />
            <column caption='Land Use' datatype='string' name='[Land_Use]' role='dimension' type='nominal' />
            <column caption='Parking and Demand Management' datatype='string' name='[Parking_and_Demand_Management]' role='dimension' type='nominal' />
            <column aggregation='None' caption='Total GHG (bin)' datatype='integer' name='[Total GHG (bin)]' role='dimension' type='ordinal'>
              <calculation class='bin' decimals='-3' formula='[TotalGHG]' peg='0' size='0.005' />
            </column>
            <column caption='Total GHG' datatype='real' name='[TotalGHG]' role='measure' type='quantitative' />
            <column datatype='string' name='[Transit]' role='dimension' type='nominal' />
            <column caption='Vehicles &amp; Fuels' datatype='string' name='[Vehicles_&amp;_Fuels]' role='dimension' type='nominal' />
            <_.fcp.ObjectModelTableType.false...column caption='for-tableau.csv' datatype='integer' name='[__tableau_internal_object_id__].[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]' role='measure' type='quantitative' />
            <column-instance column='[__tableau_internal_object_id__].[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B:qk]' pivot='key' type='quantitative' />
            <_.fcp.ObjectModelTableType.true...column caption='for-tableau.csv' datatype='table' name='[__tableau_internal_object_id__].[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]' role='measure' type='quantitative' />
            <column-instance column='[Bike_Diversion]' derivation='None' name='[none:Bike_Diversion:nk]' pivot='key' type='nominal' />
            <column-instance column='[Energy_Costs_and_Pricing]' derivation='None' name='[none:Energy_Costs_and_Pricing:nk]' pivot='key' type='nominal' />
            <column-instance column='[Land_Use]' derivation='None' name='[none:Land_Use:nk]' pivot='key' type='nominal' />
            <column-instance column='[Parking_and_Demand_Management]' derivation='None' name='[none:Parking_and_Demand_Management:nk]' pivot='key' type='nominal' />
            <column-instance column='[Total GHG (bin)]' derivation='None' name='[none:Total GHG (bin):ok]' pivot='key' type='ordinal' />
            <column-instance column='[Transit]' derivation='None' name='[none:Transit:nk]' pivot='key' type='nominal' />
            <column-instance column='[Vehicles_&amp;_Fuels]' derivation='None' name='[none:Vehicles_&amp;_Fuels:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Bike_Diversion:nk]' filter-group='3'>
            <groupfilter function='level-members' level='[none:Bike_Diversion:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Energy_Costs_and_Pricing:nk]' filter-group='4'>
            <groupfilter from='&quot;C1&quot;' function='range' level='[none:Energy_Costs_and_Pricing:nk]' to='&quot;C6&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Land_Use:nk]' filter-group='5'>
            <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:Land_Use:nk]' member='&quot;L1 (2040TR)&quot;' />
              <groupfilter function='member' level='[none:Land_Use:nk]' member='&quot;L2&quot;' />
            </groupfilter>
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Parking_and_Demand_Management:nk]' filter-group='6'>
            <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:Parking_and_Demand_Management:nk]' member='&quot;M&quot;' />
              <groupfilter function='member' level='[none:Parking_and_Demand_Management:nk]' member='&quot;M2&quot;' />
              <groupfilter function='member' level='[none:Parking_and_Demand_Management:nk]' member='&quot;M3&quot;' />
              <groupfilter function='member' level='[none:Parking_and_Demand_Management:nk]' member='&quot;M4&quot;' />
            </groupfilter>
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Transit:nk]' filter-group='7'>
            <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:Transit:nk]' member='&quot;T1 (2040TR)&quot;' />
              <groupfilter function='member' level='[none:Transit:nk]' member='&quot;T2&quot;' />
              <groupfilter function='member' level='[none:Transit:nk]' member='&quot;T3&quot;' />
            </groupfilter>
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Vehicles_&amp;_Fuels:nk]' filter-group='8'>
            <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:Vehicles_&amp;_Fuels:nk]' member='&quot;V1 (AP)&quot;' />
              <groupfilter function='member' level='[none:Vehicles_&amp;_Fuels:nk]' member='&quot;V2 (2040TR)&quot;' />
            </groupfilter>
          </filter>
          <slices>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Bike_Diversion:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Energy_Costs_and_Pricing:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Land_Use:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Parking_and_Demand_Management:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Transit:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Vehicles_&amp;_Fuels:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='width' field='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Total GHG (bin):ok]' value='102' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='font-family' value='Open Sans' />
            <format attr='font-size' value='12' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <style>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[__tableau_internal_object_id__].[cnt:for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B:qk]</rows>
        <cols>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Total GHG (bin):ok]</cols>
        <show-full-range>
          <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[Total GHG (bin)]</column>
        </show-full-range>
      </table>
      <simple-id uuid='{2EFBEA08-CA70-4543-8B06-5AFAF348FC98}' />
    </worksheet>
    <worksheet name='Urban Transit Trips'>
      <table>
        <view>
          <datasources>
            <datasource caption='for-tableau' name='federated.0b4eq4k0c5dawy13ueqiy1b7ch6t' />
          </datasources>
          <datasource-dependencies datasource='federated.0b4eq4k0c5dawy13ueqiy1b7ch6t'>
            <column caption='Bike Diversion' datatype='string' name='[Bike_Diversion]' role='dimension' type='nominal' />
            <column caption='Energy Costs and Pricing' datatype='string' name='[Energy_Costs_and_Pricing]' role='dimension' type='nominal' />
            <column caption='Land Use' datatype='string' name='[Land_Use]' role='dimension' type='nominal' />
            <column caption='Parking and Demand Management' datatype='string' name='[Parking_and_Demand_Management]' role='dimension' type='nominal' />
            <column datatype='string' name='[Transit]' role='dimension' type='nominal' />
            <column caption='Urban Transit Trips (bin)' datatype='integer' name='[Urban Transit Trips (bin)]' role='dimension' type='ordinal'>
              <calculation class='bin' decimals='2' formula='[UrbanTransitTrips]' peg='0' size='100' />
            </column>
            <column caption='Urban Transit Trips' datatype='real' name='[UrbanTransitTrips]' role='measure' type='quantitative' />
            <column caption='Vehicles &amp; Fuels' datatype='string' name='[Vehicles_&amp;_Fuels]' role='dimension' type='nominal' />
            <_.fcp.ObjectModelTableType.false...column caption='for-tableau.csv' datatype='integer' name='[__tableau_internal_object_id__].[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]' role='measure' type='quantitative' />
            <column-instance column='[__tableau_internal_object_id__].[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B:qk]' pivot='key' type='quantitative' />
            <_.fcp.ObjectModelTableType.true...column caption='for-tableau.csv' datatype='table' name='[__tableau_internal_object_id__].[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]' role='measure' type='quantitative' />
            <column-instance column='[Bike_Diversion]' derivation='None' name='[none:Bike_Diversion:nk]' pivot='key' type='nominal' />
            <column-instance column='[Energy_Costs_and_Pricing]' derivation='None' name='[none:Energy_Costs_and_Pricing:nk]' pivot='key' type='nominal' />
            <column-instance column='[Land_Use]' derivation='None' name='[none:Land_Use:nk]' pivot='key' type='nominal' />
            <column-instance column='[Parking_and_Demand_Management]' derivation='None' name='[none:Parking_and_Demand_Management:nk]' pivot='key' type='nominal' />
            <column-instance column='[Transit]' derivation='None' name='[none:Transit:nk]' pivot='key' type='nominal' />
            <column-instance column='[Urban Transit Trips (bin)]' derivation='None' name='[none:Urban Transit Trips (bin):ok]' pivot='key' type='ordinal' />
            <column-instance column='[Vehicles_&amp;_Fuels]' derivation='None' name='[none:Vehicles_&amp;_Fuels:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Bike_Diversion:nk]' filter-group='3'>
            <groupfilter function='level-members' level='[none:Bike_Diversion:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Energy_Costs_and_Pricing:nk]' filter-group='4'>
            <groupfilter from='&quot;C1&quot;' function='range' level='[none:Energy_Costs_and_Pricing:nk]' to='&quot;C6&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Land_Use:nk]' filter-group='5'>
            <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:Land_Use:nk]' member='&quot;L1 (2040TR)&quot;' />
              <groupfilter function='member' level='[none:Land_Use:nk]' member='&quot;L2&quot;' />
            </groupfilter>
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Parking_and_Demand_Management:nk]' filter-group='6'>
            <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:Parking_and_Demand_Management:nk]' member='&quot;M&quot;' />
              <groupfilter function='member' level='[none:Parking_and_Demand_Management:nk]' member='&quot;M2&quot;' />
              <groupfilter function='member' level='[none:Parking_and_Demand_Management:nk]' member='&quot;M3&quot;' />
              <groupfilter function='member' level='[none:Parking_and_Demand_Management:nk]' member='&quot;M4&quot;' />
            </groupfilter>
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Transit:nk]' filter-group='7'>
            <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:Transit:nk]' member='&quot;T1 (2040TR)&quot;' />
              <groupfilter function='member' level='[none:Transit:nk]' member='&quot;T2&quot;' />
              <groupfilter function='member' level='[none:Transit:nk]' member='&quot;T3&quot;' />
            </groupfilter>
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Vehicles_&amp;_Fuels:nk]' filter-group='8'>
            <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:Vehicles_&amp;_Fuels:nk]' member='&quot;V1 (AP)&quot;' />
              <groupfilter function='member' level='[none:Vehicles_&amp;_Fuels:nk]' member='&quot;V2 (2040TR)&quot;' />
            </groupfilter>
          </filter>
          <slices>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Bike_Diversion:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Energy_Costs_and_Pricing:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Land_Use:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Parking_and_Demand_Management:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Transit:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Vehicles_&amp;_Fuels:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <format attr='title' class='0' field='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[__tableau_internal_object_id__].[cnt:for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B:qk]' scope='rows' value='Number of Records' />
          </style-rule>
          <style-rule element='cell'>
            <format attr='width' field='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Urban Transit Trips (bin):ok]' value='37' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='font-family' value='Open Sans' />
            <format attr='font-size' value='12' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <style>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[__tableau_internal_object_id__].[cnt:for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B:qk]</rows>
        <cols>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Urban Transit Trips (bin):ok]</cols>
        <show-full-range>
          <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[Total GHG (bin)]</column>
          <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[Urban Transit Trips (bin)]</column>
        </show-full-range>
      </table>
      <simple-id uuid='{E4050A82-00B6-48EB-BCD2-FE64FAD6905F}' />
    </worksheet>
    <worksheet name='filter'>
      <layout-options>
        <title>
          <formatted-text>
            <run>Number of Scenarios</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='for-tableau' name='federated.0b4eq4k0c5dawy13ueqiy1b7ch6t' />
          </datasources>
          <datasource-dependencies datasource='federated.0b4eq4k0c5dawy13ueqiy1b7ch6t'>
            <column caption='Bike Diversion' datatype='string' name='[Bike_Diversion]' role='dimension' type='nominal' />
            <column caption='Energy Costs and Pricing' datatype='string' name='[Energy_Costs_and_Pricing]' role='dimension' type='nominal' />
            <column caption='Land Use' datatype='string' name='[Land_Use]' role='dimension' type='nominal' />
            <column caption='Parking and Demand Management' datatype='string' name='[Parking_and_Demand_Management]' role='dimension' type='nominal' />
            <column datatype='string' name='[Transit]' role='dimension' type='nominal' />
            <column caption='Vehicles &amp; Fuels' datatype='string' name='[Vehicles_&amp;_Fuels]' role='dimension' type='nominal' />
            <_.fcp.ObjectModelTableType.false...column caption='for-tableau.csv' datatype='integer' name='[__tableau_internal_object_id__].[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]' role='measure' type='quantitative' />
            <column-instance column='[__tableau_internal_object_id__].[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]' derivation='Count' name='[__tableau_internal_object_id__].[cnt:for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B:qk]' pivot='key' type='quantitative' />
            <_.fcp.ObjectModelTableType.true...column caption='for-tableau.csv' datatype='table' name='[__tableau_internal_object_id__].[for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B]' role='measure' type='quantitative' />
            <column-instance column='[Bike_Diversion]' derivation='None' name='[none:Bike_Diversion:nk]' pivot='key' type='nominal' />
            <column-instance column='[Energy_Costs_and_Pricing]' derivation='None' name='[none:Energy_Costs_and_Pricing:nk]' pivot='key' type='nominal' />
            <column-instance column='[Land_Use]' derivation='None' name='[none:Land_Use:nk]' pivot='key' type='nominal' />
            <column-instance column='[Parking_and_Demand_Management]' derivation='None' name='[none:Parking_and_Demand_Management:nk]' pivot='key' type='nominal' />
            <column-instance column='[Transit]' derivation='None' name='[none:Transit:nk]' pivot='key' type='nominal' />
            <column-instance column='[Vehicles_&amp;_Fuels]' derivation='None' name='[none:Vehicles_&amp;_Fuels:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Bike_Diversion:nk]' filter-group='3'>
            <groupfilter function='level-members' level='[none:Bike_Diversion:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Energy_Costs_and_Pricing:nk]' filter-group='4'>
            <groupfilter from='&quot;C1&quot;' function='range' level='[none:Energy_Costs_and_Pricing:nk]' to='&quot;C6&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Land_Use:nk]' filter-group='5'>
            <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:Land_Use:nk]' member='&quot;L1 (2040TR)&quot;' />
              <groupfilter function='member' level='[none:Land_Use:nk]' member='&quot;L2&quot;' />
            </groupfilter>
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Parking_and_Demand_Management:nk]' filter-group='6'>
            <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:Parking_and_Demand_Management:nk]' member='&quot;M&quot;' />
              <groupfilter function='member' level='[none:Parking_and_Demand_Management:nk]' member='&quot;M2&quot;' />
              <groupfilter function='member' level='[none:Parking_and_Demand_Management:nk]' member='&quot;M3&quot;' />
              <groupfilter function='member' level='[none:Parking_and_Demand_Management:nk]' member='&quot;M4&quot;' />
            </groupfilter>
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Transit:nk]' filter-group='7'>
            <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:Transit:nk]' member='&quot;T1 (2040TR)&quot;' />
              <groupfilter function='member' level='[none:Transit:nk]' member='&quot;T2&quot;' />
              <groupfilter function='member' level='[none:Transit:nk]' member='&quot;T3&quot;' />
            </groupfilter>
          </filter>
          <filter class='categorical' column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Vehicles_&amp;_Fuels:nk]' filter-group='8'>
            <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:Vehicles_&amp;_Fuels:nk]' member='&quot;V1 (AP)&quot;' />
              <groupfilter function='member' level='[none:Vehicles_&amp;_Fuels:nk]' member='&quot;V2 (2040TR)&quot;' />
            </groupfilter>
          </filter>
          <slices>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Bike_Diversion:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Energy_Costs_and_Pricing:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Land_Use:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Parking_and_Demand_Management:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Transit:nk]</column>
            <column>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Vehicles_&amp;_Fuels:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='worksheet'>
            <format attr='font-family' value='Open Sans' />
            <format attr='font-size' value='12' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <text column='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[__tableau_internal_object_id__].[cnt:for-tableau.csv_62F3336F06CF42E8B7BD47753965A12B:qk]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows />
        <cols />
      </table>
      <simple-id uuid='{C00ABD96-E0F3-4676-A298-84E7000B7FE2}' />
    </worksheet>
  </worksheets>
  <windows source-height='30'>
    <window class='worksheet' name='filter'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
          <strip size='160'>
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Bike_Diversion:nk]' type='filter' />
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Energy_Costs_and_Pricing:nk]' type='filter' />
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Land_Use:nk]' type='filter' />
          </strip>
          <strip size='160'>
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Parking_and_Demand_Management:nk]' type='filter' />
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Transit:nk]' type='filter' />
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Vehicles_&amp;_Fuels:nk]' type='filter' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Bike_Diversion:nk]</field>
            <field>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Energy_Costs_and_Pricing:nk]</field>
            <field>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Land_Use:nk]</field>
            <field>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Parking_and_Demand_Management:nk]</field>
            <field>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Transit:nk]</field>
            <field>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Vehicles_&amp;_Fuels:nk]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{222993F5-A981-43C6-96D8-F4A565530C5D}' />
    </window>
    <window class='worksheet' maximized='true' name='Total GHG'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='179'>
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Bike_Diversion:nk]' type='filter' />
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Energy_Costs_and_Pricing:nk]' type='filter' />
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Land_Use:nk]' type='filter' />
          </strip>
          <strip size='168'>
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Parking_and_Demand_Management:nk]' type='filter' />
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Transit:nk]' type='filter' />
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Vehicles_&amp;_Fuels:nk]' type='filter' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Total GHG (bin):ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{A4696DDF-30B2-49A9-BC1E-E878EC80296A}' />
    </window>
    <window class='worksheet' name='Urban Transit Trips'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='179'>
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Bike_Diversion:nk]' type='filter' />
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Energy_Costs_and_Pricing:nk]' type='filter' />
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Land_Use:nk]' type='filter' />
          </strip>
          <strip size='173'>
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Parking_and_Demand_Management:nk]' type='filter' />
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Transit:nk]' type='filter' />
            <card param='[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Vehicles_&amp;_Fuels:nk]' type='filter' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Total GHG (bin):ok]</field>
            <field>[federated.0b4eq4k0c5dawy13ueqiy1b7ch6t].[none:Urban Transit Trips (bin):ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{C2A5BA99-802B-4FFE-8F07-EC947F08DD98}' />
    </window>
  </windows>
  <thumbnails>
    <thumbnail height='384' name='Total GHG' width='384'>
      iVBORw0KGgoAAAANSUhEUgAAAYAAAAGACAYAAACkx7W/AAAACXBIWXMAAA7DAAAOwwHHb6hk
      AAAgAElEQVR4nO3df3AU933/8dft3el0d/qJ0IFAqZCwaREG4x+41HVlkNHETuMY6Jg4MMXE
      NdS1O5mEadK0bjxtMthpzXRcT2lNbCduHH5ENDQuAXcAx8KCBDB8RTEwVZDBEEnICAn91p10
      u/f9g+HGYJxosfbu0D4fMxruVnu691s69rX72V+eRCKREADAdYx0FwAASA8CAABcigAAAJci
      AADApQgAAI7r7e1VT0+P4+8TjUbV2dnp+PuMFb50FwAgs/385z/Xjh071NXVpby8PBUWFupb
      3/rWx+ZrbW1VTk6O8vLyPva9+vp6xeNxfeELX0hOe++99/Taa68pHo8rHo/rG9/4hvr6+tTY
      2KjFixdLkn7wgx+opqZGpaWl2r59u9588035fD75fD794z/+o7xerySpo6ND//Ef/6Hq6mrt
      379fTzzxRPJ99uzZo6GhIdXU1HysrldffVXV1dUqLy//1L+nGxEBAOA3qq6uVnV1tf76r/9a
      3/jGN1RUVCRJOnr0qBobGzVnzhxNnDhRL7zwgqZNm6YFCxYoGAzqF7/4hbKzs6+54O3o6NCz
      zz6rF154QRMmTNDQ0JC8Xq8OHz6sCxcuJOdra2tTLBbTvn379NZbb2nt2rXKzs7W4OBgcuEv
      Sa+//rruueceSdLAwID++7//W5FIRHPnztXUqVOVSCTU2dmptrY2nTx5Ujk5OZo/f74WLFig
      l156Sc8995zDv8XMxBAQANv279+vjRs3qry8XM8++6xaW1vl9XpVUlKi3NxctbW1afLkyWps
      bNSbb755zdfff//9mjBhgvr7+9XX16d4PC5J+tWvfqVt27Zp27ZtOnPmjCTprbfe0mOPPabs
      7Gx1dXVpcHBQpmkmf15DQ4PuvPNOSdLevXs1ceJE7dy5U7t27dLRo0fV0NCgs2fP6lvf+pZK
      Skq0b98+7dmzR2VlZWptbZVlWSn4rWUeAgCAbb/4xS+0bNky3XnnnfrjP/5jvffeexo/frym
      TZumoqIiJRIJ7d27Vy0tLVes0V9mmqYCgYCkS0NMf/u3f6tt27ZJkrKyspSbm6vc3Fz5/f6P
      zb9hwwatWrVKZ8+eTf48wzBkGJcWZ1VVVbrrrrv0pS99SYcOHbrife+9917ddddduvfee3Xu
      3DlJUkFBgXp7e0f5N3RjIAAA2BYOh9XV1SVJunjxosLhsDweT3I8//nnn9cTTzxxzeEfSZo5
      c6bq6uoUj8f14IMPatGiRcnvTZkyRfPmzdO8efM0adIkSdKtt96qnTt3SpKeeuop3X777Vf8
      vI+uwV/e2dzV1aVwOHzFfFlZWZIkj8eTnBaNRpPh4jbsAwBg26JFi/TMM8/o7bff1sWLF7V0
      6VKZpqkXX3xRK1asUDgc1tq1a3Xx4kXNmjXrY68vLy/Xfffdp5UrV6q0tFQtLS165JFHrlgw
      f9QXvvAFPffcc3ryySdVVFSkpqYmPfroo8nv5+XlJbc0Tp06pW9/+9vJIZ/jx49/Yh+xWEyx
      WEzZ2dmf8jdyY/JwLSAA1yORSGhwcFChUCg5bXBwUFlZWTIMQ9FoVMFg8Lf+jP7+fgWDwSt2
      6n4S0zQVjUYVCoWuCIt33nlHx44d05NPPinp0oLd7/cnh4U+ydatWxWPx7VkyZLf+t5jEQEA
      YEw4ceKEKisrbb3m//7v/zRt2rTfGhRjVUYHgGmaI1orAADY587YAwAQAADgVgQAALiUowHQ
      19en5ubmK6aZpqljx44lnzc1NamhoUHDw8NOlgIAuIpjAXDmzBmtW7dOe/bsuWL6jh079F//
      9V+SpEOHDmnv3r3q7u7Wpk2bnCoFAHANjgVAMBjU0qVLr5h26tQp9ff3q6CgQJJ0+PBhLV68
      WPPmzVN7e7trr8cBAOng2JnAkUhE7e3tyefRaFQ7duzQypUrtX79ekmXhohyc3MlSaFQSAMD
      AwoGg8kgME0zeYEoAMDoStmlIHbs2CGfz6ddu3bp/PnzOnjwoILBYPJswWg0quzsbHm93uSx
      /4ZhcB4AADgkZQEwb9489ff3S7p0xl55ebkGBwdVV1enm2++WV6vVz4flyYCgFRx9Ezg/v5+
      nTp1SjNnzrxi+oEDB/T7v//7SiQSqq+vV1dXl6qqqpL7Bi7jTGAAcA6XggAAl+JEMABwKQIA
      AFyKAAAAlyIAAMClCAAAcCkCAABcijOvgGt4+uWf69jp8+ku42NuKY9ozcrqdJeBMYItAABw
      KQIAAFyKAAAAlyIAAMClCAAAcCkCAABcigAAAJciAADApQgAAHApAgAAXIoAAACXcjQA+vr6
      1NzcnHx++vRpHThwQB9++GFyWlNTkxoaGjQ8POxkKQCAqzgWAGfOnNG6deu0Z88eSVJzc7OO
      Hj2qQCCg73//++rq6tKhQ4e0d+9edXd3a9OmTU6VAgC4BscCIBgMaunSpcnnpaWleuihhzRr
      1izl5uZqaGhIhw8f1uLFizVv3jy1t7fLsiynygEAXMWxy0FHIhG1t7dfMW3//v16++23FQ6H
      VVRUpL6+PuXm5kqSQqGQBgYGFAwGk0Fgmqbi8bhTJQKfKFNXRizLUiwWS3cZGCNSej+AuXPn
      au7cufrpT3+qhoYGBYNBRaPR5L/Z2dnyer3yer2SJMMwko+BVDKMzDw+wjAMBQKBdJeBMSJl
      AdDa2irLshQMBtXW1qbp06drxowZqqur08033yyv1yufj/vTAECqOLrEDYVCmjVrliTJ6/Xq
      4MGDMk1T99xzj373d39X06ZNU319vU6cOKHly5c7WQoA4CqOBkA4HNbMmTMlSRMmTNDChQuv
      +L7H41FVVZWTJQAAPgFjLmn23qnzGXnvWUn60n23pLsEAA4iANLs2Onz2vzWsXSXcU0EADC2
      ZeahDgAAxxEAAOBSBAAAuBQBAAAuRQAAgEsRAADgUgQAALgUAQAALkUAAIBLcSYwRs2mt45l
      5FnNt5RHtGZldbrLADIOWwAA4FIEAAC4FAEAAC5FAACASxEAAOBSBAAAuJSjAdDX16fm5ubk
      8+bmZh08eFAXLlxITmtqalJDQ4OGh4edLAUAcBXHAuDMmTNat26d9uzZI0lqb2/Xvn375Pf7
      9b3vfU/9/f06dOiQ9u7dq+7ubm3atMmpUgAA1+BYAASDQS1dujT5vLi4WF/84hd12223aeLE
      ierr69Phw4e1ePFizZs3T+3t7bIsy6lyAABXcexM4Egkovb29o9Nb25u1uDgoCKRiPr6+pSb
      mytJCoVCGhgYUDAYTAaBaZqKx+NOlZgRzAzuLxaL2Zo/U3uxLMt2L5m6MnI9vQCfJKWXgjh3
      7px+8pOf6PHHH5fH41EwGFQ0Gk3+m52dLa/XK6/XK0kyDCP5eKzy+jL3ahyBQMDW/Jnai2EY
      tnsxjMw8PuJ6egE+Sco+5efPn9crr7yimpoadXR0qLe3VzNmzFBdXZ2amprk9Xrly9AFCACM
      RY4GQCgU0qxZsyRJg4ODmj17tt5//3397//+r9rb21VVVaVwOKwTJ05o+fLlTpYCALiKo6vc
      4XBYM2fOlCSVlZWprKzsY/NUVVU5WQIA4BNk5kAnAMBxBAAAuBQBAAAuRQAAgEsRAADgUgQA
      ALgUAQAALkUAAIBLEQAA4FIEAAC4FAEAAC5FAACASxEAAOBSBAAAuBQBAAAuRQAAgEtxD0Zg
      jHv65Z/r2Onz6S7jY24pj2jNyup0l+FqbAEAgEs5FgCJREI7d+7Um2++mZzW3t6uF154Qf39
      /ZKkrq4uvfTSS/rXf/1XnThxwqlSAADX4FgAHD58WIODg+rs7JQkWZal7du3yzAMmaYpSdq1
      a5fmz5+vVatWafv27U6VAgC4Bsf2Adx5550qKyvTzp07JUmGYWjFihX6/ve/n5yntbVVixYt
      ks/nUzAYVCwWk9/vVyKRkHQpNC4/Hqssy0p3CZ8oHo/bmj9Te0kkErZ7ydTPndt7wehK6z4A
      y7JkGJdK+OiWAQDAeWk9CigSiaitrU0lJSXq7e1VMBiUx+O5Yh6v15um6lLjcgBmIp/P3scj
      U3vxeDy2e7n6c5gp3N4LRldaf/vV1dXauHGjAoGA5syZk7EfVAAYixwNgOLiYi1btuyKaY89
      9ljycUlJiVavXq14PC6/3+9kKQCAq6R9m93j8bDwB4A0SHsAAADSgwAAAJciAADApQgAAHAp
      AgAAXIoAAACXIgAAwKUIAABwKQIAAFyKAAAAlyIAAMClCAAAcCkCAABcigAAAJciAADApQgA
      AHApAgAAXMrRAEgkEhoeHr7ieXd3txKJRHLa8PCw+vr6nCwDAHANjt0TuL+/Xz/84Q+Vl5en
      ZcuWybIsvfjiiyouLlZ7e7v+8i//Us3NzdqyZYvy8/NVXl6umpoap8oBAFzFsS2AM2fOaO7c
      ucnnTU1NmjJlipYtW6bp06fr+PHjqqur0/Lly7Vy5UodOXLkii0DAICzHNsCqKysVHt7u06c
      OCFJ6ujoUHFxsSRpwoQJam9vV2dnp4qLi+XxeBQOhzU4OKhAICDLsiRJpmkqHo87VWJGMDO4
      v1gsZmv+TO3FsizbvVz+DGYat/eC0eVYAFzN7/drYGBAkhSPx+Xz+eTz+WSapgzDkGma8nq9
      yS9JMgwj+Xis8vpS9iewLRAI2Jo/U3sxDMN2L4aRmcdHuL0XjK6UfTJ+53d+R42NjZKkxsZG
      feYzn1FZWZkaGxsVjUaTa/8AgNRwdJXN7/drwoQJkqRIJKKKigr9+7//u0pLS1VRUaGSkhLV
      1taqvr5eCxcudLIUAMBVHA2AgoICLViwIPn8vvvu03333Zd8HgqFtGLFCidLAAB8gswcHAQA
      OI4AAACXIgAAwKUIAABwKQIAAFyKAAAAlyIAAMClCAAAcCkCAABcigAAAJciAADApQgAAHAp
      AgAAXIoAAACXIgAAwKUIAABwKQIAAFyKAAAAl0ppAFiWpa6uLlmWlZw2PDysvr6+VJYBAJDD
      9wT+qMHBQf3bv/2bSkpKdP78eT311FNqaWnRli1blJ+fr/LyctXU1KSqHABwvZRtAZw4cUK3
      3367li5dqltuuUXvvfee6urqtHz5cq1cuVJHjhxRIpFIVTkA4Hop2wLIycnRiRMn1NfXp+bm
      ZhmGoc7OThUXF8vj8SgcDmtwcFCBQCA5RGSapuLxeKpKTAszg/uLxWK25s/UXizLst3LR4cp
      M4nbe8HoSlkATJs2TRcuXND//M//yO/3KxwOy+fzyTRNGYYh0zTl9XqTX5JkGEby8Vjl9aXs
      T2BbIBCwNX+m9mIYhu1eDCMzj49wey8YXSn7ZHg8Hv3hH/6hPv/5z+vcuXOaPn26ysrK1NjY
      qGg0mlz7BwCkhq1VtlOnTun111/X/fffrzlz5thes/jBD36gWCymz33uc8rLy1NNTY1qa2tV
      X1+vhQsX2vpZAIBPx1YAVFRU6Gtf+5p2796tn/3sZyotLdXjjz8+4mGaL3/5y1c8D4VCWrFi
      hZ0SAACjxPYQUE5OjqZMmaK8vDx1dHRk7A4mAMBvZmsL4IMPPtC6det05513atWqVcrPz3eq
      LgCAw2wFwPjx4/UXf/EXKi8v149//GNFIhFVV1c7VRsAwEG2hoCOHz+ulpYW/fKXv5Rpmjp6
      9KgGBgacqg0A4CBbAZCTk6OWlhZt375dS5YsUUFBgQYHB52qDQDgIFsBUFlZqXA4rGXLlsnv
      92vatGkaN26cU7UBABxkKwCampo0fvx4VVZWSpJ+/etfa2hoyJHCAADOshUA7e3tVyzwe3t7
      1dPTM+pFAQCcZ+sooNmzZ+vpp5/W+++/r1gsppaWFo0fP96p2gAADrIVAKFQSM8//7waGxvl
      9/t18803y+PxOFUbAMBBts8E9vl8mjFjhqZNm6Z/+qd/UldXlxN1AQAc9qmuBrpkyRLl5eWN
      Vi0AgBSyNQTU1tambdu2XTFtxYoVGXu9cQDAJ7MVAAUFBcn79vb09Gjbtm1j/oYtADBW2QqA
      7OxsTZkyJfn8yJEj6ujoUHFx8WjXBQBwmK0AaG5u1ubNmyVdul9vc3Ozli1b5khhAABn2QqA
      CRMmJG/q4vF4VFBQwPg/ANygbAWAYRjaunWrWlpaJEnBYFCrV6+W3+93pDgAgHNsBcCRI0c0
      YcIE+Xw+/cmf/Ilee+01WyeC7d+/XydPnlRZWZn+6I/+SKZpaufOnerp6VFNTY2KiopsNwAA
      uD62xm+ys7MVCAQ0btw49fT0KC8vTxcvXhzRaz/88EM1NDTokUce0alTp/TBBx9o9+7dCgaD
      qqqq0oYNG66rAQDA9bG1BTB9+nQVFhYqJydHzz33nIqKikZ8LaDc3Fz19fXp5MmT6u7uVlFR
      kZqamvT4448rOztblmUpHo/L57NVEgDgOtneBxCPx7V7926tWbNG586dG/EQUFZWlvLy8vTO
      O+/I6/UqKytLsVhMgUBAkhQIBBSNRhUMBpM3mjdNU/F43GZLNxYzg/uLxWK25s/UXizLst3L
      5c9gpnF7LxhdtgKgtbVV69evl2VZevDBB/XTn/5Ujz76qHJycn7ra48cOaKKigrV1NTonXfe
      0YEDB5Sfn6/u7m4VFBSor69PoVBIhmEkTy776OOxypvBWzyXw3mkMrUXwzBs95KpR7e5vReM
      Llv/Y0+fPq0HHnhAx48fl3TpDzjSG8JMmDBB9fX1GjdunI4ePar7779f48ePV21trUpLSzVx
      4sSM/aACwFhkKwBuv/12/cM//IPa29vV09OjeDw+4ltCfuYzn9Gf/umfqqWlRUuWLFEkEpEk
      FRYWqqenR5/97GftVw8AuG62AiAYDOrZZ5/V6dOnlZWVpdLSUltvNn78+I/tNJ40aZImTZpk
      6+cAAD69EQXA8PCw/v7v//6a33vmmWcYxwOQEq/87P/p9LnMuwdJeUmBHv/87ekuw7YRBYDf
      79eaNWucrgUAfqPT57p07PT5dJcxZtgaArIsSy+99JJOnToly7J077336qGHHnKqNgCAg2xf
      CmLixIl68sknlUgk9Pzzz6u7u1v5+flO1QcAcIit4y7D4bCCwaCkS1cDzc/P50JwAHCDsrUT
      2OPxqKmpSbt27dLQ0JCGh4f12GOPOV0jAMAB7AQGAJeytQ8gFovp+eefV0dHhwzDUFZWlr79
      7W8zDAQANyDbO4Grqqp06tQpPfroo1q3bl3GXmgKADJZJpzTYCsAioqKdOzYMU2cOFHvvfee
      JKm/v58TwQDApkw4p8FWAEydOlWTJk2S1+vViy++qMmTJ4/4WkAAgMxi6zDQkydP6vTp0woE
      Avr617+ucePGqasr807LBgD8diPeAujo6NCGDRs0NDSkkpISSZcCYf78+Y4VBwBwzogDoKio
      SMuXL9eHH36oiooKeTweFRYWKisry8n6AAAOsTUENHXqVN199906fvy4+vv7WfgDwA3sum7B
      1dPTw708AeAGZ+sooJaWFnk8Hj3wwAPyZej9XwEAI2NrC6C3t1f79u1TdnY2AQAANzhbS/Hc
      3FzV1tZq3759CofDkkZ+R7CTJ0/qyJEjyeczZ87UTTfdpJ07d6qnp0c1NTUqKiqyWT4A4HrZ
      CoDJkydry5Yt1/VGpaWlKigokCRt375diURCu3fvVjAY1OzZs7VhwwZ95Stfua6fDQCwz1YA
      JBIJvf766zp69Kgsy9IDDzygmpqaEb02GAwqGAxqcHBQHR0d+r3f+z299dZbevzxx5WdnS3L
      shSPxxlaAoAUsbW0PX78uLKysrR27VolEgl997vf1d13350cDhqJ+vp63X333fJ4PIrFYsnh
      o0AgoGg0qmAwmLzAnGmaisfjdkq84ZgZ3J/dI70ytRfLsmz3kqkXOaQXehlNtgJgYGAgOU7v
      8Xg0btw4maY54tfH43EdPnxYX//61yVJ+fn56u7uVkFBgfr6+hQKhWQYhrxeryRd8Xis8mbw
      Fo/di/xlai+GYdjuxTCu6whpx9ELvYwmW/9j77jjDq1Zs0Zvv/22TNNUZWWl8vLyRvz6gwcP
      6rbbbksO8/zBH/yBamtrVVpaqokTJ2bELwQA3GJEATA0NKTt27erqqpKTzzxhPLz8+X1em2P
      15eXl6uwsDD5fMaMGSosLFRPT48++9nP2qscAPCpjHgJfuDAAV28eFGmaWratGnJ6ffcc8+I
      h2kuX0TuoyZNmqRJkyaNtAwAwCgZ0ZhLVlaWvvrVryoYDGpoaEjRaDT5lUgknK4RAOCAEW8B
      TJw4UY888oiGhoa4AxgAjAEj3uuaSCTk8XhY+APAGDGiABgYGNCrr76qs2fP6tSpU07XBABI
      gRENAQUCATU1NenMmTNKJBJX7LRduXKl/H6/YwUCAJwxogDwer1as2aNjh07pg8//FCVlZVX
      fA8AcOMZ8T4Ar9erW2+9VRUVFdq+fbt27dqlrKwsTt4CgBuUraV3W1ubXn/9dc2fP1933HGH
      1q5dO+av1QMAY5WtU3lPnjypz33uc8kTwWbNmqWLFy+quLjYkeIAAM6xFQAzZ87Ud77zHfX0
      9CgajepXv/qVli1b5lRtAAAH2RoCKigo0N/93d/JNE3l5ubq6aeflsfjcao2AICDbF+/t7Cw
      UPfff78TtQAAUohDeADApWwFwPHjx9XQ0JB8vnXrVnV2do56UQAA5414COjChQv60Y9+pIsX
      L2r8+PGSLl0i4sEHH3SsOACAc0YcAOPHj9dXv/pV9fX1qaKiQpLYAQwANzBbO4GLi4u1Y8cO
      /ehHP0pO+5u/+RtlZWWNemEAAGfZCoCGhgYFAgF985vfTK79270QXDQa1cDAgAoLC+XxeDQ8
      PKxYLKacnBxbPwcA8OnYCoBQKKTCwsLrvifArl271NjYqJKSEj300ENqbm7Wli1blJ+fr/Ly
      ctXU1FzXzwUA2GcrAPLy8vTKK69o9+7dys7OliQ988wzIwqE7u5unThxQl/5yleSWw91dXVa
      vny5IpGI1q5dqwULFrBfAQBSxFYATJ48WT/5yU+u643Onj2raDSqH/7wh4pGo1q2bJk6OztV
      XFwsj8ejcDiswcFBBQIBWZYlSTJNc8xfbM7M4P5isZit+TO1F8uybPdy+TOYaeiFXkaTrQD4
      4IMP9PLLL0uS4vG4srOz9fTTT49oJ7BlWaqoqNDDDz+sgwcP6t1335XP55NpmjIMQ6Zpyuv1
      Jr8kyTCMMX+/Aa/P9snYKWN3qC9TezEMw3YvmXqZc3qhl9Fk63/slClTtGbNGkmXzgFYv379
      iJuYPHmyfvnLXyqRSKi/v1+BQEBlZWVqbGzUTTfdlFz7BwCkxnWvsoVCIeXn56urqyt5Ythv
      Mn78eFVWVurll19WOBzWkiVLNDw8rNraWtXX12vhwoXXWwoA4DrYHgJav369pEtDOqFQSEVF
      RSN+fVVVlaqqqpLP/X6/VqxYYacEAMAosT0E9NxzzymRSHC0DgDc4GwFgGVZ+ud//me1tbXJ
      sizdcccd3BAGAG5Qts8EnjFjhv7qr/5KkvTiiy+qu7tb+fn5jhQHAHCOreOQvF6votFo8rll
      WfJl6KF/AIDfzNbS+9Zbb9W+ffuSWwDz589XOBx2pDAAgLNGHABnz57VpEmT9NRTTyWnXbhw
      wZGiAADOG/EQ0GuvvfaxaRs2bFB/f/9o1gMASJERB4DP5/vYeH92drYGBgZGvSgAgPNGHABT
      p07Vjh07lEgkJF0a/jl27NiIzgIGAGSeEe8DePjhh/Xqq68mL+eclZWlr33ta5wQBgA3qBEH
      gGEYWrlypZO1AABSKP3XIwUApAUBAAAuRQAAgEsRAADgUgQAALgUAQAALkUAAIBLpfRaztFo
      NHkmcTAYlCQNDw8rFospJycnlaUAgOulLAASiYS+853vaOrUqZKkL3/5yzpz5oy2bNmi/Px8
      lZeXq6amJlXlAIDrpSwAYrGYysvL9dhjjyWn1dXVafny5YpEIlq7dq0WLFjApSUAIEVSFgBD
      Q0O6cOGCvve97yk3N1cPP/ywOjs7VVxcLI/Ho3A4rMHBQQUCAVmWJUkyTVPxeDxVJaaFmcH9
      xWIxW/Nnai+WZdnu5fJnMNPQC72MppQFQF5enr75zW9Kkt544w0dOXJEPp9PpmnKMAyZpimv
      15v8ki5df+jy47HKm8G31AwEArbmz9ReDMOw3YthZObxEfRCL6MpZRXEYjGZpinp0o7frKws
      lZWVqbGxUdFoNLn2DwBIjZStsnV2dmrr1q3y+XyaNGmSZs6cqZtuukm1tbWqr6/XwoULU1UK
      AEApDICSkpIr7icsSaFQSCtWrEhVCQCAj0j/IBQAIC0IAABwKQIAAFyKAAAAlyIAAMClCAAA
      cCkCAABcigAAAJciAADApQgAAHApAgAAXIoAAACXIgAAwKUIAABwKQIAAFyKAAAAlyIAAMCl
      CAAAcKm0BEB/f3/y8fDwsPr6+tJRBgC4WsoD4PDhw/rud78rSfrggw/0wgsvaOPGjdq1a1eq
      SwEAV0tpAHR1dendd99VJBKRJNXV1Wn58uVauXKljhw5okQikcpyAMDVfKl6o0QiodraWj38
      8MPasGGDJKmzs1PFxcXyeDwKh8MaHBxUIBCQZVmSJNM0FY/HU1ViWpgZ3F8sFrM1f6b2YlmW
      7V4ufwYzDb3Qy2hKWQDs2bNHubm56ujoUH9/v1paWuTz+WSapgzDkGma8nq9yS9JMgwj+Xis
      8vpS9iewLRAI2Jo/U3sxDMN2L4aRmcdH0Au9jKaUVRCJRBSJRPTrX/9a0WhU7e3tKisrU2Nj
      o6LRaHLtHwCQGilbZausrFRlZaUkqbu7W7Nnz9a0adNUW1ur+vp6LVy4MFWlAACUwgD4qMWL
      F0uSQqGQVqxYkY4SAMD10j8IBQBICwIAAFyKAAAAlyIAAMClCAAAcCkCAABcigAAAJciAADA
      pQgAAHApAgAAXIoAAACXIgAAwKUIAABwKQIAAFyKAAAAlyIAAMClCAAAcCkCANhSpk0AAAX8
      SURBVABcKmW3hDx//rzeeOMNJRIJBYNBLV26VL29vdq8ebPi8biqq6uT9wwGADgvZQFQUFCg
      ZcuWKRQK6bXXXlNra6v279+v+fPnq7y8XP/yL/9CAABACqUsALKysnT+/Hm9++676u3tVSQS
      UWtrqxYtWiSfz6dgMKhYLCa/369EIiFJsiwr+Xissiwr3SV8ong8bmv+TO0lkUjY7iVTP3f0
      Qi+jKeX7AHw+n+LxuC5evCjLsmQYl0owDEOmaaa6HABwrZRtAUhSaWmpSktLNTg4qNOnTysS
      iaitrU0lJSXq7e1VMBiUx+O54jVerzeVJabc5QDMRD6fvY9Hpvbi8Xhs93L15zBT0Au9jKaU
      BcCRI0d04MABZWdnq6urS3/2Z3+mKVOmaOPGjQoEApozZ05G/EIAwC1SFgCzZ8/WjBkzZFmW
      AoGAJCknJ0erV69WPB6X3+9PVSkAAKV4COhaC3mPx8PCHwDSIDMHbQEAjiMAAMClCAAAcCkC
      AABcigAAAJciAADApQgAAHApAgAAXIoAAACXIgAAwKUIAABwKQIAAFyKAAAAlyIAAMClCAAA
      cCkCAABcigAAAJciAADApVJ2S8iuri5t3bpVw8PDys/P1xe/+EV1d3dr8+bNisfjqq6uVmVl
      ZarKAQDXS9kWQDgc1qJFi/Tnf/7n6u/vV1tbm3bt2qX58+dr1apV2r59e6pKAQAohVsAfr9f
      hYWFsixLnZ2dKigoUGtrqxYtWiSfz6dgMKhYLCa/369EIiFJsiwr+Xissiwr3SV8ong8bmv+
      TO0lkUjY7iVTP3f0Qi+jKaX7ABKJhGpra3XvvfcqGAzKsiwZxqUSDMOQaZqpLAcAXC1lWwCJ
      REL/+Z//qcmTJ+uuu+6SJEUiEbW1tamkpES9vb0KBoPyeDxXvM7r9aaqxLS4HICZyOez9/HI
      1F48Ho/tXq7+HGYKeqGX0ZSyADh06JD279+viooKHT16VPfcc4+qq6u1ceNGBQIBzZkzJyN+
      IQDgFikLgDlz5mjOnDkfm7569WrF43H5/f5UlQIAUAacB+DxeFj4A0AapD0AAADpQQAAgEsR
      AADgUgQAALgUAQAALkUAAIBLEQAA4FIEAAC4FAEAAC5FAACASxEAAOBSBAAAuBQBAAAuRQAA
      gEsRAADgUgQAALgUAQAALpXyAHj//fcVj8eTz5uamtTQ0KDh4eFUlwIArpayALAsSz/+8Y+1
      fv16DQwMSLp0o/i9e/equ7tbmzZtSlUpAAClMAA8Ho/mzp2r6dOnJ6cdPnxYixcv1rx589Te
      3i7LslJVDgC4ni9Vb+TxeFRWVqZEIpGc1tfXp9zcXElSKBTSwMCAgsFgMghM07xiuGgsMjO4
      v1gsZmv+TO3FsizbvWTqygi90MtoSlkAXEswGFQ0Gk3+m52dLa/XK6/XK0kyDCP5eKzy+tL6
      J/iNAoGArfkztRfDMGz3YhiZeXwEvdDLaEprBTNmzFBdXZ2amprk9Xrly9AFCACMRSkPgNtu
      uy2ZlFVVVQqHwzpx4oSWL1+e6lIAwNVSvsp92223JR97PB5VVVWlugQAgDgRDABciwAAAJci
      AADApQgAAHApAgAAXIoAAACXIgAAwKUIAABwKQIAAFyKAAAAlyIAAMClCAAAcCkCAABcKq0X
      4O/q6tLmzZsVj8dVXV2tysrKdJYDAK6S1i2AXbt2af78+Vq1apW2b9+ezlIAwHXSGgCtra2a
      OnWqsrKyFAwGbd9TEwBw/dIaAJZlJe+LaRiGTNNMZzkA4CppDYBIJKK2tjYlEgn19vYqGAym
      sxwAcJW07gSurq7Wxo0bFQgENGfOHHk8nnSWAwCuktYAKCkp0erVqxWPx+X3+0f8uk1vHdPm
      t445WNn1e+PZR9JdAgCMSNrPA/B4PLYW/gCA0eFJJBKJdBfxUaZpyrIsSVIikbjmsNCHF/t1
      /mJ/qksbkZkVEVvz04vzwtl+VUwqtPWaU60X1R8ddqii60cv9DKaMi4APso0TXm93nSX8ald
      PrqJXjLL8PDwmNn6pJfMlOm9/H8osZvPCtfK2QAAAABJRU5ErkJggg==
    </thumbnail>
    <thumbnail height='384' name='Urban Transit Trips' width='384'>
      iVBORw0KGgoAAAANSUhEUgAAAYAAAAGACAYAAACkx7W/AAAACXBIWXMAAA7DAAAOwwHHb6hk
      AAAgAElEQVR4nO3deXRUZZ4+8OcutaRSIQtJiAHCqhCWbhlAQXEZMW49dreedqFttdVDi9O/
      cRztg920M8rYtuM2fXrUgW67nYN41HZpZxjEBdTRGRVQUVbBBJAkkqSSELJX1V3e3x9pKqlU
      hLovdUPCfT7ncEyVdd/3e9966z51762qqwghBIiIyHPUE10AERGdGAwAIiKPYgAQEXkUA4CI
      yKMYAEQ0qIQQ6PvZk/63AaCurg62bR93H33/Ha9vamegvo61fk76+/rrr9Nuz2lfDAAiGlQr
      V65EU1NT4vYDDzyQ8phHHnkE0WhUuo9ly5bhH/7hH/CTn/wES5cuxccffyzd1hGrV69GVVUV
      Ghsb0dzcnLh/z549WLp0KW677TbcfvvtWLZsGdra2hL/3zAM/OpXv3LUlxACTzzxBBoaGlKW
      7e7uxoMPPjjgcjt37sQLL7yQdgjojqoiIjpO9fX1ME0zcbu6uhoA8Omnn6Krqwt+vx+KomDH
      jh2oqanBggULMGrUKFRXV2PTpk3Iy8vDwoULUVdXh8OHD+OLL75ASUkJzj77bCiKAgB48MEH
      sXfvXrz00kv4+c9/DiEEPv30U7S2tiI/Px8lJSX48MMPkZWVhYsuuggtLS1oaGhAZWUlcnNz
      cf7556OjowMbNmwAAJx//vmYM2cO8vLy8O///u/Iz8/HxRdfjClTpmDq1Kl45JFH8Mc//hHT
      p0/HvHnzUF9fj4aGBmzduhXnnnsuLrroIliWha1bt+Lw4cPo6OhARUUF/H4/3n//fdTX12PW
      rFmYOnUqAKCqqgrNzc0oKSmBoij46KOP0NjYiAsuuADBYBAXXnghDMPA9u3b0dLSgra2NlRU
      VGDatGlYuXIlLrnkEuTn5x/zueAeABENCffeey+qq6tRWloKIQQ+/vhjlJaWYtmyZTBNEw0N
      DZg8eTI+/PBDbNq0CTt37sRjjz2GyZMn489//jO++OKLb2xbCIG7774bzc3NKC4uRiQSwbhx
      47B7926sX78e+/fvx69+9SuUlZXhvffew8aNG/HEE08gHA6jpKQElmXh7bffxsGDB6HrOkpL
      S4+6gd2zZw/uu+8+lJSUwOfz4fnnn4dhGFi+fDni8Tii0SgeeeQRfPTRR9i0aROmTZuWFIof
      ffQRzj77bABAJBLBwYMHEQgEcP/99yMajeLFF19ENBrFvffeC9u2EYvF8Ic//AGqqmLWrFnY
      unVrWmPOACCiQXfkEEXfQxXFxcVYtGgRxo4dCwBYtGgR5s+fj1NOOQWRSATRaBRvv/120iGY
      iy66CKeffnriXffRlJWV4aqrrkJpaSkMw8B7772Hr7/+OtHW+eefj9mzZ+Pss89GfX09zjnn
      HLz66qtoampCQUEBAEDXdYwaNQoTJkxAcXHxUfurqKjAOeecg1AolLhv9OjRuOSSS3DFFVeg
      trYWp512Gmpra/HBBx9g9OjRicdFIhGUlpYCAAoKCvC9730Pl1xyCbq6upIOjU2YMAEVFRW4
      4IILcOjQIQBAaWkpIpHIUWs7ggFARINq5MiRqKmpgRACjY2NyM7OBgAEAoHEIRwAaG1thRAC
      ra2t6OzsxHPPPYe/+7u/w7x58xKP8fv9afcbDAYBAPF4HL/97W/xt3/7t/jrv/7rlLaO1LBg
      wQL85je/wd69e7Fu3bqktgzDOGZ/WVlZKfd1dHTAMAxEo1EoioKioiL827/9G2bOnImHH344
      8bhAIICuri4AQCwWQzQahWEYiMfj0HU96XF9awaArq6uxP3HwnMARDSorrrqKtx///1Ys2YN
      Dh06hNtuuw2KokDTtMRjwuEwnnrqKQDA+PHjMWbMGMTjcTz00EOIRCK47LLLoChKYsOnqmrS
      RhDo2Siqau973CPt+3w+ZGdn4+GHH8bhw4cxZ86cpMceaffZZ59FdXU16uvrUVFRgerqaiiK
      gunTp+Ppp59GLBbDeeedl9TfkRr6/n2kPqAnfO677z4cPnwYV155JT7//HO8+uqrsCwL06dP
      Tzy+vLwc27Ztw9y5cxEKhfAv//Iv6O7uxsKFC+Hz+aBpWsr6Hfl7+/btWLRoUVrPhcLfAiKi
      E6G7uxvBYDBlw32EEAKxWCzxzt22bcTj8cTt49G/7W96TFdXF4LBYFI4AUA0GoWu60nvxo8l
      Go3i5z//OR577DHYtg2fz5e4X1GUpHftXV1dWLp0KR5++GGEQiHYtg3TNI+5x1NfX49HHnkE
      Dz/8cErNAxkyASCE+MaJQEQ03MViMfz617/G8uXL03r8wYMHEQ6HMWLEiLT7aGxshKqqGDly
      ZFqPHzIBYFlWWolFRESZwZPAREQexQAgIvIoBgARkUcxAIiIPIoBQETkUQwAIiKPYgAQEXkU
      A4CIyKMYAEREHsUAICLyKAYAEZFHMQCIiDyKAUBE5FEMACIij3LtimBCiMT1Po/8zn/fX57u
      f8UcIiIaXK4FwLvvvoudO3dC13WcddZZmDFjBlauXAlVVaHrOq677rqkiyUTEdHgci0AIpEI
      Fi1ahMLCQgA9V8Px+/1YvHixW10SEZEDrgVAV1cX3njjDeTk5ODcc89FIBBAXV0dXnjhBYwZ
      MwZnnXUWFEWBbdsAeq73OUQuTkbkqrhpQWaq+3QVKg+bUga5dknIzs5OCCFw4MABvP/++1iy
      ZAlaW1uhqir++7//G+Xl5Zg1a1ZSAPS9wj3Ryeru372NytpDjpf7159WYHxJngsVkVe5sgcg
      hEBWVhZUVcX48eOxfv16AEBubi4AYOzYsejs7ISiKEnXAeY1gckLZD/8oKoqXyOUUa4dAnr+
      +edhGAba2tpw4YUXoqGhAa+88gpCoRBisRiuv/56t7omIqI0uBIAiqLghz/8IWKxGAKBQOId
      z5IlS2BZFvx+vxvdEhGRA67tASiKgmAwmHSfpmnchSUiGiJ41pWIyKNc2wMgouHjpXd3or07
      7ni5ay+YgVDQ50JFmbPlyzp8XlXveLm5U0oxc9IoFyoaOhgARIS3PtmHSEun4+WuOGfqkA+A
      3dVN+K//2+N4ubxw8KQPAB4CIiLyKAYAEZFHMQCIiDyKAUBE5FEMACIij2IAEBF5FAOAiMij
      GABERB7FACAi8igGABGRRzEAiIg8igFARORRDAAiIo9iABAReRQDgIjIoxgAREQexQAgIvIo
      BgARkUcxAIiIPIoBQETkUa5dFL66uho1NTUAgAkTJqC0tBQNDQ3YtWsXJk6ciHHjxrnVNRER
      pcG1PYCPP/4YpmkiOzsbPp8P0WgUzz77LEaNGoW1a9eioaHBra6JiCgNru0BdHV1oaysDCNH
      jkROTg4qKytx6qmnYtq0aeju7sauXbswatQot7onIqJjcC0ATj31VGzbtg11dXWYNGkSCgsL
      kZ2dDQAIh8M4ePAghBAwTRMAYNt24m+ioab+UCfWflTpeLnRRTm49IxJSfcJ25aqwTAMxGIx
      qWWPRQghtVw8Hkcs1nsg4YsDTfhgR63jdmZOLMaZ5aVSNRyLZVlSy5mm6dp4DxWuBcC8efMA
      ALFYDE8++SQuv/zyxDmBjo4OhEIhKIoCn88HoOdJ0jTNrXKIjktHrA1vfbLf8XKnTx6F758z
      Lek+RZU78urz+RAIBKSWPRZFUaSW8/v9STUdPNQlNU5ZQT/OPX2CVA3HIrtd0XXdtfEeKlw5
      ByCEwJYtW3DgwAF8+OGHOOWUU1BWVoZ9+/Zhz5492LhxI8rLy93omoiI0uTaSeBAIIBdu3Yh
      OzsbP/jBDxAIBLBo0SLU1NTg4osvRmmpO7t7RESUHlcOASmKgunTp2P69OlJ948aNYonfomI
      hgh+EYyIyKMYAEREHsUAICLyKAYAEZFHMQCIiDyKAUBE5FEMACIij2IAEBF5FAOAiMijGABE
      RB7FACAi8igGABGRRzEAiIg8igFARORRrl0RjIiIBvY/n32FHfsjjpdbOHsiyscVZqwOBgAR
      0SDbdaAR6z/Z53i58vFFGQ0AHgIiIvIoBgARkUcxAIiIPIoBQETkUTwJTDRMxeIm2rpijpcL
      +HWMCAVcqIiGGwYA0TD16Zd1eOi5Dxwvd97p43Dn1fNdqIiGGx4CIiLyKNf3AEzThKqqUBQF
      lmUl7tc0DYqiuN09ERF9A1cDwDAMPPnkk6ioqMCUKVPw+OOPIzs7G5qmYdGiRQiFQm52T0RE
      R+FaAAghsH79evj9fpimCcuyEA6HsXjxYre6JCIiB1wLgH379qG9vR3Tpk0DANi2jYaGBqxe
      vRpFRUW48MILoWla4rCQbduwbdutcsij2jpjiJvWsR/Yz4jsAPy6lrhtmaZU/7YQMAwj6T4h
      hFRbpmkmtWVK1iRsO6UmSNZkGMk19T3M64Q9QE1Nrd0AnNdVmJt8ZMG25LYrlmWljlOGCMlt
      nWUm12TZNlrao47b0VQV+TlBdwIgGo1izZo1uOqqq7Bt2zboug6fz4c777wTiqJg3bp12LJl
      C+bOnQtV7T0P3fdvokxYsWYLPt590PFy9/74XJw+uSRxW3ZuKlCgaVq/++RompbUVv92065J
      UVOXlTwf178m6XFSUsfp9sffRNxwHih/vv+qpPOLiiq3bqo6wDhliKLIjVP/mhpbu3Hbv77u
      uJ2y4hH47e2XuBcApaWl+PDDD1FbW4tgMIjp06djxIgRUFUVeXl5ME0TiqIknighBAOAMk72
      cwaKoiTNR0V6wzbARlGyqJSapFcuc2+2VDUzNfVfN0A+KBVVhdo3ADJYU8bIPnUZGm+gpx1X
      AiAvLw/XXHMNAGDTpk0YMWIEDMPA7373OwQCAei6nvj/RER0Yrj+MdAzzzwz8feSJUtg2zZ0
      nd8/IyI60QZ1S6yqKg/zEBENEdwaExF5FAOAiMijGABERB7FACAi8igGABGRRzEAiIg8igFA
      RORRDAAiIo9iABAReRQDgIjIoxgAREQexQAgIvIoBgARkUcxAIiIPIoBQETkUQwAIiKPYgAQ
      EXkUA4CIyKMYAEREHsUAICLyqEG9KDwR0XAVjZu49dG1jpcbEfLj8Tsuc6Gi48cAICJKgxAC
      hzuijpezhXChmszgISAiIo9yNQBs28bLL7+MvXv3QgiB//3f/8VTTz2FdevWwbZtN7smIqJj
      cDUAPvnkE1RWVqKjowMtLS3Ytm0bbrzxRnR2duLLL790s2siIjoG184BNDU1Ydu2bTjzzDMB
      AAcPHsTkyZPh9/tRXl6OAwcOYMqUKRB/OT4mhIBlWW6VQx4le/hV2MnzUUjusQqB1HktWZTd
      rybpveiBXmuS42TbdvI4Sa7bQK9/2SPntm0nLStdU8bGO3NzQPQbb9uWbAc96+ZKAFiWhf/8
      z//EFVdcgR07diTu0zQNAKDrOgzDANA7qLZtQ1EUN8ohD5N98dvCTnrBy774BUTKssezYevb
      lvzGdqD1kQ2lDI2TSB0nWbZlo++m5ETPAQw0B44jKDNTU8+yrgRAS0sLWltb8eKLL6KpqQl+
      vx/f+c530NjYCACIRCIoKCiAoijQ9Z4S+gYEUaaoqtybCk3T4PP5em/rci8VVVGS2gEg/UZH
      1/vVJPl6UdTUmiBdk56RmlRVTR0nqZYA3adD7bM+qip3pLv/HDBlt7UDzQHJmtR+Nem65BxA
      T02uBEBhYSHuuusuAMB7772HvLw8lJeXY/PmzXjhhRfQ0NCAW265xY2uiYgoTa5/D2D+/PlQ
      VRWapuHGG29Ea2srwuEw/H6/210TEdFRuB4AfTf0mqahoKDA7S6JiCgN/CIYEZFHMQCIiDyK
      AUBE5FEMACIij2IAEBF5FAOAiMijGABERB7FACAi8iheEYyGpP/3m3Vokbj60u9/9jfIzuK3
      zInSwQCgIakjGkdHd9zxckP34ntEQw8PAREReRQDgIjIoxgAREQexQAgIvIoBgARkUcxAIiI
      PIoBQETkUQwAIiKPYgAQEXkUA4CIyKMYAEREHsUAICLyKAYAEZFHMQCIiDzKlZ+DFkJgw4YN
      OHDgAGzbxqWXXorS0lKsWrUKpmlC13Vce+21CIVCbnRPRERpcO16AN/+9rdxwQUXoKamBu+8
      8w4WLVoE0zRx0003QVEUaJrmVtdERJQGVwJAURTk5+dj586d2Lp1K8rLy2GaJiKRCF5//XWU
      lZXh29/+NoQQsG0bABL/JToelmXBsqzEbSF5hRjbFkntyM5PIZDUTs99ckXZtp2hmkRKTbID
      ZVnu1SR7cR/bsiAUJaltqXb6jbdlSW6jMjgHRKbmAHrG27U9ANu20dbWBlVV0dTUhGAwiJtv
      vhmqquK1116DqqqYOXNmb0FCSA8K0REp80j2hdavHfm5mbl5nbGaxPGsz9FrOtHtHGnraLdl
      28roHJB96jI2L3uWdRQAjY2NiEQimDx5Mh5//HGMHTsW11xzzYCPDQQCWLBgAWbPno0nn3wS
      l112GUpLSwEA5eXlaGlpSTkUxMNCdLx0XYeu905rRVWO8uhvpmlqUjuyc1NRlKR2jtwnV5OW
      mZrU1JogWZOuJ9ekqnKfK1FVNXWcpFoCNF2H2md9MlWTbklubAeaA5LzUs3UHEBPTY4CYNeu
      XcjJycEbb7yBM844A7t370ZHRwfC4XDS4wzDwDPPPINwOIzm5maceeaZqKurw9q1axEOh9HY
      2Igbb7xRqnAiIsoMRwFQUlKCt956C5FIBMuXL8euXbsG3AXRdR3XX389urq6kJWVBb/fDwC4
      7rrrYJomsrOz+W6fiOgEcxQAp512GmKxGEpLS6GqKhYuXJjy7h/o2cX1+/2JDf8R/NgnEdHQ
      kVYAxGIxPPbYYwB6Nu5H3vWHQiH89Kc/hc/nc69CIiJyRVoB4Pf7cfvtt6Ourg7vvvsurrzy
      SsTjcbzwwgvSJ1iIiOjESisAFEVBOBxGbW0tTj/9dBQWFgIAcnJy0NraioKCAleLJCKizHN0
      DuCv/uqvsHz5cnzwwQeIRqMAgPz8fFcKIyIidzkKACEE/umf/gkdHR3w+/0oLCyU/kwzERGd
      WI4CYPfu3ejs7MTChQvdqoeIiAaJowCYOHEifvnLX6KyshKhUAiapuGaa65J/VYhERENeY62
      3NnZ2bjrrrsSt1VV5aeAiIiGKUcBEAqF0NjYiPXr1yMYDOLqq69mABARDVOOtt5VVVX4/PPP
      cffdd2Px4sVYtWoV4vG4W7UREZGLHO0B1NbWYt68ecjOzkZ2djYmTJiArq6ulJ98ICKioc9R
      AMyZMwfLly/Hzp070dnZiXg8jtzcXLdqIyIiFzkKgHA4jAceeCDxKaDx48fzewBERMOUo3MA
      n3zyCTZt2oQZM2Zg4sSJWLFiBbq6utyqjYiIXOQoAAzDgGEYAHq+FRyPx2GapiuFERGRuxwd
      Apo7dy4efPBBbNiwAaZpory8HDk5OW7VRkRELnIUAD6fD/fccw8ikQhGjhwJXdd5DoCIaJhy
      dAiovb0dd999N5YuXQoA+P3vf584JERERMOL45PAP/jBD3DWWWcB6Ln2b1tbmyuFERGRuxwF
      wKRJk7BhwwY0NjZiy5Yt+PLLL3k9ACKiYcpRAIwdOxZXX301TjnlFFRWVuLuu+/mbwEREQ1T
      aW+9u7u78fLLL2P//v245ZZb8N3vfherV69GLBZzsz4iInJJ2p8CWrVqFcaMGYP29nbcf//9
      aG9vx49//GP+DhAR0TCVdgC0tLTg1ltvhWma2LZtGx566KGjHv5pb29HVVUV/H4/pk6dCk3T
      0NraiqqqKpSVlaGoqCgjK0BERHLSPgTU1dWFJ554AitWrMD+/fvx5JNPYsWKFQN+DNS2bbzz
      zjswDAN79uzBunXrEI/H8Yc//AGGYeC5557DoUOHMroiRETkTNp7AHfccQe6u7sBAFdeeSUA
      QFEUaJqW8lhVVfG9730PAJCTk4PPP/8c1dXVmDhxIubNmwdVVbF9+3acd955mVgHIiKSkHYA
      jBw50lHDnZ2deOaZZ/DVV1/hJz/5Cdra2jBixAgAQG5uLhoaGiCESPyWkG3b/F0h6iXkFovH
      44ipvQvbti3VjmGYSR9wkP3Co22LlA9KCOmajMzUZNmpNQm5AY/H44jFeg8kyL6GLctKrUmq
      JSAei6PvDxRYliXVjmkmz4F4XHL7JFLngG3JzQGz/7yMy80BIXrmQFoBYNs2amtrUVZWlnYH
      oVAIS5YsQXNzM5599llccskliT2I7u5uBINBKIoCn88HoOdJGmhvgjxK8hdG/H4/AoHeDybI
      fkzZ59MRCAT63PZJtaOqSlI7AKBI1+TLTE2amlqT5E+69Ix3b1u67ujXZRI0TUutSaolwB/w
      Q+2zPrLbFV1PngO2s0/N91JS54CqybWl95+XfrkrMipKzxxIq4q2tja8+eab2LJlC7Zv337M
      x1uWhS+++AKHDx9GbW0tgsEgxo0bh8rKStTV1WHTpk047bTTpAonIqLMSCuuc3NzYVkWnnrq
      KSiKkvj2r67r+OUvf5nyUVBFUdDY2IgdO3YgPz8f119/PYLBIK688kps3LgR8+fPd7Q3QURE
      mZdWACiKgltvvRUtLS3o7OzEmDFjjvp4VVUHPME7btw4jBs3Tq5SIiLKqLQP2B1557927Vps
      27YNAPDd734X5557rmvFERGRexydsfn8888RCoXw6KOPwrIsPPTQQ5g7dy6ysrLcqo+IiFzi
      6FR0Z2cniouLIYSAqqooKCjgRzeJiIYpR3sAZ5xxBh544AGsX78ehmFg5syZvCQkEdEw5SgA
      /H4/7rvvPnR3d8Pn80l/DpmIiE48x9/aUBQFoVDIjVqIiGgQOToH0NLSwktAEhGdJBwFwJ49
      e7B582a3aiEiokHk6BDQxIkT8Ytf/AJ79uxBKBSCpmn44Q9/KP37H0REdOI42nKHw2H84he/
      SNxWFIXXBCYiGqYcbb2DwSA2b96MFStWYPz48aiurpb+FUEiIjqxHAXAZ599BlVVMXnyZAgh
      UFVVhcOHD7tVGxERuchRANi2jXA4DEVRYFkW6urqEAwG3aqNiIhc5CgAZs+ejfr6emzevBnL
      li3DggUL+DtARETDlKOTwKqq4oYbbsA555yDYDCIsWPHulUXERG5zFEAtLS04J//+Z8xbdo0
      dHV1obW1Ff/4j//IE8FERMOQowDYunUrfvSjH2H27NkAgKeffhotLS0oKChwpTgiInJP2heF
      b2pqQlFREd59910UFRWhu7sbhw8fRm5urts1EhGRC9IKAMMw8Pzzzyduv/rqqwCArKwsCCHc
      qYyIiFyVVgAEAgH8/d//PYQQiEajaGlpAcBvAhMRDWeOzgHU1tbi0UcfxamnngpFUaDrOm6+
      +WaGAAEA9h1swQfbqx0vN3l0AebP4CfKiAabowDYv38/rr/+esyZM8etemgYq4m04uX3vnC8
      3IVzJjIAiE4ARwEwe/ZsPPDAA9i7dy98Ph80TcN3vvMd/hooEdEw5GjLXVVVhcLCQkyaNAmK
      oiT+DUQIASFE4jEDnSzm9weIiE4cRwFgmiZmz56N2bNnH3XjLYTA22+/jX379sEwDFx++eUY
      PXo0Vq5cCVVVoes6rrvuOl5akojoBHIUAIWFhfj1r3+NtWvXIhAIQNd1LFu2DH6/P+lxiqJg
      1qxZWLhwIaqrq/H+++/jqquugt/vx+LFizO6AkREJMdRAJSVleHll19O67EjR46EEALV1dUo
      LS1N/Hron/70J4wZMwbz58+HoiiwbRtAz5fN+J2CE8Ow5Mbep6lJe4LWX55Lp4RtwzRNqWX7
      M00Tptn7qTRhy80p20quybYsqXaEECnrJjvPLctKasuSrclOrQmSNZlmck225BywB5gDslsD
      yzRh95mXmarJNOXGGwPNAel5maE5gJ6aHH8KaNWqVT2F2DaCwSCWLl0Kn8+X2oEQ2Lt3L3bt
      2oWbb74Zuq7j9ttvh6qqWLNmDbKysjBr1qykZXhO4MT4+e/ewVf1zq/r8PjtF2N00YjEbeln
      7yjnkpw31a8t2XaVTM1H99ZNut2MrdsA4y3bzlAcp37LyrczwLrJPnUZXjdHATB+/Hjcc889
      AID29nasXr16wO8ACCGwf/9+vP3227jhhhvg8/kghEj8bERZWRk6OjqgKAo0TUss1/dvGjyy
      c0jVtKTnTPb7IP3nwfHQ+tUkvW6qmrxukvUpSuq8ln3RptSUyfGWrEnTMlSTmlqT7KZN1TSo
      GdhI9h9vTZPbk0AG54CSqTmAnvF2/HPQRzrMz89HVlYW2tvbkZeXl/Q4y7KwatUq5Obm4umn
      n8aIESNQUVGBV155BaFQCNFoFDfccINU4URElBmOPwb6xz/+EUDPIaDc3NwBfwxO13UsX748
      5f4lS5bAsqyUk8ZERDT4HAXA5MmT8eCDD0p31n/3nIiITpy0A+BPf/oT9uzZk7itaRpisRju
      uecevqMnIhqG0g6Aq6++OvF3JBLBihUrMG3aNP4MBBHRMJX21ltRFFiWhddeew0bN27Ebbfd
      xmsCExENY2l/hujrr7/GvffeC9u2cf/992PMmDGJ3/shIqLhJ+09gJdeegmmaWLnzp3Yvn17
      z8K6jp/97GcDfhGMiIiGtrQD4I477nCzDiIiGmS8lBcRkUcxAIiIPIoBQETkUQwAIiKPYgAQ
      EXkUA4CIyKMYAEREHsUAICLyKAYAEZFHMQCIiDyKAUBE5FEMACIij2IAEBF5FAOAiMijGABE
      RB7FACAi8igGABGRR6V9RTAnhBBobW3Fjh07EAgEMGvWLOi6jkgkgl27dmHixIkoKytzo2si
      IkqTa3sAmzZtQl5eHhoaGvDWW28hGo1i9erVKCoqwpo1axCJRNzqmoiI0uDKHoCiKLj44osB
      AKFQCJs3b0Z1dTUmT56M6dOno7u7Gzt37kRxcbEb3RMRURpcCYAj4vE43nrrLVx++eWIRCII
      h8MAgJycHNTV1UEIAdM0AQC2bSf+pmM72NSONzbvc7zc2OIRqJgzIek+IYRUDcALGngAAAyM
      SURBVEY8jlgs1ntb8vmzLSupnZ6ipJpCPB5HTO1d2LZtqXYMw0xeN8OQase2Rcq6CemajMzU
      ZNmpNUnOgXg8jlis90CC7GvYGmAOSE4BxGNxKEpy2zJMM3kOxOOS2yeROgdsS24OmP3nZVxu
      DgjRMwdcCwDDMPDcc8/hrLPOwujRo9HZ2YmamhoAQEdHB0KhEBRFgc/nA9DzJGma5lY5J53W
      7ha88bHzAJg7tRR/c/bUpPuUvq8WB3x+PwKBQO9tXW46qZqW1E5PUVJNwe/3IxDw97atyh3l
      9Pn05HX7yzx1SlWVlHVTpGvyZaYmTU2tSXIO+PvNAV1yDmgDzAHJKQB/wA+1z/rIbld0PXkO
      2LJHzJXUOaBqcm3p/eelPy5ZUs8ccOUcgG3beOaZZ5CVlQVd11FZWYmysjLs27cPlZWV2Lhx
      I6ZOnXrshoiIyDWuBIAQAlOmTEFxcTEaGhrQ1NSEYDCIa6+9Fl999RUuuugijB492o2uiYgo
      Ta4cAtI0DQsWLEi5v6SkBCUlJW50SUREDvGLYEREHsUAICLyKAYAEZFHMQCIiDyKAUBE5FEM
      ACIij2IAEBF5FAOAiMijGABERB7FACAi8igGABGRRzEAiIg8igFARORRDAAiIo9iABAReRQD
      gIjIoxgAREQexQAgIvIoBgARkUcxAIiIPMqVi8LTN2vrjMEwLcfLjcgOwKdrLlRERF7FABhk
      //riR/isst7xcg8svgAzJhS7UBEReRUPAREReZSrewBCCAghoKoqhBCwrN5DH5qmQVEUN7sn
      IqKjcC0AotEoXnnlFRQUFODSSy+FZVl4/PHHkZ2dDU3TsGjRIoRCIbe6JyKiY3AtALZv347i
      4mK0t7cDACzLQjgcxuLFi93qkoiIHHAtAObOnYvq6mp88sknAADbttHQ0IBnn30WRUVFWLhw
      ITRNSxwWsm0btm27Vc6QYdtCajnLNGEYRp/bzj9J1NO/ndQO0HOoTobZvyYrczVBriQYhgFD
      7z20KD3eltVvvE2pdmwhXBtvU3K8hZ1aEyRrMgz35oDkFIBpGEmHl2W3K/3ngGHIzQEMNAck
      a7L71WRKzkuBnpoG7VNAwWAQd955JxRFwWuvvYYtW7Zg7ty5UNXe89B9/z5ZyZ72UFUNmtb7
      MVBVkxsrRVGS2gEABXJFaaqa1JYi+fypA9QkWRI0LXmcZM8zqf3WTXZuKhhovOVo/eeAbE0D
      jrfkHNBcrElS//OLmZoDmiYbSQPMAUV2nPrXJDdmyl+WHbQAsG0bgUAAqqoiPz8fxl9S+siT
      c+Rk8clOdjIqqpI0PtLtKErqOEtukRRVTQ5w2XQbqCZJar+a5EvqN97SG7YBNooZmgPy4525
      N1uqi/NSNigVVU0am0zVpKoZHG/p11xmxhvoacfVANB1PXGit7m5GS+99BKCwSA0TcO1117r
      ZtdERHQMrgZAaWkpSktLAQDFxcW47bbbYNs2dJ3fPyMiOtEGdUvcf/eciIhOHG6NiYg8igFA
      RORRDAAiIo9iABAReRQDgIjIoxgAREQexQAgIvIoBgARkUcxAIiIPIoBQETkUQwAIiKPYgAQ
      EXkUA4CIyKMYAEREHsUAICLyKAYAEZFHMQCIiDyKAUBE5FEMACIij2IAEBF5FAOAiMijGABE
      RB7FACAi8ihXA6C7uxstLS2J211dXfjyyy/R1tbmZrdERJQG1wKgtrYWTzzxBN59910AgGEY
      eOqpp3DgwAH8x3/8B1pbW93qmoiI0uBaABiGgcsuuyxxu6amBqNHj0ZFRQXOOOMMbN++3a2u
      iYgoDbpbDU+YMAHV1dWJ262trcjPzwcA5Ofno7KyEkIImKYJALBtO/H3ycy2banlDMNALBZL
      3DYNQ7r/vu0AgBBCrqZ4PKktQ/L5sy0rpSbIlYR4PI6Y2ruw/HibyesmPd4idbwzNAeka7Iy
      Nwfi8Thisd73kbKvYWuAOSA5BRCPxaEoyW3LMM3kORCPS26fROocsC25OWD2n5dxuTkgRM8c
      cC0A+gsGg4nCY7EYAoEAFEWBz+cD0PMkaZo2WOWcMKoqt9Pl8/kQCAQSt/W/jJtM/33bAQCl
      76vFSU1+f1JbPl1uOqmallIT5EqC3+9HIODvbVt6vPXkdZMebyV1vDM0B6Rr0jI3B/z95oAu
      OQe0AeaA5BSAP+CH2md9ZLcrup48B2zZAyZK6hxQNbm29P7z0h+XLKlnDgzap4DGjh2Lqqoq
      tLS04LPPPsOkSZMGq2siIhqAqwEQDocxceJEAEB2djYqKirw+uuv49RTT03cT0REJ4arh4AK
      CgpQUFAAoGcXs7y8HOXl5W52SUREaeIXwYiIPGrQTgIPZ+1dMfzftupjP7Cf3HAQZ80Y60JF
      RETHjwGQhua2bqxc86nj5U4dU8AAIKIhi4eAiIg8igFARORRDAAiIo9iABAReRQDgIjIoxgA
      REQexQAgIvIoBgARkUcxAIiIPIoBQETkUQwAIiKPYgAQEXkUA4CIyKMYAEREHsUAICLyKAYA
      EZFHMQCIiDzqpL4i2Gdf1qGmsc3xcnOmlKK0MMeFioiIho6TOgD+Z+sB/M9nXzlebuSILAYA
      EZ30eAiIiMijGABERB41aIeALMvCM888A9M0oWkarr32WoRCocHqnoiI+hnUADAMAzfddFNP
      x/pJffqBiGjIG7StsGmaiEQiePPNN1FWVoaZM2dCCAEhBADAtu2M93mkbads24ZlWUm35fpH
      UjtDoyYxQE1STQ1Qk1xDA9Uky7KspLbk1014ag7IDpRluTgvpVoCbMuCUJSktqXa6Tfeluw2
      KoNzQGRqDqBnvActAILBIG666Saoqop169ZBURTMmDEjsQJ9wyBjZAdZJD9BQnLDBmRunfqP
      j/QEGnBZ+cmYiZqQwec+ZR5Jz4FMjffQmwMQx7HsMWqS3Wpn8vVvC5F0cjNT4ySk36QOsG4Z
      GqfjGTMhxOAFgKqqGD16NACgvLwczc3NUBQlcSjIsixompbRPhVV7hy3pqlJh6g0Xa6uvuvX
      9z65mrTkmiTHSs1kTbrerya58VZUNWOHBPV+NSmq7HirQ2+8MzQHFDW1JkjWpOvJNamSc0Ad
      YA7IVdQzB9Q+66NKbgfUfnNAtyQ3tgPNAcl5qWZqDqCnpkELgPr6eqxduxbhcBiRSAQ33njj
      YHVNREQDGLQAGDVqFBYtWgTTNBEOhzP+bp+IiJwZtABQFAXZ2dmD1R0RER0DvwhGRORRDAAi
      Io9iABAReRQDgIjIoxgAREQexQAgIvIoBgARkUcxAIiIPIoBQETkUQwAIiKPYgAQEXkUA4CI
      yKMYAEREHsUAICLyKAYAEZFHMQCIiDyKAUBE5FEMACIijxq0S0IKIbBhwwZUVVWhuLgY3//+
      93ldYCKiE2jQ9gCam5uxb98+3HrrrdB1Hbt37x6sromIaACDtgdQX1+PCRMmQFVVTJ48GTU1
      NZg+fXrK4w53RHHLQ2sct1+UF8LKu/4mE6USEXnCoAWAZVlQ1Z4dDlVVYVnWNz7WtGzH7css
      Q0TkZYN2CKioqAgNDQ0AevYGCgsLB6trIiIawKAFQElJCWzbxtNPP43t27fjW9/61mB1TURE
      Axi0Q0CqquJHP/oRYrEY/H5/4nAQERGdGIMWAACgKAqCweBgdklERN9AEUKIE9W5EAKmaSb+
      VhQFhmlh11eNjtvy6RqmjS9Kuq+6oRUt7d2O2xpXkoe8cG9QdccMfFnT7LidrIAPp40dmXTf
      voMtaO+KOW5r0ugChLP8idvtXTHsO9jiuJ0R2QFMOCU/6b49Nc2IxgzHbU0pK0TQ3/seoqW9
      G9UNrY7bKRiRhbHFuUn37fwqAtN0fmJ/+oRi6Frv3uX+uha0dTof74ml+cgJBRK3O7rj2Pv1
      IcfthEN+TCotSLqvsrYZXVHn433a2JHICvgStw93RHGg/rDjdvJysjBuVPJ4f3GgEXHjmz+Y
      8U2mjS+CT+/9Pk9Taxe+bmxz3E5RXjZKC3OS7tu2twEym6dvTRoFRVESt+sPdaDhUIfjdk4Z
      mYPi/OzEbcu2sWNfxHE7mqZixoTipPtqI21obuty3NbY4lwUjMhK3I4ZJnYfaHLcTtCvY0pZ
      4YkNADeYpglVVTNyiMkwDPh8vmM/MI12dF1PmpQyjgRmpmrKRDtCCFiWBV0//p3JTNVk2zZs
      2x5SNWVyvIfaHBiK433kU4aZ+LJppmoaitumky4AjqzO8W5sj7SVqXaA46/pZF63I22dzDWd
      zOsGsKZ02gGGVk0nXQAQEVF6+FEcIiKP+v/1iIjab1rDdQAAAABJRU5ErkJggg==
    </thumbnail>
    <thumbnail height='52' name='filter' width='128'>
      iVBORw0KGgoAAAANSUhEUgAAAIAAAAA0CAYAAABCZTCoAAAACXBIWXMAAA7DAAAOwwHHb6hk
      AAAH00lEQVR4nO2caUhUURvHf85ok1pqZZsmpEnZZlbSRkirRUUrDFhRllFRilEaRWV7mZWE
      UKZIZh8iy4IW+lC0SbQY2RBkWo0tlIU6E1q5z/h+GObkvDPqTPESL/f8QHjuPc8991zP/57z
      nOdcxq21tbUViWJR/esGSP4tUgAKRwpA4UgBKBwpAIUjBaBwpAAUjhSAwpECUDhSAApHCkDh
      SAEoHCkAhSMFoHCkABSOFIDCkQJQOFIACkcKQOFIASgcKQCFIwWgcKQAFI4UgMKRAlA47v+6
      Af+vGAwGysrK+PLlCwaDAW9vbwIDAxk4cCAhISF/VOeHDx949eoVRqORmpoaPDw86N69OxER
      EQwbNsyluqqrqykuLgaga9euREVFOfSTAnABg8FAbGwsX79+7dRXrVaTmZnJmDFj2vWprq4m
      MTGRsrKyDuuKjo7m0KFDTrfz3LlzZGRkiGMvLy8KCwsd+koBuMC+fftE53fr1o3Vq1cze/Zs
      evfuTU1NDUVFRZw+fZpPnz5hMpnYtm0bBQUF+Pj42NVlNBpZt24dHz9+FOeioqKIiooiNDSU
      kJAQfv78yefPn/H29na6jW/evCEzM9NpfykAJzEajTx+/BgAlUrFsWPHiIyMFOV+fn5ER0cT
      GRnJ8uXLqaysxGg0otPpHA6/R44cEZ3v6elJcnIy8+fPt/Hx8vKiT58+TrexqamJnTt30tzc
      jEajwcfHh6qqqg6vkUGgk5SVlWE2mwEYPny4Tee3pWfPnsybN08cv3792s6nqKiIO3fuAODm
      5kZWVpZd5/8JGRkZlJeXA7BixQp69OjR6TVSAE6iUv3+V/n6+nbo6+fnJ+y6ujq78oKCAmHP
      mDHD5QDPEU+ePCE/Px+AoKAgVqxYQWNjY6fXSQE4yYABA4RdUlLisGOtPH/+XNgDBw60Kfvx
      4wcPHz4ELKJau3btX7ettraWvXv30trailqtZv/+/Xh6etLU1NTptVIAThIYGMiQIUMASzxw
      9OhRTCaTnd+dO3d48OABYAkUp0+fblOu0+lEx4waNYrg4OC/btvBgwfFXB8XF8eIESMAaG5u
      7vRaGQS6QHx8PImJiZjNZq5fv86XL1/YunUroaGhNDY2kpeXR05OjvBPSEiwWwG8fftW2NbR
      obm5mbdv31JSUoJer8ff35+QkBCGDh1Kv379OmzTjRs3RDwxcuRI1qxZI8qc+fEXKQAXmDhx
      Itu3b+fw4cOYzWaKi4uJiYlh3LhxvHv3DoPBAIC7uzvbtm1j4cKFdnXo9XphBwUFUVhYyI4d
      O6ivr7fzVavVrFq1iri4ODw8POzKKyoqSEtLAywrhv3799vEKs4gpwAXWbRoEdnZ2Xh5eQGW
      t+zp06ei8729vblw4YLDzgf4/v27sHU6HUlJSaLzu3TpYhO5m0wmcnJy2LBhg109ZrOZlJQU
      EYts2bLFJk5xFikAFzEajVy7ds3hGwvw69cv0tLSbBI8/11upbCwELPZzIIFC8jNzeXBgwfc
      vn2bu3fvEhsbK97mFy9e2NVz9uxZdDodAFOmTGHBggV/9DxyCnCBjx8/kpCQQEVFBQAajQat
      VktQUBAFBQW8efMGsKzzY2JiSE1NtUsCtRWOSqVi06ZNLF261MbHx8eH+Ph4evbsSXp6OmB5
      462CeP36NdnZ2QD06tWLnTt3/vEzyRHASRoaGti4caPo/LCwMK5cuUJiYiKLFy/m/PnzZGRk
      0LdvX8CSlUtOThYbMlY0Go2wJ0+ebNf5bdFqtaI+630bGxvZtWsXLS0tAOzevdsm7+AqUgBO
      cubMGb59+wbA4MGDycnJEZ1jZdKkSeTn5xMREQFY5vD09HSbaNzf31/Y06ZN6/Ce7u7uDB8+
      HLDsFAKcOHFC2FqtlkmTJv3Vc8kpwEnu378v7M2bN9O1a1eHft26dePQoUMsWbKE+vp6SktL
      +fr1KwEBAQD0799f+LZXR1usgqmsrMRsNnPp0iVRdvHiRS5fvtzutdY8RV1dHePHjxfnU1NT
      mTp1KiBHAKdoaWkRQZ1KpWL06NEd+vfp00e8ufD77QVbAZSUlHR6b+vqor1NIZPJ1O5fe35t
      E0RyBHASlUqFyWTCbDY7lWCxbhwBYr4GCA0NFbY1iu+I9+/fA7+TRuPGjXOYgXREcXGxaOvY
      sWPF+d69ewtbCsAJ3N3dCQ4OFlG+TqdrdzcQLGv90tJScRwWFibsCRMmMGjQIPR6PS9fvuTi
      xYtotVqH9Tx69Ejs7gUEBKBSqTh16pTT7Z41axYGgwEvLy+ysrIc+sgpwEna7tjt2bOn3X32
      hoYGdu3aJRI0gwcPthm+3dzciIuLE8fHjx+3WymAZdo4evSoOHY1w+csbvLXwp2jqqqKZcuW
      YTQaActybvHixYSHh+Pr60tDQwPv3r0jPz9fzNtqtZqTJ0/ajRZms5mVK1eKbwXUajVz584l
      IiKClpYW9Ho9V69epaGhAbDM/zdv3nS5zW1HgPY+CZMCcIFnz56RlJRkk81rD41GQ1JSEosW
      LXJYXldXR0pKis3qwhEBAQEcOHCA8PBwl9srBfA/oLa2lry8PK5du2aT17fi6enJzJkzWb9+
      faefc7W2tpKbm8utW7coLy+3CRz9/PyYOXMmCQkJYt/BVebMmUNlZSXdu3fn3r17Dn2kABSO
      DAIVjhSAwvkPmOkQQWdpmXwAAAAASUVORK5CYII=
    </thumbnail>
  </thumbnails>
</workbook>
