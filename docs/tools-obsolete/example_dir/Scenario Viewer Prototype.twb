<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20192.20.0218.0709                               -->
<workbook original-version='18.1' source-build='2019.2.9 (20192.20.0218.0709)' source-platform='mac' version='18.1' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <SheetIdentifierTracking ignorable='true' predowngraded='true' />
    <SortTagCleanup />
    <WindowsPersistSimpleIdentifiers />
  </document-format-change-manifest>
  <preferences>
    <preference name='ui.encoding.shelf.height' value='24' />
    <preference name='ui.shelf.height' value='26' />
  </preferences>
  <datasources>
    <datasource caption='database-for-tableau' inline='true' name='federated.1xb3n6j0v4z0p918shy1f1pvgo02' version='18.1'>
      <connection class='federated'>
        <named-connections>
          <named-connection caption='database-for-tableau' name='textscan.02i1x1p160zoki1d1l1z31nyuy6r'>
            <connection class='textscan' directory='.' filename='database-for-tableau.csv' password='' server='' />
          </named-connection>
        </named-connections>
        <relation connection='textscan.02i1x1p160zoki1d1l1z31nyuy6r' name='database-for-tableau.csv' table='[database-for-tableau#csv]' type='table'>
          <columns character-set='UTF-8' header='yes' locale='en_US' separator=','>
            <column datatype='string' name='Scenario' ordinal='0' />
            <column datatype='integer' name='B' ordinal='1' />
            <column datatype='integer' name='C' ordinal='2' />
            <column datatype='integer' name='D' ordinal='3' />
            <column datatype='integer' name='E' ordinal='4' />
            <column datatype='integer' name='F' ordinal='5' />
            <column datatype='integer' name='G' ordinal='6' />
            <column datatype='integer' name='I' ordinal='7' />
            <column datatype='integer' name='L' ordinal='8' />
            <column datatype='integer' name='P' ordinal='9' />
            <column datatype='integer' name='T' ordinal='10' />
            <column datatype='integer' name='V' ordinal='11' />
            <column datatype='integer' name='GHGReduction' ordinal='12' />
            <column datatype='real' name='DVMTPerCapita' ordinal='13' />
            <column datatype='real' name='WalkTravelPerCapita' ordinal='14' />
            <column datatype='integer' name='TruckDelay' ordinal='15' />
            <column datatype='real' name='AirPollutionEm' ordinal='16' />
            <column datatype='real' name='FuelUse' ordinal='17' />
            <column datatype='real' name='VehicleCost' ordinal='18' />
            <column datatype='real' name='VehicleCostLow' ordinal='19' />
            <column datatype='string' name='Bicycles' ordinal='20' />
            <column datatype='string' name='Demand_Management' ordinal='21' />
            <column datatype='string' name='Land_Use' ordinal='22' />
            <column datatype='string' name='Parking' ordinal='23' />
            <column datatype='string' name='Transit' ordinal='24' />
            <column datatype='string' name='Vehicle_Travel_Cost' ordinal='25' />
            <column datatype='string' name='Vehicle_Characteristics' ordinal='26' />
            <column datatype='string' name='Technology_Mix_and_CI' ordinal='27' />
            <column datatype='string' name='Driving_Efficiency' ordinal='28' />
            <column datatype='string' name='Fuel_Price' ordinal='29' />
            <column datatype='string' name='Income' ordinal='30' />
            <column datatype='integer' name='Strategy_Community_Design' ordinal='31' />
            <column datatype='integer' name='Strategy_Marketing_Incentive' ordinal='32' />
            <column datatype='integer' name='Strategy_Pricing' ordinal='33' />
            <column datatype='integer' name='Strategy_Vehicles_Fuels' ordinal='34' />
            <column datatype='integer' name='Strategy_Fuel_Price' ordinal='35' />
            <column datatype='integer' name='Strategy_Income' ordinal='36' />
            <column datatype='integer' name='scenario_index' ordinal='37' />
          </columns>
        </relation>
        <metadata-records>
          <metadata-record class='capability'>
            <remote-name />
            <remote-type>0</remote-type>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias />
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='character-set'>&quot;UTF-8&quot;</attribute>
              <attribute datatype='string' name='collation'>&quot;en_US&quot;</attribute>
              <attribute datatype='string' name='field-delimiter'>&quot;,&quot;</attribute>
              <attribute datatype='string' name='header-row'>&quot;true&quot;</attribute>
              <attribute datatype='string' name='locale'>&quot;en_US&quot;</attribute>
              <attribute datatype='string' name='single-char'>&quot;&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Scenario</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Scenario]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Scenario</remote-alias>
            <ordinal>0</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>B</remote-name>
            <remote-type>20</remote-type>
            <local-name>[B]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>B</remote-alias>
            <ordinal>1</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>C</remote-name>
            <remote-type>20</remote-type>
            <local-name>[C]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>C</remote-alias>
            <ordinal>2</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>D</remote-name>
            <remote-type>20</remote-type>
            <local-name>[D]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>D</remote-alias>
            <ordinal>3</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>E</remote-name>
            <remote-type>20</remote-type>
            <local-name>[E]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>E</remote-alias>
            <ordinal>4</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>F</remote-name>
            <remote-type>20</remote-type>
            <local-name>[F]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>F</remote-alias>
            <ordinal>5</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>G</remote-name>
            <remote-type>20</remote-type>
            <local-name>[G]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>G</remote-alias>
            <ordinal>6</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>I</remote-name>
            <remote-type>20</remote-type>
            <local-name>[I]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>I</remote-alias>
            <ordinal>7</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>L</remote-name>
            <remote-type>20</remote-type>
            <local-name>[L]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>L</remote-alias>
            <ordinal>8</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>P</remote-name>
            <remote-type>20</remote-type>
            <local-name>[P]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>P</remote-alias>
            <ordinal>9</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>T</remote-name>
            <remote-type>20</remote-type>
            <local-name>[T]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>T</remote-alias>
            <ordinal>10</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>V</remote-name>
            <remote-type>20</remote-type>
            <local-name>[V]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>V</remote-alias>
            <ordinal>11</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>GHGReduction</remote-name>
            <remote-type>20</remote-type>
            <local-name>[GHGReduction]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>GHGReduction</remote-alias>
            <ordinal>12</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DVMTPerCapita</remote-name>
            <remote-type>5</remote-type>
            <local-name>[DVMTPerCapita]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>DVMTPerCapita</remote-alias>
            <ordinal>13</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>WalkTravelPerCapita</remote-name>
            <remote-type>5</remote-type>
            <local-name>[WalkTravelPerCapita]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>WalkTravelPerCapita</remote-alias>
            <ordinal>14</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TruckDelay</remote-name>
            <remote-type>20</remote-type>
            <local-name>[TruckDelay]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>TruckDelay</remote-alias>
            <ordinal>15</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>AirPollutionEm</remote-name>
            <remote-type>5</remote-type>
            <local-name>[AirPollutionEm]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>AirPollutionEm</remote-alias>
            <ordinal>16</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FuelUse</remote-name>
            <remote-type>5</remote-type>
            <local-name>[FuelUse]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>FuelUse</remote-alias>
            <ordinal>17</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>VehicleCost</remote-name>
            <remote-type>5</remote-type>
            <local-name>[VehicleCost]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>VehicleCost</remote-alias>
            <ordinal>18</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>VehicleCostLow</remote-name>
            <remote-type>5</remote-type>
            <local-name>[VehicleCostLow]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>VehicleCostLow</remote-alias>
            <ordinal>19</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Bicycles</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Bicycles]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Bicycles</remote-alias>
            <ordinal>20</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Demand_Management</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Demand_Management]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Demand_Management</remote-alias>
            <ordinal>21</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Land_Use</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Land_Use]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Land_Use</remote-alias>
            <ordinal>22</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Parking</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Parking]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Parking</remote-alias>
            <ordinal>23</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Transit</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Transit]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Transit</remote-alias>
            <ordinal>24</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Vehicle_Travel_Cost</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Vehicle_Travel_Cost]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Vehicle_Travel_Cost</remote-alias>
            <ordinal>25</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Vehicle_Characteristics</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Vehicle_Characteristics]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Vehicle_Characteristics</remote-alias>
            <ordinal>26</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Technology_Mix_and_CI</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Technology_Mix_and_CI]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Technology_Mix_and_CI</remote-alias>
            <ordinal>27</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Driving_Efficiency</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Driving_Efficiency]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Driving_Efficiency</remote-alias>
            <ordinal>28</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Fuel_Price</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Fuel_Price]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Fuel_Price</remote-alias>
            <ordinal>29</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Income</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Income]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Income</remote-alias>
            <ordinal>30</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>1073741823</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Strategy_Community_Design</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Strategy_Community_Design]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Strategy_Community_Design</remote-alias>
            <ordinal>31</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Strategy_Marketing_Incentive</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Strategy_Marketing_Incentive]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Strategy_Marketing_Incentive</remote-alias>
            <ordinal>32</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Strategy_Pricing</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Strategy_Pricing]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Strategy_Pricing</remote-alias>
            <ordinal>33</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Strategy_Vehicles_Fuels</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Strategy_Vehicles_Fuels]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Strategy_Vehicles_Fuels</remote-alias>
            <ordinal>34</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Strategy_Fuel_Price</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Strategy_Fuel_Price]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Strategy_Fuel_Price</remote-alias>
            <ordinal>35</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Strategy_Income</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Strategy_Income]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>Strategy_Income</remote-alias>
            <ordinal>36</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>scenario_index</remote-name>
            <remote-type>20</remote-type>
            <local-name>[scenario_index]</local-name>
            <parent-name>[database-for-tableau.csv]</parent-name>
            <remote-alias>scenario_index</remote-alias>
            <ordinal>37</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
          </metadata-record>
        </metadata-records>
      </connection>
      <aliases enabled='yes' />
      <column aggregation='None' caption='Air Pollution Em (bin)' datatype='integer' name='[Air Pollution Em (bin) 2]' role='dimension' type='ordinal'>
        <calculation class='bin' decimals='4' formula='[AirPollutionEm]' peg='0' size='10000' />
      </column>
      <column caption='Air Pollution Em' datatype='real' name='[AirPollutionEm]' role='measure' type='quantitative' />
      <column aggregation='Sum' datatype='integer' name='[B]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[C]' role='dimension' type='ordinal' />
      <column aggregation='None' caption='DVMT Per Capita (bin)' datatype='integer' name='[DVMT Per Capita (bin)]' role='dimension' type='ordinal'>
        <calculation class='bin' decimals='-3' formula='[DVMTPerCapita]' peg='0' size='0.25' />
      </column>
      <column caption='DVMT Per Capita' datatype='real' name='[DVMTPerCapita]' role='measure' type='quantitative' />
      <column aggregation='Sum' datatype='integer' name='[D]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[E]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[F]' role='dimension' type='ordinal' />
      <column aggregation='None' caption='Fuel Use (bin)' datatype='integer' name='[Fuel Use (bin)]' role='dimension' type='ordinal'>
        <calculation class='bin' decimals='4' formula='[FuelUse]' peg='0' size='500000' />
      </column>
      <column caption='Fuel Use' datatype='real' name='[FuelUse]' role='measure' type='quantitative' />
      <column aggregation='None' caption='GHG Reduction (bin)' datatype='integer' name='[GHG Reduction (bin)]' role='dimension' type='ordinal'>
        <calculation class='bin' decimals='0' formula='[GHGReduction]' peg='0' size='1' />
      </column>
      <column caption='GHG Reduction' datatype='integer' name='[GHGReduction]' role='measure' type='quantitative' />
      <column aggregation='Sum' datatype='integer' name='[G]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[I]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[L]' role='dimension' type='ordinal' />
      <column datatype='integer' name='[Number of Records]' role='measure' type='quantitative' user:auto-column='numrec'>
        <calculation class='tableau' formula='1' />
      </column>
      <column aggregation='Sum' datatype='integer' name='[P]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Community Design' datatype='integer' name='[Strategy_Community_Design]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Fuel Price (Strategy)' datatype='integer' name='[Strategy_Fuel_Price]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Income (Strategy)' datatype='integer' name='[Strategy_Income]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Marketing and Incentives' datatype='integer' name='[Strategy_Marketing_Incentive]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Pricing' datatype='integer' name='[Strategy_Pricing]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Vehicles and Fuels' datatype='integer' name='[Strategy_Vehicles_Fuels]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[T]' role='dimension' type='ordinal' />
      <column aggregation='None' caption='Truck Delay (bin)' datatype='integer' name='[Truck Delay (bin)]' role='dimension' type='ordinal'>
        <calculation class='bin' decimals='0' formula='[TruckDelay]' peg='0' size='1' />
      </column>
      <column caption='Truck Delay' datatype='integer' name='[TruckDelay]' role='measure' type='quantitative' />
      <column aggregation='Sum' datatype='integer' name='[V]' role='dimension' type='ordinal' />
      <column aggregation='None' caption='Vehicle Cost (bin)' datatype='integer' name='[Vehicle Cost (bin)]' role='dimension' type='ordinal'>
        <calculation class='bin' decimals='-2' formula='[VehicleCost]' peg='0' size='0.1' />
      </column>
      <column aggregation='None' caption='Vehicle Cost Low (bin)' datatype='integer' name='[Vehicle Cost Low (bin)]' role='dimension' type='ordinal'>
        <calculation class='bin' decimals='-2' formula='[VehicleCostLow]' peg='0' size='0.25' />
      </column>
      <column caption='Vehicle Cost Low' datatype='real' name='[VehicleCostLow]' role='measure' type='quantitative' />
      <column caption='Vehicle Cost' datatype='real' name='[VehicleCost]' role='measure' type='quantitative' />
      <column datatype='string' name='[Vehicle_Travel_Cost]' role='dimension' type='nominal' />
      <column aggregation='None' caption='Walk Travel Per Capita (bin)' datatype='integer' name='[Walk Travel Per Capita (bin)]' role='dimension' type='ordinal'>
        <calculation class='bin' decimals='-3' formula='[WalkTravelPerCapita]' peg='0' size='0.005' />
      </column>
      <column caption='Walk Travel Per Capita' datatype='real' name='[WalkTravelPerCapita]' role='measure' type='quantitative' />
      <column aggregation='Sum' datatype='integer' name='[scenario_index]' role='dimension' type='ordinal' />
      <column-instance column='[Vehicle_Travel_Cost]' derivation='None' name='[none:Vehicle_Travel_Cost:nk]' pivot='key' type='nominal' />
      <layout dim-ordering='alphabetic' dim-percentage='0.698276' measure-ordering='alphabetic' measure-percentage='0.301724' show-structure='true' />
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
      </semantic-values>
      <default-sorts>
        <manual-sort column='[none:Vehicle_Travel_Cost:nk]' direction='ASC'>
          <dictionary>
            <bucket>&quot;Base&quot;</bucket>
            <bucket>&quot;5 cents/mile&quot;</bucket>
            <bucket>&quot;9 cents/mile&quot;</bucket>
          </dictionary>
        </manual-sort>
      </default-sorts>
    </datasource>
  </datasources>
  <shared-views>
    <shared-view name='federated.1xb3n6j0v4z0p918shy1f1pvgo02'>
      <datasources>
        <datasource caption='database-for-tableau' name='federated.1xb3n6j0v4z0p918shy1f1pvgo02' />
      </datasources>
      <datasource-dependencies datasource='federated.1xb3n6j0v4z0p918shy1f1pvgo02'>
        <column aggregation='Sum' caption='Community Design' datatype='integer' name='[Strategy_Community_Design]' role='dimension' type='ordinal' />
        <column aggregation='Sum' caption='Fuel Price (Strategy)' datatype='integer' name='[Strategy_Fuel_Price]' role='dimension' type='ordinal' />
        <column aggregation='Sum' caption='Income (Strategy)' datatype='integer' name='[Strategy_Income]' role='dimension' type='ordinal' />
        <column aggregation='Sum' caption='Marketing and Incentives' datatype='integer' name='[Strategy_Marketing_Incentive]' role='dimension' type='ordinal' />
        <column aggregation='Sum' caption='Pricing' datatype='integer' name='[Strategy_Pricing]' role='dimension' type='ordinal' />
        <column aggregation='Sum' caption='Vehicles and Fuels' datatype='integer' name='[Strategy_Vehicles_Fuels]' role='dimension' type='ordinal' />
        <column-instance column='[Strategy_Community_Design]' derivation='None' name='[none:Strategy_Community_Design:ok]' pivot='key' type='ordinal' />
        <column-instance column='[Strategy_Fuel_Price]' derivation='None' name='[none:Strategy_Fuel_Price:ok]' pivot='key' type='ordinal' />
        <column-instance column='[Strategy_Income]' derivation='None' name='[none:Strategy_Income:ok]' pivot='key' type='ordinal' />
        <column-instance column='[Strategy_Marketing_Incentive]' derivation='None' name='[none:Strategy_Marketing_Incentive:ok]' pivot='key' type='ordinal' />
        <column-instance column='[Strategy_Pricing]' derivation='None' name='[none:Strategy_Pricing:ok]' pivot='key' type='ordinal' />
        <column-instance column='[Strategy_Vehicles_Fuels]' derivation='None' name='[none:Strategy_Vehicles_Fuels:ok]' pivot='key' type='ordinal' />
      </datasource-dependencies>
      <filter class='categorical' column='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]'>
        <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
          <groupfilter function='member' level='[none:Strategy_Community_Design:ok]' member='1' />
          <groupfilter function='member' level='[none:Strategy_Community_Design:ok]' member='2' />
          <groupfilter function='member' level='[none:Strategy_Community_Design:ok]' member='3' />
          <groupfilter function='member' level='[none:Strategy_Community_Design:ok]' member='4' />
        </groupfilter>
      </filter>
      <filter class='categorical' column='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]'>
        <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
          <groupfilter function='member' level='[none:Strategy_Fuel_Price:ok]' member='0' />
          <groupfilter function='member' level='[none:Strategy_Fuel_Price:ok]' member='1' />
          <groupfilter function='member' level='[none:Strategy_Fuel_Price:ok]' member='2' />
        </groupfilter>
      </filter>
      <filter class='categorical' column='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]'>
        <groupfilter function='union' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
          <groupfilter function='member' level='[none:Strategy_Income:ok]' member='0' />
          <groupfilter function='member' level='[none:Strategy_Income:ok]' member='1' />
          <groupfilter function='member' level='[none:Strategy_Income:ok]' member='2' />
        </groupfilter>
      </filter>
      <filter class='categorical' column='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]'>
        <groupfilter function='level-members' level='[none:Strategy_Marketing_Incentive:ok]' user:ui-enumeration='all' user:ui-marker='enumerate' />
      </filter>
      <filter class='categorical' column='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]'>
        <groupfilter function='level-members' level='[none:Strategy_Pricing:ok]' user:ui-enumeration='all' user:ui-marker='enumerate' />
      </filter>
      <filter class='categorical' column='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]'>
        <groupfilter function='level-members' level='[none:Strategy_Vehicles_Fuels:ok]' user:ui-enumeration='all' user:ui-marker='enumerate' />
      </filter>
    </shared-view>
  </shared-views>
  <worksheets>
    <worksheet name='Air Pollution Emissions'>
      <layout-options>
        <title>
          <formatted-text>
            <run>Air Pollution Emissions</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='database-for-tableau' name='federated.1xb3n6j0v4z0p918shy1f1pvgo02' />
          </datasources>
          <datasource-dependencies datasource='federated.1xb3n6j0v4z0p918shy1f1pvgo02'>
            <column aggregation='None' caption='Air Pollution Em (bin)' datatype='integer' name='[Air Pollution Em (bin) 2]' role='dimension' type='ordinal'>
              <calculation class='bin' decimals='4' formula='[AirPollutionEm]' peg='0' size='10000' />
            </column>
            <column caption='Air Pollution Em' datatype='real' name='[AirPollutionEm]' role='measure' type='quantitative' />
            <column datatype='integer' name='[Number of Records]' role='measure' type='quantitative' user:auto-column='numrec'>
              <calculation class='tableau' formula='1' />
            </column>
            <column aggregation='Sum' caption='Community Design' datatype='integer' name='[Strategy_Community_Design]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Fuel Price (Strategy)' datatype='integer' name='[Strategy_Fuel_Price]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Income (Strategy)' datatype='integer' name='[Strategy_Income]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Marketing and Incentives' datatype='integer' name='[Strategy_Marketing_Incentive]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Pricing' datatype='integer' name='[Strategy_Pricing]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Vehicles and Fuels' datatype='integer' name='[Strategy_Vehicles_Fuels]' role='dimension' type='ordinal' />
            <column-instance column='[Air Pollution Em (bin) 2]' derivation='None' name='[none:Air Pollution Em (bin) 2:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Community_Design]' derivation='None' name='[none:Strategy_Community_Design:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Fuel_Price]' derivation='None' name='[none:Strategy_Fuel_Price:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Income]' derivation='None' name='[none:Strategy_Income:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Marketing_Incentive]' derivation='None' name='[none:Strategy_Marketing_Incentive:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Pricing]' derivation='None' name='[none:Strategy_Pricing:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Vehicles_Fuels]' derivation='None' name='[none:Strategy_Vehicles_Fuels:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Number of Records]' derivation='Sum' name='[sum:Number of Records:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <slices>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='cell-w' value='20' />
            <format attr='cell-h' value='20' />
            <format attr='cell' value='20' />
            <format attr='cell-q' value='100' />
            <format attr='width' field='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Air Pollution Em (bin) 2:ok]' value='36' />
          </style-rule>
          <style-rule element='label'>
            <format attr='text-orientation' field='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Air Pollution Em (bin) 2:ok]' value='-90' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
            <format attr='font-size' value='11' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='font-size' value='10' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <customized-tooltip>
              <formatted-text>
                <run>Æ&#10;</run>
                <run fontcolor='#787878' fontsize='14'>Number of Records:&#9;</run>
                <run bold='true' fontsize='14'>&lt;[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]&gt;</run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-color' value='#75a1c7' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]</rows>
        <cols>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Air Pollution Em (bin) 2:ok]</cols>
        <show-full-range>
          <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[Air Pollution Em (bin) 2]</column>
        </show-full-range>
      </table>
      <simple-id uuid='{7A19953D-881D-4A1E-BECD-5DB6791A1567}' />
    </worksheet>
    <worksheet name='Annual Fuel Use'>
      <layout-options>
        <title>
          <formatted-text>
            <run>Annual Fuel Use</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='database-for-tableau' name='federated.1xb3n6j0v4z0p918shy1f1pvgo02' />
          </datasources>
          <datasource-dependencies datasource='federated.1xb3n6j0v4z0p918shy1f1pvgo02'>
            <column aggregation='None' caption='Fuel Use (bin)' datatype='integer' name='[Fuel Use (bin)]' role='dimension' type='ordinal'>
              <calculation class='bin' decimals='4' formula='[FuelUse]' peg='0' size='500000' />
            </column>
            <column caption='Fuel Use' datatype='real' name='[FuelUse]' role='measure' type='quantitative' />
            <column datatype='integer' name='[Number of Records]' role='measure' type='quantitative' user:auto-column='numrec'>
              <calculation class='tableau' formula='1' />
            </column>
            <column aggregation='Sum' caption='Community Design' datatype='integer' name='[Strategy_Community_Design]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Fuel Price (Strategy)' datatype='integer' name='[Strategy_Fuel_Price]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Income (Strategy)' datatype='integer' name='[Strategy_Income]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Marketing and Incentives' datatype='integer' name='[Strategy_Marketing_Incentive]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Pricing' datatype='integer' name='[Strategy_Pricing]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Vehicles and Fuels' datatype='integer' name='[Strategy_Vehicles_Fuels]' role='dimension' type='ordinal' />
            <column-instance column='[Fuel Use (bin)]' derivation='None' name='[none:Fuel Use (bin):ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Community_Design]' derivation='None' name='[none:Strategy_Community_Design:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Fuel_Price]' derivation='None' name='[none:Strategy_Fuel_Price:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Income]' derivation='None' name='[none:Strategy_Income:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Marketing_Incentive]' derivation='None' name='[none:Strategy_Marketing_Incentive:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Pricing]' derivation='None' name='[none:Strategy_Pricing:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Vehicles_Fuels]' derivation='None' name='[none:Strategy_Vehicles_Fuels:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Number of Records]' derivation='Sum' name='[sum:Number of Records:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <slices>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='cell-w' value='20' />
            <format attr='cell-h' value='20' />
            <format attr='cell' value='20' />
            <format attr='cell-q' value='100' />
            <format attr='width' field='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Fuel Use (bin):ok]' value='46' />
          </style-rule>
          <style-rule element='label'>
            <format attr='text-orientation' field='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Fuel Use (bin):ok]' value='-90' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
            <format attr='font-size' value='11' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='font-size' value='10' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <customized-tooltip>
              <formatted-text>
                <run>Æ&#10;</run>
                <run fontcolor='#787878' fontsize='14'>Number of Records:&#9;</run>
                <run bold='true' fontsize='14'>&lt;[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]&gt;</run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-color' value='#75a1c7' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]</rows>
        <cols>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Fuel Use (bin):ok]</cols>
        <show-full-range>
          <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[Air Pollution Em (bin) 2]</column>
          <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[Fuel Use (bin)]</column>
        </show-full-range>
      </table>
      <simple-id uuid='{41972AEC-EA12-4BBC-B196-71C6FABAF07F}' />
    </worksheet>
    <worksheet name='DVMT Per Capita'>
      <layout-options>
        <title>
          <formatted-text>
            <run>DVMT Per Capita</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='database-for-tableau' name='federated.1xb3n6j0v4z0p918shy1f1pvgo02' />
          </datasources>
          <datasource-dependencies datasource='federated.1xb3n6j0v4z0p918shy1f1pvgo02'>
            <column aggregation='None' caption='DVMT Per Capita (bin)' datatype='integer' name='[DVMT Per Capita (bin)]' role='dimension' type='ordinal'>
              <calculation class='bin' decimals='-3' formula='[DVMTPerCapita]' peg='0' size='0.25' />
            </column>
            <column caption='DVMT Per Capita' datatype='real' name='[DVMTPerCapita]' role='measure' type='quantitative' />
            <column datatype='integer' name='[Number of Records]' role='measure' type='quantitative' user:auto-column='numrec'>
              <calculation class='tableau' formula='1' />
            </column>
            <column aggregation='Sum' caption='Community Design' datatype='integer' name='[Strategy_Community_Design]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Fuel Price (Strategy)' datatype='integer' name='[Strategy_Fuel_Price]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Income (Strategy)' datatype='integer' name='[Strategy_Income]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Marketing and Incentives' datatype='integer' name='[Strategy_Marketing_Incentive]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Pricing' datatype='integer' name='[Strategy_Pricing]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Vehicles and Fuels' datatype='integer' name='[Strategy_Vehicles_Fuels]' role='dimension' type='ordinal' />
            <column-instance column='[DVMT Per Capita (bin)]' derivation='None' name='[none:DVMT Per Capita (bin):ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Community_Design]' derivation='None' name='[none:Strategy_Community_Design:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Fuel_Price]' derivation='None' name='[none:Strategy_Fuel_Price:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Income]' derivation='None' name='[none:Strategy_Income:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Marketing_Incentive]' derivation='None' name='[none:Strategy_Marketing_Incentive:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Pricing]' derivation='None' name='[none:Strategy_Pricing:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Vehicles_Fuels]' derivation='None' name='[none:Strategy_Vehicles_Fuels:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Number of Records]' derivation='Sum' name='[sum:Number of Records:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <slices>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='width' field='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:DVMT Per Capita (bin):ok]' value='33' />
            <format attr='cell-w' value='20' />
            <format attr='cell-h' value='20' />
            <format attr='cell' value='20' />
            <format attr='cell-q' value='100' />
          </style-rule>
          <style-rule element='label'>
            <format attr='text-orientation' field='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:DVMT Per Capita (bin):ok]' value='-90' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
            <format attr='font-size' value='11' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='font-size' value='10' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#787878' fontsize='14'>Number of Records:&#9;</run>
                <run bold='true' fontsize='14'>&lt;[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]&gt;</run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-color' value='#75a1c7' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]</rows>
        <cols>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:DVMT Per Capita (bin):ok]</cols>
      </table>
      <simple-id uuid='{64BD31B9-E47C-464D-9ADC-DEF49975E980}' />
    </worksheet>
    <worksheet name='GHG Target Reduction'>
      <layout-options>
        <title>
          <formatted-text>
            <run>GHG Target Reduction</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='database-for-tableau' name='federated.1xb3n6j0v4z0p918shy1f1pvgo02' />
          </datasources>
          <datasource-dependencies datasource='federated.1xb3n6j0v4z0p918shy1f1pvgo02'>
            <column aggregation='None' caption='GHG Reduction (bin)' datatype='integer' name='[GHG Reduction (bin)]' role='dimension' type='ordinal'>
              <calculation class='bin' decimals='0' formula='[GHGReduction]' peg='0' size='1' />
            </column>
            <column caption='GHG Reduction' datatype='integer' name='[GHGReduction]' role='measure' type='quantitative' />
            <column datatype='integer' name='[Number of Records]' role='measure' type='quantitative' user:auto-column='numrec'>
              <calculation class='tableau' formula='1' />
            </column>
            <column aggregation='Sum' caption='Community Design' datatype='integer' name='[Strategy_Community_Design]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Fuel Price (Strategy)' datatype='integer' name='[Strategy_Fuel_Price]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Income (Strategy)' datatype='integer' name='[Strategy_Income]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Marketing and Incentives' datatype='integer' name='[Strategy_Marketing_Incentive]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Pricing' datatype='integer' name='[Strategy_Pricing]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Vehicles and Fuels' datatype='integer' name='[Strategy_Vehicles_Fuels]' role='dimension' type='ordinal' />
            <column-instance column='[GHG Reduction (bin)]' derivation='None' name='[none:GHG Reduction (bin):ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Community_Design]' derivation='None' name='[none:Strategy_Community_Design:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Fuel_Price]' derivation='None' name='[none:Strategy_Fuel_Price:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Income]' derivation='None' name='[none:Strategy_Income:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Marketing_Incentive]' derivation='None' name='[none:Strategy_Marketing_Incentive:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Pricing]' derivation='None' name='[none:Strategy_Pricing:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Vehicles_Fuels]' derivation='None' name='[none:Strategy_Vehicles_Fuels:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Number of Records]' derivation='Sum' name='[sum:Number of Records:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <slices>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='cell-w' value='20' />
            <format attr='cell-h' value='20' />
            <format attr='cell' value='20' />
            <format attr='cell-q' value='100' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
            <format attr='font-size' value='11' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='font-size' value='10' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <customized-tooltip>
              <formatted-text>
                <run fontcolor='#787878' fontsize='14'>Number of Records:&#9;</run>
                <run bold='true' fontsize='14'>&lt;[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]&gt;</run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-color' value='#75a1c7' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]</rows>
        <cols>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:GHG Reduction (bin):ok]</cols>
        <show-full-range>
          <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[GHG Reduction (bin)]</column>
        </show-full-range>
      </table>
      <simple-id uuid='{E9C7FFAF-8938-4AAC-80F5-1CB5907012DC}' />
    </worksheet>
    <worksheet name='Household Vehicle Cost'>
      <layout-options>
        <title>
          <formatted-text>
            <run>Household Vehicle Cost as a Percentage of Income</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='database-for-tableau' name='federated.1xb3n6j0v4z0p918shy1f1pvgo02' />
          </datasources>
          <datasource-dependencies datasource='federated.1xb3n6j0v4z0p918shy1f1pvgo02'>
            <column datatype='integer' name='[Number of Records]' role='measure' type='quantitative' user:auto-column='numrec'>
              <calculation class='tableau' formula='1' />
            </column>
            <column aggregation='Sum' caption='Community Design' datatype='integer' name='[Strategy_Community_Design]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Fuel Price (Strategy)' datatype='integer' name='[Strategy_Fuel_Price]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Income (Strategy)' datatype='integer' name='[Strategy_Income]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Marketing and Incentives' datatype='integer' name='[Strategy_Marketing_Incentive]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Pricing' datatype='integer' name='[Strategy_Pricing]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Vehicles and Fuels' datatype='integer' name='[Strategy_Vehicles_Fuels]' role='dimension' type='ordinal' />
            <column aggregation='None' caption='Vehicle Cost (bin)' datatype='integer' name='[Vehicle Cost (bin)]' role='dimension' type='ordinal'>
              <calculation class='bin' decimals='-2' formula='[VehicleCost]' peg='0' size='0.1' />
            </column>
            <column caption='Vehicle Cost' datatype='real' name='[VehicleCost]' role='measure' type='quantitative' />
            <column-instance column='[Strategy_Community_Design]' derivation='None' name='[none:Strategy_Community_Design:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Fuel_Price]' derivation='None' name='[none:Strategy_Fuel_Price:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Income]' derivation='None' name='[none:Strategy_Income:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Marketing_Incentive]' derivation='None' name='[none:Strategy_Marketing_Incentive:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Pricing]' derivation='None' name='[none:Strategy_Pricing:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Vehicles_Fuels]' derivation='None' name='[none:Strategy_Vehicles_Fuels:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Vehicle Cost (bin)]' derivation='None' name='[none:Vehicle Cost (bin):ok]' pivot='key' type='ordinal' />
            <column-instance column='[Number of Records]' derivation='Sum' name='[sum:Number of Records:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <slices>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='cell-w' value='20' />
            <format attr='cell-h' value='20' />
            <format attr='cell' value='20' />
            <format attr='cell-q' value='100' />
            <format attr='width' field='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Vehicle Cost (bin):ok]' value='65' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
            <format attr='font-size' value='11' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='font-size' value='10' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <customized-tooltip>
              <formatted-text>
                <run>Æ&#10;</run>
                <run fontcolor='#787878' fontsize='14'>Number of Records:&#9;</run>
                <run bold='true' fontsize='14'>&lt;[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]&gt;</run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-color' value='#75a1c7' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]</rows>
        <cols>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Vehicle Cost (bin):ok]</cols>
        <show-full-range>
          <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[Air Pollution Em (bin) 2]</column>
          <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[Fuel Use (bin)]</column>
          <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[Truck Delay (bin)]</column>
        </show-full-range>
      </table>
      <simple-id uuid='{9A23F5A0-98D3-4BB1-837A-5797ED5BE08C}' />
    </worksheet>
    <worksheet name='Low Income Household Vehicle Cost'>
      <layout-options>
        <title>
          <formatted-text>
            <run>Household Vehicle Cost as a Percentage of Income for Low Income Households</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='database-for-tableau' name='federated.1xb3n6j0v4z0p918shy1f1pvgo02' />
          </datasources>
          <datasource-dependencies datasource='federated.1xb3n6j0v4z0p918shy1f1pvgo02'>
            <column datatype='integer' name='[Number of Records]' role='measure' type='quantitative' user:auto-column='numrec'>
              <calculation class='tableau' formula='1' />
            </column>
            <column aggregation='Sum' caption='Community Design' datatype='integer' name='[Strategy_Community_Design]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Fuel Price (Strategy)' datatype='integer' name='[Strategy_Fuel_Price]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Income (Strategy)' datatype='integer' name='[Strategy_Income]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Marketing and Incentives' datatype='integer' name='[Strategy_Marketing_Incentive]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Pricing' datatype='integer' name='[Strategy_Pricing]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Vehicles and Fuels' datatype='integer' name='[Strategy_Vehicles_Fuels]' role='dimension' type='ordinal' />
            <column aggregation='None' caption='Vehicle Cost Low (bin)' datatype='integer' name='[Vehicle Cost Low (bin)]' role='dimension' type='ordinal'>
              <calculation class='bin' decimals='-2' formula='[VehicleCostLow]' peg='0' size='0.25' />
            </column>
            <column caption='Vehicle Cost Low' datatype='real' name='[VehicleCostLow]' role='measure' type='quantitative' />
            <column-instance column='[Strategy_Community_Design]' derivation='None' name='[none:Strategy_Community_Design:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Fuel_Price]' derivation='None' name='[none:Strategy_Fuel_Price:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Income]' derivation='None' name='[none:Strategy_Income:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Marketing_Incentive]' derivation='None' name='[none:Strategy_Marketing_Incentive:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Pricing]' derivation='None' name='[none:Strategy_Pricing:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Vehicles_Fuels]' derivation='None' name='[none:Strategy_Vehicles_Fuels:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Vehicle Cost Low (bin)]' derivation='None' name='[none:Vehicle Cost Low (bin):ok]' pivot='key' type='ordinal' />
            <column-instance column='[Number of Records]' derivation='Sum' name='[sum:Number of Records:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <slices>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='cell-w' value='20' />
            <format attr='cell-h' value='20' />
            <format attr='cell' value='20' />
            <format attr='cell-q' value='100' />
            <format attr='width' field='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Vehicle Cost Low (bin):ok]' value='34' />
          </style-rule>
          <style-rule element='label'>
            <format attr='text-orientation' field='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Vehicle Cost Low (bin):ok]' value='-90' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
            <format attr='font-size' value='11' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='font-size' value='10' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <customized-tooltip>
              <formatted-text>
                <run>Æ&#10;</run>
                <run fontcolor='#787878' fontsize='14'>Number of Records:&#9;</run>
                <run bold='true' fontsize='14'>&lt;[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]&gt;</run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-color' value='#75a1c7' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]</rows>
        <cols>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Vehicle Cost Low (bin):ok]</cols>
        <show-full-range>
          <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[Air Pollution Em (bin) 2]</column>
          <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[Fuel Use (bin)]</column>
          <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[Truck Delay (bin)]</column>
          <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[Vehicle Cost Low (bin)]</column>
        </show-full-range>
      </table>
      <simple-id uuid='{45CEECB4-873B-4F00-BBAB-AE15FA4EA908}' />
    </worksheet>
    <worksheet name='Truck Delay'>
      <layout-options>
        <title>
          <formatted-text>
            <run>Truck Delay</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='database-for-tableau' name='federated.1xb3n6j0v4z0p918shy1f1pvgo02' />
          </datasources>
          <datasource-dependencies datasource='federated.1xb3n6j0v4z0p918shy1f1pvgo02'>
            <column datatype='integer' name='[Number of Records]' role='measure' type='quantitative' user:auto-column='numrec'>
              <calculation class='tableau' formula='1' />
            </column>
            <column aggregation='Sum' caption='Community Design' datatype='integer' name='[Strategy_Community_Design]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Fuel Price (Strategy)' datatype='integer' name='[Strategy_Fuel_Price]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Income (Strategy)' datatype='integer' name='[Strategy_Income]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Marketing and Incentives' datatype='integer' name='[Strategy_Marketing_Incentive]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Pricing' datatype='integer' name='[Strategy_Pricing]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Vehicles and Fuels' datatype='integer' name='[Strategy_Vehicles_Fuels]' role='dimension' type='ordinal' />
            <column aggregation='None' caption='Truck Delay (bin)' datatype='integer' name='[Truck Delay (bin)]' role='dimension' type='ordinal'>
              <calculation class='bin' decimals='0' formula='[TruckDelay]' peg='0' size='1' />
            </column>
            <column caption='Truck Delay' datatype='integer' name='[TruckDelay]' role='measure' type='quantitative' />
            <column-instance column='[Strategy_Community_Design]' derivation='None' name='[none:Strategy_Community_Design:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Fuel_Price]' derivation='None' name='[none:Strategy_Fuel_Price:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Income]' derivation='None' name='[none:Strategy_Income:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Marketing_Incentive]' derivation='None' name='[none:Strategy_Marketing_Incentive:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Pricing]' derivation='None' name='[none:Strategy_Pricing:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Vehicles_Fuels]' derivation='None' name='[none:Strategy_Vehicles_Fuels:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Truck Delay (bin)]' derivation='None' name='[none:Truck Delay (bin):ok]' pivot='key' type='ordinal' />
            <column-instance column='[Number of Records]' derivation='Sum' name='[sum:Number of Records:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <slices>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='cell-w' value='20' />
            <format attr='cell-h' value='20' />
            <format attr='cell' value='20' />
            <format attr='cell-q' value='100' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
            <format attr='font-size' value='11' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='font-size' value='10' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <customized-tooltip>
              <formatted-text>
                <run>Æ&#10;</run>
                <run fontcolor='#787878' fontsize='14'>Number of Records:&#9;</run>
                <run bold='true' fontsize='14'>&lt;[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]&gt;</run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-color' value='#75a1c7' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]</rows>
        <cols>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Truck Delay (bin):ok]</cols>
        <show-full-range>
          <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[Air Pollution Em (bin) 2]</column>
          <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[Fuel Use (bin)]</column>
          <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[Truck Delay (bin)]</column>
        </show-full-range>
      </table>
      <simple-id uuid='{BB5C4EA8-ED0A-4198-99B9-5A502C0A9671}' />
    </worksheet>
    <worksheet name='Walk Trips per Capita'>
      <layout-options>
        <title>
          <formatted-text>
            <run>Walk Trips Per Capita</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='database-for-tableau' name='federated.1xb3n6j0v4z0p918shy1f1pvgo02' />
          </datasources>
          <datasource-dependencies datasource='federated.1xb3n6j0v4z0p918shy1f1pvgo02'>
            <column datatype='integer' name='[Number of Records]' role='measure' type='quantitative' user:auto-column='numrec'>
              <calculation class='tableau' formula='1' />
            </column>
            <column aggregation='Sum' caption='Community Design' datatype='integer' name='[Strategy_Community_Design]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Fuel Price (Strategy)' datatype='integer' name='[Strategy_Fuel_Price]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Income (Strategy)' datatype='integer' name='[Strategy_Income]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Marketing and Incentives' datatype='integer' name='[Strategy_Marketing_Incentive]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Pricing' datatype='integer' name='[Strategy_Pricing]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Vehicles and Fuels' datatype='integer' name='[Strategy_Vehicles_Fuels]' role='dimension' type='ordinal' />
            <column aggregation='None' caption='Walk Travel Per Capita (bin)' datatype='integer' name='[Walk Travel Per Capita (bin)]' role='dimension' type='ordinal'>
              <calculation class='bin' decimals='-3' formula='[WalkTravelPerCapita]' peg='0' size='0.005' />
            </column>
            <column caption='Walk Travel Per Capita' datatype='real' name='[WalkTravelPerCapita]' role='measure' type='quantitative' />
            <column-instance column='[Strategy_Community_Design]' derivation='None' name='[none:Strategy_Community_Design:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Fuel_Price]' derivation='None' name='[none:Strategy_Fuel_Price:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Income]' derivation='None' name='[none:Strategy_Income:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Marketing_Incentive]' derivation='None' name='[none:Strategy_Marketing_Incentive:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Pricing]' derivation='None' name='[none:Strategy_Pricing:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Vehicles_Fuels]' derivation='None' name='[none:Strategy_Vehicles_Fuels:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Walk Travel Per Capita (bin)]' derivation='None' name='[none:Walk Travel Per Capita (bin):ok]' pivot='key' type='ordinal' />
            <column-instance column='[Number of Records]' derivation='Sum' name='[sum:Number of Records:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <slices>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='cell-w' value='20' />
            <format attr='cell-h' value='20' />
            <format attr='cell' value='20' />
            <format attr='cell-q' value='100' />
            <format attr='width' field='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Walk Travel Per Capita (bin):ok]' value='55' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
            <format attr='font-size' value='11' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='font-size' value='10' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <customized-tooltip>
              <formatted-text>
                <run>Æ&#10;</run>
                <run fontcolor='#787878' fontsize='14'>Number of Records:&#9;</run>
                <run bold='true' fontsize='14'>&lt;[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]&gt;</run>
              </formatted-text>
            </customized-tooltip>
            <style>
              <style-rule element='mark'>
                <format attr='mark-color' value='#75a1c7' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]</rows>
        <cols>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Walk Travel Per Capita (bin):ok]</cols>
      </table>
      <simple-id uuid='{9BB135DB-4865-4B5C-927D-9E3F3F0F22FB}' />
    </worksheet>
    <worksheet name='filters'>
      <layout-options>
        <title>
          <formatted-text>
            <run>Number of Scenarios</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='database-for-tableau' name='federated.1xb3n6j0v4z0p918shy1f1pvgo02' />
          </datasources>
          <datasource-dependencies datasource='federated.1xb3n6j0v4z0p918shy1f1pvgo02'>
            <column datatype='integer' name='[Number of Records]' role='measure' type='quantitative' user:auto-column='numrec'>
              <calculation class='tableau' formula='1' />
            </column>
            <column aggregation='Sum' caption='Community Design' datatype='integer' name='[Strategy_Community_Design]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Fuel Price (Strategy)' datatype='integer' name='[Strategy_Fuel_Price]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Income (Strategy)' datatype='integer' name='[Strategy_Income]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Marketing and Incentives' datatype='integer' name='[Strategy_Marketing_Incentive]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Pricing' datatype='integer' name='[Strategy_Pricing]' role='dimension' type='ordinal' />
            <column aggregation='Sum' caption='Vehicles and Fuels' datatype='integer' name='[Strategy_Vehicles_Fuels]' role='dimension' type='ordinal' />
            <column-instance column='[Strategy_Community_Design]' derivation='None' name='[none:Strategy_Community_Design:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Fuel_Price]' derivation='None' name='[none:Strategy_Fuel_Price:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Income]' derivation='None' name='[none:Strategy_Income:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Marketing_Incentive]' derivation='None' name='[none:Strategy_Marketing_Incentive:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Pricing]' derivation='None' name='[none:Strategy_Pricing:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Strategy_Vehicles_Fuels]' derivation='None' name='[none:Strategy_Vehicles_Fuels:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Number of Records]' derivation='Sum' name='[sum:Number of Records:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <slices>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]</column>
            <column>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='width' value='69' />
          </style-rule>
          <style-rule element='header'>
            <format attr='height-header' value='22' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='font-size' value='11' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='font-size' value='12' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <text column='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[sum:Number of Records:qk]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minheight' value='-1' />
                <format attr='maxheight' value='-1' />
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows />
        <cols />
      </table>
      <simple-id uuid='{564858E6-08DE-462D-AC5B-8E0D8D22114A}' />
    </worksheet>
  </worksheets>
  <windows source-height='30'>
    <window class='worksheet' name='filters'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
          <strip size='259'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]' type='filter' />
          </strip>
          <strip size='300'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]' type='filter' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:B:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Bicycles:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:C:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:D:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Demand_Management:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Fuel_Price:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Income:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:L:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Land_Use:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:P:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Parking:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:T:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Technology_Mix_and_CI:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Transit:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Vehicle_Travel Cost:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_name:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_name:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{B0844ECF-0094-4377-9960-0E2B5922A9F0}' />
    </window>
    <window class='worksheet' name='GHG Target Reduction'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='126'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]' type='filter' />
          </strip>
          <strip size='143'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]' type='filter' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:B:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:C:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:D:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:DVMT Per Capita (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:GHG Reduction (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:L:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:P:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:T:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Transit:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_name:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_name:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{F3A65E8F-AF0F-4602-82C9-771C9BA7AD9C}' />
    </window>
    <window class='worksheet' name='DVMT Per Capita'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='126'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]' type='filter' />
          </strip>
          <strip size='143'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]' type='filter' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:B:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:C:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:D:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:DVMT Per Capita (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:L:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:P:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:T:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Transit:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_name:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_name:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{98BE670E-4ADF-41B5-8B70-0582693197E0}' />
    </window>
    <window class='worksheet' maximized='true' name='Walk Trips per Capita'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='126'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]' type='filter' />
          </strip>
          <strip size='143'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]' type='filter' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:B:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:C:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:D:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:DVMT Per Capita (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:L:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Land_Use:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:P:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:T:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Transit:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Walk Travel Per Capita (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_name:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_name:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{5523D7CD-C29C-44EB-9B2A-7884BAB40787}' />
    </window>
    <window class='worksheet' name='Air Pollution Emissions'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='126'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]' type='filter' />
          </strip>
          <strip size='143'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]' type='filter' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Air Pollution Em (bin) 2:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:B:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:C:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:D:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:DVMT Per Capita (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:L:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Land_Use:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:P:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:T:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Transit:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Walk Travel Per Capita (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_name:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_name:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{48DE8574-B04D-424E-AF74-8AB17F083530}' />
    </window>
    <window class='worksheet' name='Annual Fuel Use'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='126'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]' type='filter' />
          </strip>
          <strip size='143'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]' type='filter' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Air Pollution Em (bin) 2:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:B:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:C:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:D:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:DVMT Per Capita (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Fuel Use (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:L:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Land_Use:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:P:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:T:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Transit:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Walk Travel Per Capita (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_name:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_name:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{22740D0A-CDE6-4F77-B044-51AB32B44DAF}' />
    </window>
    <window class='worksheet' name='Truck Delay'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='126'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]' type='filter' />
          </strip>
          <strip size='143'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]' type='filter' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Air Pollution Em (bin) 2:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:B:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:C:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:D:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:DVMT Per Capita (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Fuel Use (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:L:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Land_Use:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:P:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:T:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Transit:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Truck Delay (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Walk Travel Per Capita (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_name:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_name:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{7EC2A287-4D57-44A2-8B92-9B3885633825}' />
    </window>
    <window class='worksheet' name='Household Vehicle Cost'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='126'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]' type='filter' />
          </strip>
          <strip size='143'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]' type='filter' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Air Pollution Em (bin) 2:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:B:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:C:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:D:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:DVMT Per Capita (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Fuel Use (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:L:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Land_Use:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:P:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:T:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Transit:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Truck Delay (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Vehicle Cost (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Walk Travel Per Capita (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_name:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_name:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{B65B26CE-F72C-4CF4-A1E5-E22D55E61F5B}' />
    </window>
    <window class='worksheet' name='Low Income Household Vehicle Cost'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='126'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Vehicles_Fuels:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Pricing:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Marketing_Incentive:ok]' type='filter' />
          </strip>
          <strip size='143'>
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Fuel_Price:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Income:ok]' type='filter' />
            <card param='[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Strategy_Community_Design:ok]' type='filter' />
          </strip>
          <strip size='31'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Air Pollution Em (bin) 2:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:B:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:C:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:D:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:DVMT Per Capita (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Fuel Use (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:L:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Land_Use:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:P:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:T:ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Transit:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Truck Delay (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Vehicle Cost (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Vehicle Cost Low (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:Walk Travel Per Capita (bin):ok]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:category_name:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_label:nk]</field>
            <field>[federated.1xb3n6j0v4z0p918shy1f1pvgo02].[none:level_name:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{03F202BF-A728-420C-8C67-966879FD0364}' />
    </window>
  </windows>
  <thumbnails>
    <thumbnail height='384' name='Air Pollution Emissions' width='384'>
      iVBORw0KGgoAAAANSUhEUgAAAYAAAAGACAYAAACkx7W/AAAACXBIWXMAAA7DAAAOwwHHb6hk
      AAAgAElEQVR4nO3deXAU14E/8G9PzyVpdKMbcd+Y+7TBsY0t8IkPsI3t2Ck7JM6us5tNUt7a
      3WxtbX7Jbra2kk1lk92s7TLGjgFf2NgWYECAMTZGgBG3EIcMuqXROZp7pvv9/lDUQZYEGqZH
      I9TfTxV/qJH6vek309/p916/loQQAkREZDimeFeAiIjigwFARGRQDAAiIoNiABARGdQNGwBC
      CKiqGu9qEBHdsG7YAADAACAiisINHQBERHT9GABERAbFACAiMigGABGRQTEAiIgMigFARGRQ
      DAAiIoNiABARGRQDgIjIoMzxroAQAm1tbXA4HLBarRBCwO12Q1EU7XccDgfM5rhXlYhoWInr
      WVUIgePHj2PTpk145plnMH36dIRCIbz00ktIT0/Xfm/lypXIysqKY02JiIafuAaA2+3G3r17
      MX/+fG1dH7fbjeTkZHz3u9+NZ9WIiIa9uI0BCCFQXFyMZcuWITExUdvucrmQmpoar2oRERlG
      3K4Azpw5g3A4jJkzZ6KmpkbbHgwGUV1djd/+9rdQFAWzZ8/GsmXLYDKZEA6HEQ6Htd9VVRXB
      YDAe1SciuuHFJQA8Hg+Ki4vx8MMPo6mpCR6PBx0dHXC73Zg4cSJ+8pOfQAiBQCCAN954A3l5
      eZg+fTrMZrM2GCyEgKIoHBwmIrpOcQuA/Px8lJWVAQC+/vprOJ1OpKam4qabboIsy12VM5sx
      ffp0OJ3OeFSTiGhYk4QQIt6V2Lp1K0aNGoUZM2Zg7969kGUZU6ZMgd/vx1tvvYWnnnoKBQUF
      Pf6GVwBERNEZEgFw+vRpZGRkIC8vD52dnSgtLUVVVRVkWcbNN9+MiRMnQpKkHn/DACAiis6Q
      CIDrwQAgIooOl4IgIjIoBgARkUExAIiIDIoBQERkUAwAIiKDYgAQERkUA4CIyKAYAEREBsUA
      ICIyKAYAEZFBMQCIiAyKAUBEZFAMACIig2IAEBEZFAOAiMigGABERAbFACAiMigGABGRQTEA
      iIgMigFARGRQDAAiIoNiABARGRQDgIjIoBgAREQGxQAgIjIoBgARkUHFPQBUVUV5eTncbre2
      ze/3o6ysDCdPnkQ4HI5j7YiIhq+4BoAQAnv27MEbb7yBr7/+GkDXyf+ll15CbW0tKioqsG7d
      OqiqGs9qEhENS3ENgMbGRpSXl+Pmm2/Wtp06dQqFhYW47777sGrVKgBATU1NvKpIRDRsmeNV
      cDgcxscff4yVK1fi1KlT2vaqqipMnz4dkiQBAKZNm4bLly9j1KhREEJACKH9rqqqUBRl0OtO
      RDQcxC0ASktLkZubi1GjRvUIAI/HA4fDof3scDhQX18PoPcJX1XVHoEQa7WtHrx38JIu+xqX
      nYwH5o/SZV9ERNcjLgHQ1taGXbt24d5778XJkyfR2NgIVVWRlZWFhIQE+Hw+7Xd9Ph8SExMB
      ALIsQ5ZlAF3jB4qiwGwexJcgBdDcGdRlV9mpKmw2my77IiK6HnEZAzCZTLj99tvh9XrR2toK
      r9eLzs5O+Hw+5Ofn4/z581p3z/nz51FQUBCPahIRDWtxuQJITU3F7bffrv3s8XgwatQojB07
      FllZWfjjH/+ItLQ0BAIBtLS0YOzYsfGoJhHRsBb3+wAAYNasWSgsLATQ1ef/3e9+Fy6XC4qi
      4Pnnn9e6fYiISD9xGwS+0siRI3v8nJGRgRUrVsSpNkRExjAkrgCIiGjwMQCIiAyKAUBEZFAM
      ACIig2IAEBEZFAOAiMigGABERAbFACAiMqghcSMYERHFzqen6nD8Ukuv7QwAIqJhrt0bQG2r
      p9d2dgERERkUA4CIyKAYAEREBsUAICIyKAYAEZFBcRbQEOTxh/TZkQQk2Sz67IuIYsIXDENV
      hS77SrCZYZKkAf8+A2AI+vk7X+myH5tFxi+eWKDLvogoNv73k9NobPfpsq9/WjUHaUm2Af8+
      u4CIiAyKAUBEZFAMACIig2IAEBEZFAOAiMigGABERAbFACAiMigGABGRQTEAiIgMKm53Ajc3
      N+PEiRNoaWlBWloaFi5ciNTUVAghcOjQIXi9Xu13582bh5SUlHhVlYhoWIrbFcDBgwdhs9mw
      YMEC2O12vPrqqwiFQvB6vdi/fz9SU1O1f7Isx6uaRETDVtyuAO6//34AgBACeXl5OHToEMLh
      MNxuNzIzMzF37tx4VY2IyBDiFgCKouCrr75CTU0Nqqurcd9998Fut+PChQuoqanBq6++iuTk
      ZCxYsABjxoyBJEkIh8MIh8PaPlRVRTAYHLQ6B4IB3falKEqPbq5YEELEvAwiio7QaSVQAPD7
      /fBKSq/tV543rxS3AJAkCXa7HcnJybBarbh06RImTZqEadOmobCwEIqioKmpCRs3bsQzzzyD
      wsJCmM1mmM1dVRZCQFEU7efBYLP2fRCvhyzLSExM1G1/fZEkKeZlEFF0JNPAl2++FrvdjsTE
      3quB9neejNsYgMlkwsyZM1FUVITnn38e586dg9PphCzLSEtLQ2ZmJqZMmYJvfetbuHDhQryq
      SUQ0bMUlAAKBAE6dOoVAoKtLxe/3IxQKQZZlHDt2DNXV1Vr3zpkzZ5CXlxePahIRDWtx6QIy
      mUyorKxESUkJLBYLwuEwFi9ejMzMTHg8HnzyySfwer1QVRXTpk3DpEmT4lFNIqJhLS4BYLFY
      sHLlSoTDYQSDQVitVsiyDEmSMHr0aKxduxbBYBAmkwkWCx9pSEQUC3F9JOSVg7pXkiQJNtvA
      H2tGRESR41IQREQGxQAgIjIoBgARkUExAIiIDIoBQERkUAwAIiKDYgAQERkUA4CIyKAYAERE
      BsUAICIyKAYAEZFBMQCIiAyKAUBEZFAMACIig2IAEBEZFAOAiMigGABERAbFACAiMigGABGR
      QTEAiIgMigFARGRQDAAiIoNiABARGRQDgIjIoBgAREQGxQAgIjIoc7wKDgaDuHDhApqbm5Ge
      no4pU6bAYrEAADweD06ePAmz2YyZM2fCarXGq5pERMNW3K4ANm/ejLNnz8JqtaKiogKvvPIK
      FEWBz+fDyy+/DJfLhbq6Orz66qtQVTVe1SQiGrbidgWwZs0aAIAkSRBC4Ne//jUCgQBOnTqF
      8ePHo6ioCACwbt06VFVVYcyYMfGqKhHRsBRRADidThw/fhx33XUXvvjiC2zevBm33XYbVq5c
      CUmSIi5cVVW43W4cOXIEOTk5sNlsqK6uxsyZM7X9TZkyRQsAVVUhhOjx9+FwOOJyr5ei45WI
      ECL2dR+MMogoOuLavzJQitL3OVGofRcSUQDs3r0bfr8foVAIv/3tb/H9738fGzduxPz581FQ
      UBBhRRVs2LABly9fhtVqxXPPPQdZluH1epGYmKj9XlJSEurr67tehBA9uoO+GQixpqqKbvsS
      QkBR9NtffwajDCK6fkLHBFBVBYrSu2e/v/NkRAHQ3t6OsWPH4uzZs8jNzcWdd96JL774Am1t
      bREHgCzL+Pa3v41AIICvv/4ab7zxBl544QUkJCTA6/Vqv+f1epGUlKT9jSzL2guSJAlm8+D1
      YlnMAd32ZTKZYLPZdNtfnyQp9mUQUVSup/ekPxaLpc/PvEnue7g3okHg6dOnY9OmTfjd736H
      2267DYqioKGhAZmZmRFVsvubvCzLSExMxPTp05GRkQGn04nCwkJUVFRACAEhBCoqKjBy5MiI
      9k9ERNcW0dfnJUuWQAgBl8uFFStWoKOjA/fddx9yc3MjKtTr9eKNN97ApEmTkJWVhcbGRrS0
      tCA7OxuZmZn4v//7PyQnJ8Pv98PtdmP06NER7Z+IiK5tQAHg8/m0vuS5c+cCAAKBAOx2O+68
      806tO2agkpKS8OSTT6K8vBz19fVIT0/H3/zN38ButwMA1q5di6NHjyIxMRHf+973tG4fIiLS
      z4AC4Gc/+xnOnj2LcDiMYDCoDdIqigKTyYR169YhLy8vooJTU1OxePHifv/vjjvuiGh/REQU
      mQEFwC9/+UuoqoqPPvoIoVAIq1atAtB1FfCLX/wCDocjppUkIiL9DWgQODExEQ6HAy6XCwDg
      cDjgcDiQkZGBcDiMS5cuxbKOREQUAxENAi9cuBD/8i//gvT0dIwaNQplZWW4dOkSRowYEav6
      ERFRjEQUAHPmzME//dM/YfPmzXA6ncjNzcXPf/7ziGcBERFR/EUUAN2Lt/3mN7+JVX2IiGiQ
      RHQjWG1tLf7rv/6LywsQEQ0DEQXALbfcgkWLFuG3v/0tWltb0d7ejvb2dgYCEdENKKIuoE8/
      /RSfffYZ3G43vvzyS0iSBLvdjt/85jfIycmJVR2JiCgGIgqAZcuW9bp5S5IkpKSk6FopIiKK
      vYgCwGazoba2Ftu3b0dbWxsmTZqEe+65h0s1EBHdgCIaAygvL8ePfvQjAF0PaikrK8OLL77Y
      Y/lmIiK6MUQUANu2bcMzzzyDH/7wh3j00Ufxb//2b5AkCefOnYtV/YiIKEYiCgCr1aotBwEA
      4XAYfr8fFotF94oREVFsRTQG8NBDD+HHP/4xzp07h+zsbJw8eRIjR47ExIkTY1U/IiKKkYgC
      oLCwEK+88gq++OILtLa24gc/+AHmzp0Lq9Uaq/oREVGMRBQAFy5cQGVlJR544AEAXQ+Kefvt
      t/HYY49pD3MhIqIbQ0RjALt27YLP59N+ttvtKC0tRW1tre4VIyKi2IooACwWCxoaGiCEANB1
      BdDW1gaTKaLdEBHREBBRF9ADDzyAv/qrv0JVVRXy8/NRWlqKKVOmYNSoUbGqHxERxUhEAZCT
      k4N169ahpKQELS0teP7557Fo0SLeCUxEdAOKuO+m+6Hw48aNw8yZM1FWVqZ1CRER0Y0jogCo
      qKjAD3/4Qxw6dAjvvfcezGYz1q1bh9bW1ljVj4iIYiSiACguLsZ3vvMd/PznP4fZbIbNZkNy
      cjKamppiVT8iIoqRiAIgISEBLpcLqqoCAFwuF2pqapCYmBiTyhERUexENAi8atUq/OhHP8L+
      /ftx4cIFPPfcc1i4cCFGjhwZq/oREVGMRDwL6E9/+hOOHj0Kl8uFsWPHYty4cZAkKVb1IyKi
      GBlwAPh8Ppw7dw6yLGPBggWQZRnhcBivvfYa7r33XuTn50dUcCgUwokTJ3Dx4kWYzWYsWbIE
      OTk5UFUVBw4cgNvt1n538eLFSEtLi2j/RER0dQMKAK/XixdffFFb8mH27Nn4h3/4B/z+97/H
      xYsXsWbNmogL3rJlCywWC2655Ra0tbVh/fr1+Lu/+zuEw2GUlpbi7rvv1n7XZrNFvH8iIrq6
      AQXAiRMn4HK58Oabb0JRFKxduxY//elPIcsyfvWrX8HhcERccFFREVJTUyFJEgoKCrB37174
      /X74/X5kZGRg+vTpEe+TiIgGbkAB0NTUhKlTp8LhcEAIgZkzZ6K1tRW/+MUvruvkD0Dr0lEU
      BV9++SWsViuSkpJQV1cHj8eD0tJSpKamYsyYMbDZbJAkCYqiaDOQuv/2yp9jLRQO67YvVagI
      BoO67a9PQsS+DCKKjo430oZCIQSDvcdkVaXv8+SAAkAIgba2NpSVlQEAOjo6MGHCBJw/fx4m
      kwlTpky5rm4aj8eDd999FwDwne98B2azGTk5OZgzZw4CgQCOHz+OLVu24K//+q+RkpICSZJ6
      LDwnhBjUhehMJv0GuyVIg1J3LtRHNNTpd14xmUx9fub7m6gzoABITU1FXV0dfv3rX2vb6uvr
      ceDAAdhsNvzHf/wHsrOzI6poZ2cnXnrpJSxZsgSLFy/WKpiRkYElS5YA6DrB79y5E8ePH8et
      t97a6+QvhIDZHNFEpqjIJv3WPJIkKfZ1H4wyiCg6Ok6ilGW5z8+81M+X1wGdHW677Tbceuut
      /f7/9XzL3L59OxYtWtTj5A90dTelp6drzxn2+/1IT0+PeP9ERHR1AwoASZJ0XfFTCIELFy6g
      ra0NX3/9tbZ95cqVqKysxMGDB5Gfnw+fzweXy9VjRhAREekjbv0DP/jBD6AoSo9tDocDixYt
      wrRp09DS0gJZlpGXl6ddDRARkX4GFAD//u//jh/84Ac4evQoFEXBihUroipUkiRkZGT0+/8p
      KSlISUmJqgwiIrq6AQXApUuXcOHCBdTV1SEcDve4SxcAEhMTOduEiOgGM6AAeOaZZ/Dzn/8c
      bW1tUFUV69at0/4vISEB69evR0FBQcwqSURE+htQACxduhRbt27Fli1bEA6HsXr16ljXi4iI
      Yizih8IrioLz58+js7MT+fn5yMnJ4WqgREQ3oIgCwO1245//+Z/R1NSEhIQEdHR04P7778ez
      zz7LMQAiohtMRAHw/vvvIysrC//5n/8Ju92Ouro6/OQnP8Htt9+O8ePHx6qOREQUAxEFQEND
      A+bMmYOEhAQAQH5+PnJzc9HW1haTyhERUexEFAA333wz/vjHPyI1NRXZ2dk4cuQI6uvrMW7c
      uFjVj4iIYiSiAPjWt76FYDCIDRs2oL29HRMmTMCvfvWrq97URUPTxYYOuP36LG89MS8Vibbh
      vejciUst0GPRXpMEzBidqcOeiKIX0afWZDKhqKgIRUVFEEJAkiTOALpBlZyoxcUGly77+tv7
      bkKi7fqeC3Gj2LD/vC7LtlvNJgYADRkRf23rPuHzxE9EdGOLaO5mW1sbnE5nrOpCRESDKKIA
      2Lt3L/7f//t/EDo+woyIiOIjogAoKiqCxWLBzp07oaqq9lQuBgIR0Y0nojGAr776Cp2dnfjl
      L3+JDz74AJIkwWaz4Wc/+xmysrJiVUciIoqBiAJgypQp+N73vtdjm8lkQnJysq6VIiKi2Iuo
      Cyg3Nxc33XST9uT56dOnIzU1FXa7PVb1IyKiGIkoAKqqqrB27VqsW7cOL7/8MlRVxa9//Wt0
      dHTEqn5ERBQjEQXABx98gJUrV+IPf/gDzGYzEhMTkZKSgvr6+ljVj4iIYiSiABBCwG63azeB
      +Xw+NDQ0wGq1xqRyREQUOxENAq9cuRJ///d/j7KyMlRVVeHHP/4x8vPzUVhYGKv6ERFRjEQU
      AOPGjcNLL72Ezz//HOPHj8eYMWOwePFiWCyWWNWPiIhiJOIuII/Hg0AgAACwWq18EhgR0Q0q
      orP38ePHsXbtWhw7dgxOpxP//d//jV/+8pcIh/VZVpiIiAZPRF1An376KR599FGsXbsWkiSh
      vb0d3//+91FTU4MxY8bEqIpERBQLEV0BTJgwATabTZsFlJaWhvz8/Kgq0N9aQlxjiIgotgZ0
      BfC73/0OVVVVCAaDuHjxIo4fP679X319/XWtA9TR0YF9+/ahtrYWQNfTxqZNmwYAOHHiBD7/
      /HOYTCasWLECY8eO5fMHiIh0NqAAWLp0KVyuvp8eJcvydZ2ct2/fjvHjx2P58uVwuVx49dVX
      MW7cOLhcLpSUlODZZ59FIBDAunXr8NOf/pTLTRAR6WxAATBv3jzdC3700UchyzIAwGazwWaz
      IRQK4fjx47jllluQkZEBIQRuuukmlJeXY86cObrXgYjIyCIaBD569Cj+8R//ET6fT/vWn5CQ
      gHXr1kU8FtB98g8EAti8eTNGjRoFh8OBlpYWTJ48GUDXYycLCgrQ0tICAAiHw1AURduHqqoI
      hUIRlRuNYDCo274UVYHP59Ntf30Sot8yVFXVrZhgIBj71zKM8FjRlYSq31hnIBCAz9T7s62E
      lT5+O8IA2L9/Px555BE88cQT2jZJkpCUlBRhNbvU1tbi7bffxqxZs3D77bfDZDJBUZQe9xZ0
      bwO6QqM7OICuQDCbI36s8XWzWPULAJPJFPtuLQn9lqHn/RsWq5VddBHgsaIrSSb9xjetVivs
      dluv7VeeN68U0dnzpptuwrlz55CSknJ9tbtCfX09NmzYgNWrV/cY5E1LS0Nra6u2vERLSwvS
      0tIA9HwQvRACkiQN6uCwBP3KkjAYdR+c4yNJ4CB9BHisKFb6PSf285aLKAAmTJiA3/3udzh/
      /ry2zWq14sUXX0RmZmZEFd2xYweKioowevRorTvCZDJhypQp2Lt3L6ZPnw5FUXDs2DGsXbs2
      on0TEdG1RRQA27Ztw9SpU3HPPfdo22RZRmJiYkSFCiHgdDqxe/du7Nu3T9v+9NNPY+zYsTh5
      8iT+8Ic/QFEUzJ8/X7sCICIi/UQUAGlpaVp/fTQkScKLL77Y7/8/9NBD8Hg8kGUZCQkJUZVF
      RER9iygAJk+ejN/85jc9Bl7NZjOWL18Oh8OhW6UkSdJ1f0RE1FtEASCEwIwZM1BZWalts1qt
      XAyOiOgGFFEAzJs3LyY3hRER0eCLKAAOHDiAjz/+uMc2m82Gv/3bv0VGRoauFSMiotiKKAAK
      Cgp6DAC3t7dj165dsNl633hARERDW0QBMHr0aIwePVr7WVVVfPrpp2hra7vuu4GJiCg+IgqA
      jo4OOJ1O7edgMAin04n29naMHDlS98oREVHsRBQABw8exPr167WfJUnCvHnzMGHCBL3rRURE
      MRZRABQVFWHZsmU9d2A2c20TIqIb0IACoL6+HhUVFX3+nyzLmD9/Pu/YJaJBU9/mRVllsy77
      KhyRhBmjI1vLbLgYUABUV1ejuLi41/ampiZcunQJGzdu5BgAEQ0ap8uHT0/X6bKvBROyGABX
      s3DhQixcuBBA193Ara2teOedd1BTU4OnnnoKI0aMiGkliYhIfwMeAxBCoL29HW+99RZ27tyJ
      5cuX4w9/+ANP/kREN6gBBUBbWxs2bdqknfhfe+01LtFMRHSDG1AA7N+/H2+++SbuuusuBAIB
      vPbaa9r/WSwWfPvb32YgEBHdYAYUANOnT8cLL7zQ9w7M5n6fN0lEREPXgAJg/PjxGD9+fKzr
      QkTDwJnqNlQ1u3XZ18zRGcjP4DIzsRLRjWBERNdSUdeOLysaddlXdqqdARBDpnhXgIiI4oMB
      QERkUAwAIiKDYgAQERkUA4CIyKAYAEREBsUAICIyKAYAEZFBMQCIiAwq7ncCt7a2wmw2IyUl
      BUDXstONjY0IhULa7+Tk5MBqtcarikREw1LcAqCjowO7d+/G4cOHsWrVKsyfPx8AEAgE8Prr
      r2Ps2LHa7955553IzDTmE3uIiGIlbgFQXl6O/Px83H777RBCaNs9Hg8yMjLw2GOPxatqRESG
      ELcAWLx4MQBg7969Pba3t7fDbrejo6MDFosFCQkJkCQJQFf30JVhIYSAqqqDVucry456XxiE
      ul/t+Oj3Uga9HW50w/5Y6fk56ee9JVQd38AYPm0i1H4+i/0crriPAXyTLMvwer14++234Xa7
      MXLkSDzyyCMwm81QVRWKomi/qyhKj59jLRQOXfuXBkhVVQSDQd3215/+ylCFfm/4UCg0KK9l
      uBjux0rR8WQaDit9Hq9wOKxbGarSdxmDRc8vlsFQCH29lP7Ok0MuAMaMGYPnn38eQFel33zz
      TZw8eRJz5syBLMvaw2eEEDCZTDCbB+8lWC36vUlkkwy73a7b/vokSf2WYTLpNwHMarXG/rUM
      I8P9WOn5gCiLxdzn8bJYLbqVYZIH4bN4Fd09HHqw2ayw2229tsvmvttkyE0DvfLyRZZljB49
      Gi6XK441IiIanuJ2BXD58mV4vV40NjbC5XKhvLwcBQUF+PLLL+HxeDBt2jT4fD588cUX2hUB
      ERHpJ24B4HQ60d7ejhEjRgAAamtrkZGRgTvuuANnzpxBRUUFZFnG2rVrOQWUiCgG4hYA3fP+
      +zJ79mzMnj17EGtDRGQ8Q24MgIiIBgcDgIjIoBgAREQGxQAgIjIoBgARkUExAIiIDIoBQERk
      UAwAIiKDGnKLwRFR7JRVNuPwxSZd9vXUrRORZNdvUbahxh8M441953TZV0qCFWuWTtBlX3pi
      ABAZSKvbjwv1+iyuGNZ5Tf6hRlGFbscqM7n3Cp1DAbuAiIgMigFARGRQDAAiIoNiABARGRQD
      gIjIoBgAREQGxQAgIjIoBgARkUExAIiIDIoBQERkUAwAIiKDYgAQERkUF4OjG5rbH8KF+g5d
      9pWSaMW4nBRd9kV0I2AA0A3N2eHDxv0XdNnX1JFpDAAyFHYBEREZ1JANAFVVIcTwXm+ciCie
      4tYFJIRAU1MTdu7ciXnz5mHatGna9iNHjuDgwYOQJAlFRUWYNGkSJEmKV1WJiIaluF0BHD58
      GJs2bYLf74fH49G2NzQ04PPPP8czzzyDxx9/HJs3b4bf749XNYmIhq24XQFMnDgRc+fOxf79
      +3tsP3HiBG6++WakpqZCCIEZM2agvLwcc+fOjVNNiYiGp7gFQHp6ep/bW1tbMWXKFACAJEnI
      y8tDa2srACAcDkNRFO13FUVBKBSKfWX/LBAM6LYvRVXg8/l0219fhBD9lqGqqm7lBAIB+Hyy
      bvuLrOygbvtSFDXmbQKg3zKqWrzwBsK6lDFmRBLs1t5tEgrrs38A8Pv9sEpKr+2KjmWEgqE+
      j1cwqGe79/1Z9OvUFgCgqv1/FoWOz1b2+/3wmXp/tpVw73YChuA0UFVVYTL9pWfKZDJpJytZ
      liHLf3lTK4rS4+dYs1r1CxvZJMNut+u2v75IktRvGVce42jZbLaYv5b+WG16tolpUF5Hf2Xs
      O/M1LjTo8xDyv7n3JqSl9C7HYtbvI2+32WC3W3ttl3Usw2K19Hm8rJbe5V4vWe77s6hI+gWA
      ydT/Z1Ey6Te+abfb+26Tfs6TQy4A0tLS0NzcjMLCQgCA0+nUrhauHAjuniE0mIPDepc0GHUf
      rOMTr0F6XUuV2CYRFhC/46VzsX2VcSN+3vstp5+ih9w00GnTpqG0tBShUNel37FjxzB58uR4
      V4uIaNiJ2xXA+++/j7q6OrjdbphMJpSWluLuu+/G2LFjUVhYiP/5n/+BoihYsmQJUlJ4dyYR
      kd7iFgCPPPJIv/937733wufzwTRIfbJEREY05MYAgK4+rMTExHhXg4hoWBtyYwBERDQ4GABE
      RAbFACAiMigGABGRQTEAiIgMigFARGRQDAAiIoNiABARGRQDgIjIoBgAREQGxR5/5KMAACAA
      SURBVAAgIjIoBgARkUExAIiIDIoBQERkUAwAIiKDYgAQERkUA4CIyKAYAEREBsUAICIyKAYA
      EZFBMQCIiAyKAUBEZFAMACIig2IAEBEZFAOAiMigGABERAZljncFvkkIgbq6OgSDQW1bfn4+
      bDZbHGtFRDT8DLkACAQC+NOf/oRJkyZp21JTUxkAREQ6G3IB4Ha7kZmZiUceeSTeVSEiGtaG
      XAC0t7fDarXC6XTCbrfD4XBAkiQAXd1DQgjtd4UQUBRl0OqmClW3fQkMQt2vdnyuOI7RUtXB
      bYdvlq0XITAor2MwyhBC7bMcoWu7x76M/j7jQsd2Rz/trqo6ft6v9t7S8aVE2iZDLgCsVitU
      VcXWrVvR0dGBzMxMPP7447BYLL1enKIoMJkGbxw7HNbvgytUgXA4rNv++tNfGaqOH1IlHB6U
      19KXsKJfuUKow6ZNwmGlz3IUHU9q4VAIYYvUa7ueJ06ln9ehZ7ur/bR7SNf3Qv+fd6FjAoTD
      YYTDvc+J/bXJkAuAUaNG4dlnnwXQdYLftGkTTpw4gXnz5kGWZciyDKAr0UwmE8zmwXsJVktA
      t32ZTKbYj2tIUr9l6BmcFqslbmM0N1ybAP23idT7ZHq9LJa+28T858+PHqw2G2w2a6/tso5l
      mC3mPl+HxWLRrYz+2j0s9PuMSFf5LEo6trvVau2znP7aZMhNA1UURbtcMZlMKCgogMfjiXOt
      iIiGnyF3BbBz5050dHRg2rRp8Pl8+Pzzz/HCCy/Eu1pERMPOkAuAoqIinDt3DpcvX4Ysy3jh
      hReQnp4e72oREQ07Qy4AzGYzpk2bhmnTpsW7KkREw9qQGwMgIqLBMeSuAK5X6blGlJ5v0mVf
      t07Nw5xxI3TZl5EdOt+Eg+caddnX0qm5mDsuS5d9EVGXYRMALl8INS36zBbq9Id02Y/RubxB
      /drExzYh0hu7gIiIDIoBQERkUAwAIiKDYgAQERkUA4CIyKAYAEREBsUAICIyKAYAEZFBMQCI
      iAyKAUBEZFAMACIig2IAEBEZFAOAiMigGABERAbFACAiMqgh+TyAYDCIqqoqyLKMUaNGQZbl
      eFeJiGjYGXIBEAwG8frrr8PhcCAYDMJms+Gxxx6DycSLFSIiPQ25s+rZs2fhcDiwZs0afPvb
      30ZzczMaG/V5rCAREf3FkAuAixcvYvbs2ZAkCbIsY8aMGaisrIx3tYiIhp0hFwButxspKSna
      z6mpqejs7IxjjYiIhqchFwA2mw2BQED7ORAIwG63x7FGRETD05ALgNzcXK3LRwiByspK5Obm
      xrlWRETDz5ALgFmzZqGsrAxnzpxBWVkZampqMGHChHhXi4ho2Bly00BTU1PxxBNP4Msvv4Qs
      y/jud78Ls3nIVZOI6IY3JM+sI0eOxKOPPhrvahARDWtDrguIiIgGx5C8AuiPqqpQVbXHz+Fw
      GACQlWzFzFFpupSTmWTR9nslu0XSrYyCjMQ+ywCAWaPTIYSIugyLbOq3jHHZSUiy6pP/Nlnq
      s5ysFB3bxGGNeZvkZyT1e7xmjtKnTcxXbRMHEvVqE3M/bZJs0+14maD2WUZ+ml23MlITzH2W
      kWyT9fsspif03SZC1a0Mh73vcwoATMpLRk6KTZdyTBARtYkk9HhXD5JvBoAQApIkxbzMWC9D
      MVzK6H4rsU2GThmD0SaD1e78vA/cQNvkhroCMJlM2oETQkBRlJgOEHcHTqwHof1+P6xWa0zL
      CAaDPY5fLHR/84jl8RqMdgfYJpEIhUIwmUwxXbSx+2rfYrHErAxgcNo9EAhAluWYhtlA24Rj
      AEREBsUAICIyqBs6AAZyyakoSr//5/P5rvq3kiQN6NJZUZR+BwgDgUCPcYu+DOSSU1XVfstQ
      FKXH8hl9MZvN17zkFEL0W1chBPx+/1X/XpblmLcJgAF1A8S7TVRVNUybmM3mAXVjDdZn8XrL
      AKJv93A4jFAodNW/H8j792rtrqrqNdt9oG1ywwaAJEkD6kMrKSlBZWVljwYTQuDMmTMoLi6+
      ZhkDOYgnT57EoUOHeg1QO51ObNiwAcFg8Kp/P5AyWltb8eGHH/bal9/vx7vvvouGhoZrlnGt
      4xUMBrFlyxa0tbX12K4oCvbt24cjR45c9e8H2ia7d+/GxYsXe7VJeXm5bm1y6tQplJaW9jgh
      CCHQ3NyMDRs2XPPkPJAy2trartom9fX11yzjWscrFAphy5YtaG1t7bG9u00OHz581b+Ptk3O
      nj2Ljz/+WJcytm7ditra2l5llJWVoaSk5JplDKRNDh06hOPHj/f6LNbW1mLDhg3X/PuBlFFT
      U4MdO3b0mm3T2dmJt99+u1db9VXGtY6X1+vFli1bei2EGQ6HsWPHDpw5c+aqfz/QNpH/9V//
      9V+v+Vs3sPz8fHz66aeor6/H2LFjAQA7d+5EbW0tVq5cqcvgWHZ2Ni5cuIDS0lKMHz8eZrMZ
      Z86cwY4dO7B69WokJydHXUZCQgIkScKHH36IgoICOBwONDY24q233sKSJUswbty4qMswm83I
      zMzEBx98AJvNhqysLO1klpaWhiVLlugycJWfn499+/ahtrYWY8aMgSRJ2LVrF6qrq3Vvk4MH
      D2LcuHGwWCwoLy/HJ598glWrVvVYcfZ6Xdkm+fn5SE5O7tEm48ePj7oMWZYxYsQIfPDBB7Ba
      rcjOzu7RJkuXLtW1TWpqajB27FitTS5fvowHH3xQlzbJz8/HJ598gs7OTowcORKKoqC4uBgu
      lwv33nuvLoPh+fn5KCsrw4kTJzB+/HjIsowjR47gwIEDeOyxx2CzRT/dMiUlBR6PB9u2bcOY
      MWOQkJCAqqoqvP/++7jrrrtQUFAQdRlWqxXJycnYsmULkpOTkZmZCbfbjY0bN2LcuHGYO3eu
      Lu1+Q00DvV5CCBw9ehTHjh2DyWTC1KlTsWjRIl1H4YUQqK6uxo4dO5CamgqTyYSHHnpI99kX
      nZ2d+OCDD5CUlIT29nasWrUKaWn6zFXupigKtm/fDo/Hg7a2Nixfvlw7Keil+5tfWVkZTCYT
      pkyZgsWLF+teRk1NDT755JNBa5O2tjasXr36hm2TY8eO4ejRo5BlGZMnT8aiRYt0naWkqioO
      HDiAixcvIhQKYf78+Zg1a5bur+PixYvYu3cvkpKSkJaWhhUrVug6S0kIgfb2dmzevBkZGRno
      7OzE6tWrkZSUpFsZQFd35datWyGEQGNjIx566CHk5eXpd7yEQaiqKg4fPiz27NkjVFWNWTlO
      p1OsX79eKIoSszLC4bD43//9X+H1emNWhqqqYuPGjaKmpiZmx0tVVXHkyBGxe/fumLZJc3Oz
      eO2114ZFm2zatElUV1fHtE2++uorUVJSEtMy9uzZIw4fPhzTdr98+bJ45513YlpGKBQSv//9
      70UwGIxZGYqiiJdfflm0t7fr/lqG/RXAvn37UFVVBaDrYTOKoiA1NRUAMGLECNxzzz1Rl3H2
      7FmtLzYcDqOlpQU5OTkAui7hV61aFfWlZ1tbW4/+8bq6Ou2bgCRJuP322zFy5MioyggGg9i6
      dSvcbjcAoKmpCenp6dqg1bRp0zBv3ryoygCu3iaZmZm49957oy7jyjZRFAXNzc092uSRRx6J
      +jkT7e3tPfrHr2wTALjjjjuibpNQKITi4uKYt8lnn32Gy5cvAwA8Hg/C4bDubbJjxw40NTUB
      ADo6OmA2m7VvzCNHjsQdd9wRdRnHjh3DyZMnAXS9n10uF0aMGAGga/B1zZo1UZdRX1/fY8yi
      trZW6/YxmUxYvnw5srKyoirD5/OhuLhYG+ytr69Hbm6u9t6aO3cupk+fHlUZgAG6gILBYL+j
      6ZIk6dInGA6H+73NG+h6yE20l2yqql51MNlisUR9iSuEQDAY7HeGgyzLutyIwzYZuOHUJoFA
      oN/XYTKZdLkBKxQKXXUmkB4Pl1IU5aozfaxWa9TdZkKIq05UMJvNunRlDvsAALpOBq2trWht
      bYUQAunp6cjMzNT1rkJVVeFyueB0OhEKheBwOJCdna3LiaabEAJerxdNTU3wer2w2+3Izs6G
      w+HQtYxgMAin0wmXywVZlpGVlYXU1FRd+1DZJpGVMVzaJBQKoaWlRZtplpmZiYyMDF3HZRRF
      QUdHB5xOJxRFQUpKCrKysnQJsW5CCLjdbjQ1NcHv9yMxMRHZ2dlITEzUtd0DgQCamprQ2dkJ
      i8WC7OxspKSk6DYuM+wDoKGhAcXFxdqbWZIktLW1wel0oqioCGPGjIm6DL/fj82bN8NkMiE7
      OxtWqxWdnZ2oq6vDjBkzsHDhQl2+be7atQv19fXIy8tDUlISvF4vGhsb4XA48OCDD+ryrePU
      qVP48ssvkZ+fj9TUVIRCITidTvj9fjz88MO6zJ4ZrDZ5//33AQA5OTkxaRMhBHbt2oXa2lrk
      5+f3aJOkpCQ89NBDurTJ6dOnceDAAeTl5SEtLQ2hUAjNzc3w+Xy6tUljYyM+/vjjPtvkrrvu
      0mbQRePy5cvYuXMnsrKykJ7etbhea2srWlpa8MADD+jy5D+Px4N3330XCQkJyMrKgsViQUdH
      B+rr67Fw4ULMmjUr6jLC4TC2bduGtrY25OXlITExER6PR+umWb58uS7tfvjwYZw4cQJ5eXlI
      SUlBMBhEU1MTVFXFww8/jMTExKhfy7AfBF6/fr1ob2/vtd3j8YhXX31VhEKhqMvYtm2bOHfu
      XK/tqqqKP/3pT6K5uTnqMs6fPy+2bt3aaxBIVVXx+eefi0OHDkVdRmdnp1i/fn2fx6S6ulq8
      9957UZchRFebtLW19dru9XrFq6++qsuA2vbt20VFRUWv7aqqijfffFM4nc6oy7hw4YIoLi7u
      s02++OILUVpaGnUZbrdbvPbaa322SU1NjXj33XejLkMIIV5//fWYt8nLL78sPB5Pr+0ul0us
      W7dOhMPhqMt49913RW1tba/t4XBYvPzyy8Ln80VdRllZmdi3b1+v7aqqih07dogzZ85EXUZz
      c7PYuHFjn8fk7NmzYtu2bVGXIYQQN+yNYAMlhOhzHn5CQgJsNts179obCLfbrQ0wXkmSJGRl
      ZcHj8URdRkdHB/Lz83t9a5UkCQUFBejo6Ii6jGAwiJSUlD4vxzMyMq5589RA9dcmdrsdNpvt
      qn33A2WENklPT9etTVRV7bdN7Ha7Lp8Ts9mMhISEXtsdDgdMJtM1784eCK/Xi+zs7F7bTSYT
      MjIyBnQ38LW4XC7k5eX12i5JEvLy8uByuaIuw+v1IjMzs88uvuzsbHi93qjLAAxwI5jL5cKJ
      EyeQmpoKSepaI72trQ1ffPEF7HY7pkyZEnVXQEJCAnbs2IH09HTIsgxVVeF2u3HmzBlUVlbi
      lltuibqvNjU1Fdu3b0diYiIsFovW91xdXY3du3fjzjvv7PPDFQmr1YqTJ0+io6MDSUlJkCRJ
      64PctWsXpk+f3udJNVLdbZKWltajTQ4cOACbzYapU6fq1iZpaWm92uTixYsxb5OSkhLcdddd
      urTJ6dOn0d7ers2YubJNbrrpJl3apLOzE8ePH++zTaxWqy5t0tDQgMrKSq3LKhQKoa2tDXv2
      7EFubq4u9zVYLBaUlJQgIyMDJlPXsxdcLheOHz8Op9OJBQsWRF2Gw+HAJ598AofDAbPZDCEE
      PB4PKisrcfDgQdx+++1RjzckJiaitLQUwWBQew/5/X40NDRg586dWLBgATIyMqIqAzDAGICq
      qjh//jzOnDkDt9sNIQQcDgcmT56MKVOm6DKIJoRAQ0MDysrK0NbWBkVRYLPZUFhYiLlz5+oy
      86D7TXbo0CE0NjYiFArBbDYjKysL8+fPR3p6etRlAF3fOE+cOIHKykr4/X6YTCakpqZi1qxZ
      KCws1GWAS1VVXLhwAadPn+7RJpMmTcLUqVPZJt8wmG1y5swZdHZ2xqRNFEVBeXk5zp07p12B
      ORwOTJ8+HRMmTNBlYFP8+ea/Y8eOoaOjA6qqIiEhAWPGjMGsWbN0mWkkhEBHRwcOHz4Mp9Op
      LVOdm5uLefPm6TImA3Sd8MvKylBVVaUtIZ2eno45c+b0mBIajWEfAFcSf55N4fP5tO4GPQ7i
      YKyHDvxlLfzutcSDwSACgQCSkpJ0vVuzu5xwOAyTyQSLxRKTtcu7F84SQsBqtcZ8jfRuHo8H
      CQkJup1wgsEgrFZrj7qHw2FIkqRbmHU/RESSJKiqCq/XC6vVGtO162tra5GXlxez5xV0T6PV
      Y9pkf0KhELxeLywWi7Z0R7QURYGqqjF/NsE3P+/hcBg+nw8JCQm6zZq6oR4Icz2OHj2KSZMm
      weFwoLm5Ge+88w4yMjLg8Xhw6623YvLkyVGXUVpailOnTmHp0qWYMmVKTB6+IYTAvn37cP78
      eRQWFmLBggXYvn07QqEQkpKS8Oijj+ry5u7s7MSePXvQ0NCAYDAIs9mM5ORkLF68GOPHj9el
      jEAgoN0M1h0uoVAI2dnZuPPOO/WZ3XAVu3fvxrJly+BwOKLaj6qq2LZtG1pbW5GQkID7779f
      u1wvLy+HLMuYNm1aVGWIPy9c+PnnnyMlJQUPP/wwPvvsMzQ0NCAQCGD16tW6XGn4fL5e8+f3
      7t2L+++/H1arVZc22bdvH5YuXQpZluH1evHee+8hHA5DCIFHHnlEl9dRUVGBtLQ05OTkwOPx
      YNOmTUhISEAwGMTUqVOxePHiqMuorKzExx9/jFtuuQVz586NyRckIQROnDiB0tJSpKen4557
      7kFxcbF2L8Xjjz8edfciYIAAOHnyJGbNmgUhBHbs2IFHH30U2dnZCAQC2LBhg7Z4WzQCgQCW
      LVuG2tpaHDx4EPPmzcOsWbN0/VbjdDrR2NiItWvX4tixY3j//fexZs0aOBwOlJSUoKKiAlOm
      TImqjFAohPfeew+33XYbVq5cidraWpw/fx4LFizAJ598glAohKlTp0b9WroX0brrrrt6POHt
      /PnzeP/99/Hkk09Gfew8Hk+/A356DAQCXXf+KoqCp59+GhcvXsSHH36Ixx57DCaTSbuyiVYw
      GERpaSmeffZZNDQ0YMuWLUhNTcXTTz+N2tpafP7553jggQeiLuf111/Xroq7VVZWYuvWrRgx
      YgRWrFgRdRk1NTVQVRWyLOPgwYOYOnUq5s+fj9raWmzduhVPPfVU1CfSQ4cOaXf7HjhwAIsW
      LcKMGTOgqipee+01zJ49O+ruv3A4jEWLFkGWZaxfv15bW0zPIPB6vTh+/DieffZZNDU14ZVX
      XsFTTz2F7OxsnDhxAocOHcJtt90WdTnDfhYQ8JfnYobDYW2GgM1mQ1JS0jWXah4ou92OO+64
      A9/5znfg8XjwyiuvoLS0VJcZLUDXreCTJ0+GJEmYMGECEhMTkZycDEmSMHHiRNTU1ERdRmdn
      J9LT0zFu3DhIkoScnBzU1NTA4XDgnnvuwfHjx3V4JV2zZ2bPnt3jJC9JEiZNmgSLxaLLCfrE
      iRN444038MUXX/T6V1tbG/X+AaClpUXrg58wYQJyc3Nx8OBBXfbdze/3Iz09HVarFaNGjUJr
      ayumT58OSZKQmZmpy2wmALjnnntgMpkwb948PPHEE3jiiScwefJkPPbYY7qc/L+pqalJm4DR
      vTLo1e7gjUT3593r9fZYomHEiBG6zZ4xm81YsGAB1q5dC4fDgddffx179uzR7ctFR0cH8vLy
      YLFYkJOTg+TkZO3cVVhYiObmZl3KGfYBMHbsWOzbtw/hcBi5ublobm7Wbq4xmUy6XEZdyWKx
      YMmSJXjuuecAAG+88cY1H94wENnZ2dpzDaqqquDxeOD1eiGEwKVLl3S5icZms8HlcmnT8Zqb
      m3t8W9Lr243D4UBFRUWvdeG7B7v0aJP58+cjNTUVd999Nx588MEe/yZMmBD1/oGu9WuufNbE
      0qVLcfnyZRw7dkyX/QPoFYiFhYVaV0koFNKtH3r06NFYs2YNqqqq8OGHH+p2oryS3+9HZWUl
      WlpaoCiKNn7RfbWkx3jJ/Pnz8dFHH8Hv96OwsBANDQ3awLDT6dRtYL6byWTCnDlz8NxzzyEn
      JwdvvfWWtt5RNJKTk9HU1KStLdbR0aE9Z6ChoUG31WaH/SCwoig4evQoTp8+DZ/PB1VVYbfb
      kZubi2XLlulystm3bx/Gjx/f58Jf3TND9LjrdMeOHairq8OIESOwcOFClJSUaIOCTz31lG53
      H545cwapqaloaGjA6tWrkZWVhYaGBtTX12POnDlRlQF0fTMrKSnRAkaSJPj9fiQnJ6OoqEhb
      hCxa5eXlSE1NRX5+fo/te/fuxcKFC6NeulcIgZ07d2L+/PnIzMwE0NUduGfPHlRUVGDFihVR
      d5kJIbBt2zbcdtttcDgcCAaDWlfDV199BVVVsWDBgqjK+GZ59fX12Lt3L9rb2/H9739ft5Cp
      rKxEdXU12tvb4XK5sGbNGpjNZnz11VewWCy63KUr/vwQm8OHDyMYDMLtdiM1NRVJSUlYvny5
      LifOiooKdHZ2Yv78+b3+T6/BfyEEDh06hJMnTyIpKQl33303tm/fDpPJBLfbrXX/RmvYB0A3
      VVXh8/m0b2vd89z14Pf7ey3OFAgEYLFYdJ+dEwgEtNlLbrcbHo8HmZmZug08iz+vceL1epGS
      kqLdLKfnOird5YRCIe3brd1u7zWTJlYURRnQU5mulxBCOxHo0S7d79lv1tfj8cBms8Vk0kEo
      FMLly5cxbty4mM3QAf7yntZ7JpD48+Myw+GwNltKr/YOhUJQVVX3z8Q3dc8wM5vNkGVZW900
      JSVFt9lfw/5GsCs/PBaLBVarFdu3b8fEiRMH/Ni0a5Fludebd8+ePT0WoNLjCgBAjw97VVWV
      th6JnmVYrVYkJSXBbDbD4/Fg//792tOt9Dhely5dQnJyMiwWC2w2G86ePYsDBw7A6XQiPz9f
      lxNaQ0MDJEmC1WrVHtZTUlKCixcvIicnR5crP6/XC6fTqd1B29HRgZKSEpw6dQpJSUnaVUE0
      FEVBVVVVj26fzz77DEeOHIGqqro9HOTSpUvaHblCCJw8eRKnT5/WtU0qKyu1GzIVRdGe1NXZ
      2YmCggJdAqC6uhp2u137QlZVVYXPPvsMVVVVyMvL0+XE6Xa74fP5tJlRzc3N2LVrF8rLy5GS
      kqLLfQDBYBB1dXXIzMyEyWRCIBDAp59+ioqKCm0xQD3afdjPAvrggw/Q0tLS42BdvnwZTqcT
      2dnZePDBB6Mu49ChQzh27FiPy766ujpcvHgRdrsdTzzxRNQzD+rr67VHNXZzuVwIh8M4fPgw
      li9fjlGjRkVVht/vx8aNG3v0zYdCITQ1NaG6uhozZ87EwoULoyoD6Dpe2dnZMJvNqKysxLFj
      x3Dffffh3Llz2Lp1Kx5++OGo39zl5eUYN24ckpKS4PF48NFHH+Hhhx+G1+vF22+/jeeeey7q
      k0FbWxvOnDmD/Px8CCGwefNmLFy4EOnp6fjoo4/w6KOPamvRX69QKISDBw9qC+Tt3bsXsizj
      tttuw/79+6Gqap9dEZG6sk2+/vprlJWV4f7778e5c+dQXFyMRx55JOo2+fLLL1FYWAiTyYST
      J0/i0qVLuOOOO3D8+HGUlJRgxYoVUZdRVlaGpUuXwmazoaWlBbt378aDDz6IlpYWbNq0CWvX
      ro26jLq6Ou05A0IIvPPOO1i+fDksFgs++ugjPPnkk1F3Y/r9fhw5cgSjRo2CEALFxcUoKCjA
      7NmzsWvXLsiyHPWsP8AAAdC9nG1RUZF2En7rrbewevVq3S6d09PT4XA4cPfdd2vf+oqLi7F0
      6VLdBmuSkpKQmJiIW2+9VZuPf/bsWbjdbl1OAEDX1UVaWhpyc3Nxyy23QJIkuFwufPrpp1i5
      cqUuZXzT6dOntQdojBgxAq+//jpCoZCuNzhVV1djxowZ2oyQ8vJytLa26jJw3q2zsxN2ux03
      3XQTAGDZsmU4ffq0LlP1rlRbW4snn3wSNpsNK1aswEcffaRb+3cbjDY5f/487rzzTmRmZuLO
      O+/EunXrtCmierl48SIWLVqEvLw85Obm4tixY/B6vbo+trGlpQXZ2dnaxIJ58+ahsrJSl7Gy
      boqioK2tDatWrQIALF++HAcOHNAlAIb9LKBvfetbmDNnDt59912Ul5fHpIyJEyfigQcewJ49
      e7B//37dpn5eKSUlBU8//TQqKyvx0Ucf6Tb970oWiwUPP/wwHA4H3nzzTTQ2NupeBtDV1dR9
      leH3+3t8WzKZTLr1BXfPZvL5fL2CWK8TjaqqWl/tlYupCSF0HZfpPl6yLPfYbyzK+Obx0mu8
      5MoywuFwjxOxnif+/tpdrzuzryzjm+9fQP826V7OIhZlDPsrgO55xo8//jgOHDiAs2fP6jIt
      85tlpKSkYNWqVTh9+jTeeeedmJRhNptRVFSEuro6fPjhh7Db7VF3+3yTyWTCzJkzMX78eOzc
      uRM2m023+dnd8vPz8c4778BisaCurk474dfW1ur2kJOCggJ89tln2L9/P9xut/ZNPBgMoqWl
      RZcrM4fDgaamJrz++usAoHX3qKqKsrIy3H333VGXIcsyEhMTsX79ethsth7vq1OnTmljM9Eq
      KCjos03q6ur6XY00Ujk5OdiwYQOsViucTqdWxoULF5Cfn69L8I8ZMwbFxcWQZRmtra3a8el+
      zKUeA7cZGRk4fPgwzp07B0VRtGclKIqCc+fO6dKtbLVaoaoq1q9fD6vV2uNL5fHjxzFp0qSo
      ywAMNAsI+MsCYUePHsXdd98ds7V73G43SkpKUFRUpOvl5pVCoRC+/PJLFBQU6HYS+CZVVVFe
      Xo6Ojg7ccsstuu67exZQIBDQnp7V1NSExMREXaa3dZehKAr8fr82MOjxePpdKvp6qaqqLdJm
      t9uhKAqqq6t1ebBNt+7ZMoqiaIOP1dXVug3QdpfRV5skJCT0uVT09ZYRDAa1JUyArvGtjIwM
      XRbo6y4jHA5r95TIsoyOjg6EQqGox2SuLKO73bsnlwSDQTQ2NqKwsFCXFczoGgAAAFRJREFU
      MoCu91b38g+JiYna/TIjR47U5fxlqAAgIqK/GPZjAERE1DcGABGRQTEAiIgMigFARGRQDAAi
      IoNiABARGRQDgIjIoBgAREQGxQAgIjKo/w9CYWvxnGG49QAAAABJRU5ErkJggg==
    </thumbnail>
    <thumbnail height='384' name='Annual Fuel Use' width='384'>
      iVBORw0KGgoAAAANSUhEUgAAAYAAAAGACAYAAACkx7W/AAAACXBIWXMAAA7DAAAOwwHHb6hk
      AAAgAElEQVR4nO3deXRU12EG8O/NPpJG0qB9RUJIAgshEGBjwJjVYIyNjeN4TZy0cZMmTVO3
      J+manjQ9XXLaLG1zsjjBC04c8DGrCWbfN4MRYheSQUiI0b5Lo1nf7R/KTJAFWIN1ZyS973eO
      /5gZPPdq5s773rv3vnsVIYQAERFpji7SFSAioshgABARaRQDgIhIoxgAREQaNaoDQFXVSFeB
      iGjUYgAQEWnUqA4AIiK6dyMiAPx+P253O4Lf749AbYiItMEQycJdLhe2bdsGh8MBIQQeeugh
      lJaWwu12Y8uWLXA4HLBarfjc5z6HhISESFaViGjMUSJ1J7AQAps3b4bNZsPDDz8Mn88Hj8eD
      uLg47Nu3D52dnVi5ciUqKipw7NgxvPLKK1AUZcCVgt/vh16vj0T1iYhGvYhdATidTtTW1uIb
      3/gGdDodDAYDrFYrhBC4dOkSXnjhBRiNRkyZMgW7d++G0+mE1Wod0C3k9/vZTUREdI8iGgB9
      fX1Yv349mpqaYLfbsXLlSsTFxcHr9SIuLg4AoCgK7HY7enp6EBMTM+CM3+fzwWCIaC8WEdGo
      FbFBYK/XC71ej0WLFuEv//IvMW3aNLzzzjvw+XwA+g/8AZ/s+iEios8uYgEQFRUFq9WK1NRU
      6PV6lJSUQAgBVVVhMBjQ1dUV/LcdHR2IiYmJVFWJiMakiAWAzWaDyWTCxYsXAQCNjY3Q6XQw
      m80oKCjAiRMnIITA1atXYTQaERUVFamqEhGNSRGbBQQALS0tePfdd6EoCrxeL1auXIkJEybA
      6XRi/fr16Onpgc/nw3PPPYe0tLRB/z/HAIiI7l1EAwDoX86ht7cXVqt1wMFcCBF8/k5TPRkA
      RET3LuIB8FkwAIiI7t2IWAqCiIjCjwFARKRRDAAiIo1iABARaRQDgIhIoxgAREQaxQAgItIo
      BgARkUYxAIiINIoBQESkUQwAIiKNYgAQEWkUA4CISKMYAEREGsUAICLSKAYAEZFGMQCIiDSK
      AUBEpFEMACIijWIAEBFpFAOAiEijGABERBrFACAi0igGABGRRjEAiIg0igFARKRRDAAiIo1i
      ABARaRQDgIhIoxgAREQaxQAgItIoBgARkUYxAIiINIoBQESkUQwAIiKNYgAQEWkUA4CISKMY
      AEREGsUAICLSKAYAEZFGMQCIiDTKEKmCu7u7ceDAgeDjqKgoLFiwAHq9HrW1tTh06BAMBgOW
      LFmCxMTESFWTiGjMitgVQG1tLTo6OjBp0iRMmjQJeXl5UBQF7e3tWLduHebMmYOpU6dizZo1
      8Hg8kaomEdGYFbErgIaGBhQWFiI/P3/A8+fOncPMmTMxYcIECCFw/vx5VFVVoaioKEI1JSIa
      myIWAE1NTejs7ITX60VqairGjx8Pg8GAxsZGzJw5EwCgKApyc3PR2NiIoqIi+P1+qKoafA+/
      3w+/3x+pP4GIaFSLWAA8+OCDaGpqgsfjwf79+2EymfDiiy/C4/HAZDIF/53JZILb7QbQHwg6
      3R97rYQQAx4TEdHQRSwAcnJykJOTAwCYO3cufvSjH6G7uxs2mw1dXV3Bf9fZ2YnY2FgAGHSw
      F0LAYIjYn0BENKpF5PRZVVXU1dVBCAGgvyvH6/XCYDBg4sSJKCsrgxACqqqivLwceXl5kagm
      EdGYFrHT5yNHjqCpqQkpKSm4efMmpk+fjujoaBQUFODkyZN444034PF4kJGRgeTk5EhVk4ho
      zFJE4DQ8zFRVRXd3N9rb2xEXF4f4+HgoigIA8Hq9aGhogF6vR0pKCvR6/W3fw+fzsQuIiOge
      RSwAhgMDgIjo3nEKDRGRRjEAiIg0igFARKRRDAAiIo1iABARaRSn0BARjSEfVjZiz7mbQ/q3
      DAAiojHE7fWj0zm0JfTZBUREpFEMACIijWIAEBFpFAOAiEijGABERBrFACAi0igGABGRRjEA
      iIg0ijeCDSNHWy8aO/qkllGSkwCdTpFaBhFpAwNgGJ2racW+8w6pZUwZPw46MACI6LNjFxAR
      kUYxAIiINIoBQESkUQwAIiKNYgAQEWkUA4CISKMYAEREGsUAICLSKAYAEZFGMQCIiDSKAUBE
      pFEMACIijWIAEBFpFAOAiEijGABERBrFACAi0igGABGRRjEAiIg0igFARKRRDAAiIo1iABAR
      aRQDgIhIoxgAREQaxQAgItIoBgARkUYxAIiINMoQ6Qr09PRg586dmDNnDtLS0gAAtbW1OHz4
      MPR6PZYsWYLExMQI15KIaOyJ6BWAqqrYsmULqqur0dLSAgBob2/H+vXrMXv2bEydOhVr1qyB
      x+OJZDWJiMakiAbA5cuXIYTA5MmTg8+dO3cOpaWlyMvLw+TJk5GTk4OqqqoI1pKIaGyKWBdQ
      b28v9u7di5dffhlHjx4NPt/Y2IiZM2cCABRFQW5uLhobG1FUVAS/3w9VVYP/1u/3w+/3h73u
      dxKOung8Hqg6RXo5RDQ6+UI4DkUkAIQQ2LlzJ2bPno24uLgBr3k8HphMpuBjk8kEt9sNoD8Q
      dDrdgPe59XGkKYr8uuj1eugZAER0B6EcEyMSAA6HA8ePH0dpaSmqq6tx/fp1VFdXQwgBm82G
      rq6u4L/t6upCbGwsgMF/mBACBkPEx7GDdGE4MOv1ehj0Iyf0iGhk0SlDPw5F5OiZnJyMb3/7
      28HHBw4cQFpaGvLy8qDX61FWVobJkydDCIEzZ87g2WefjUQ1iYjGtJACoKmpCeXl5XjkkUdw
      6NAhvPfee1iwYAGeeuopKCGkjtFoRHJycvBxTEwM4uPjER0djYKCApw8eRJvvPEGPB4PMjIy
      BvxbIiIaHiEFwL59++B2u+H1evE///M/+MY3voG3334b999/PzIzM++5EosWLYJerwfQHw4v
      vfQSGhoaoNfrkZKSMqL6+YmIxoqQjqwdHR1ISUnB5cuXkZ6ejocffhjZ2dno6Oj4TJWwWCww
      Go3Bx0ajEVlZWUhPTw8GAxERDa+QrgCmTJmC1157DQaDAStWrIDf70d9fT0SEhJk1Y+IiCQJ
      KQDmzJkDnU6Hrq4uLF68GF1dXVi1ahVSU1Nl1Y+IiCQZUgA4nc7gDVhTp04FALjdbpjNZjz8
      8MMQQoQ0CExERJE3pAD4h3/4B1RUVMDn88HtdiM6OhpA/52viqLgrbfeCi7kRkREo8OQAuAH
      P/hBcOE2j8eDZ555BkD/VcC//Mu/ICYmRmoliYho+A1pFpDZbIbVakVHRwf8fj+sViusVivi
      4uLg8Xhw/fp1ydUkIqLhFtIg8OzZs/FP//RPiIuLQ3Z2NsrLy1FbW4ukpCRZ9SMiIklCCoCS
      khJ897vfxcaNG7Ft2zakpqbi+9//PlJSUmTVj4iIJAkpAC5fvgygf0yAiIhGt5DuBG5oaMBP
      fvIT+Hw+WfUhIqIwCSkAZs+ejblz5+KHP/whWltb0dbWhvb29hG1KQsREQ1NSF1ABw4cwP79
      ++F0OvHhhx9CURRYLBb88Ic/5N3ARESjTEgBsHjxYsyZM2fAc4qi8D4AIqJRKKQAMJlMqKmp
      wfbt29He3o78/HysXLmSK3YSEY1CIY0BXLx4EX/zN38Di8WCadOmoaKiAt/+9rfhdDpl1Y+I
      iCQJKQB27NiBL3zhC/ja176Gp556Ct///vdhMBhQWVkpq35ERCRJSAFgNpsHbP7i9XrhdDoH
      bOZCRESjQ0hjAE899RT+6q/+CpcvX0ZKSgouXLiACRMmoKCgQFb9iIhIkpACICMjA7/+9a9x
      /PhxtLW1YdGiRZg2bRqvAIiIRqGQAqCyshLXrl3Do48+CgDo6+vDb3/7Wzz33HOwWq1SKkhE
      RHKENAawZ88euN3u4GOLxYKPPvoIN2/eHPaKERGRXCEPAt+8eRNCCAD9W0W2t7fzPgAiolEo
      pC6gxx9/HH/+53+O2tpapKWl4dSpU7jvvvuQnZ0tq35ERCRJSAGQnJyMN998E3v37kVbWxu+
      +c1vYtasWbwCICIahULqAgKA3t5e6HQ6pKenY/LkyTh58mSwS4iIiEaPkALg8uXL+OY3v4lz
      585h06ZNMJlMeOutt9Da2iqrfkREJElIAbB9+3Z8+ctfxne/+10YDAaYTCbYbDY0NzfLqh8R
      EUkSUgBERUWhra0tuAFMR0cHbty4gaioKCmVIyIieUIaBH766afx6quv4vDhw7h27Rq+8pWv
      4MEHH0RmZqas+hERkSQhBUBKSgreeustnD17Fl1dXRg/fjxyc3Nl1Y2IiCQacgD09fXh0qVL
      MBgMmDZtGgwGA7xeL371q19h5cqVyMjIkFlPIiIaZkMKAKfTib/+679Gc3MzhBC477778I//
      +I/4yU9+ghs3buCll16SXU8iIhpmQwqAs2fPoq+vD2vXroWqqvjKV76CV199FVarFf/+7/+O
      6Oho2fUkIqJhNqQAaG5uRmFhIaKjoyGEwJQpU9DR0YHvf//7PPgTEY1SQx4DaG1txalTpwAA
      7e3tyMnJwaVLl6DT6VBUVASLxSKtkkRENPyGFABxcXFoamrC//7v/wafa25uxqlTp2CxWPBv
      //ZvDAAiolFmSAEwf/58zJ8/X3ZdiIgojIYUAIqiyK4HERGFWcirgRIR0dgwpAD413/9V7S2
      tmLXrl344IMPZNeJiIjCYEhdQHV1dbhy5QocDgd8Ph86OjqCrymKgpiYGG4KQ0Q0ygwpAF5+
      +WX84Ac/QGtrK1RVxW9+85vgaxaLBa+//jrS09OlVZKIiIbfkAJgzpw52LJlC7Zs2QKfz4en
      n35adr2IiEiykDeF9/v9qKioQHd3NzIyMpCWlnZPs4RUVYXD4UBHRwdiY2ORmZkJna5/SMLj
      8aC6uhp6vR65ubnsXiIikiCkAOjq6sLf//3fo729HTExMWhpacGjjz6Kr3zlKyEfpDds2IDe
      3l4kJyejoaEBBoMBL774Inw+H958803Y7XZ4PB6cOHECL7zwQjAciIhoeIQUABs3bkRmZiZ+
      /OMfw2w2o6GhAa+++ioWL16MiRMnhlTwypUrYbFYoCgKVFXFf/3Xf8HtdqOqqgrx8fF45pln
      IITAL37xC9TX13O5aSKiYRZSADQ1NaG4uDi47ENqaipSUlIGzAoaKovFAiEE3G43Ll++jPj4
      eJhMJly7dg1Tp06FoihQFAXFxcWorq5GRkYGhBAQQgTfQwgBVVVDLluWW6omrwxVhcr78ojo
      DkI5DIUUAHPmzMFPf/pTREdHIyUlBR999BEaGhqQl5cXYhUBv9+Pd955B9evX4fRaMRXv/pV
      GAwG9PT0wGazBf+dzWZDfX09gP5xg8B+xIH3uPVxpIWjLh6PB6qe3WFEI9WO8jrcaHVKe397
      tAmfm51zx9d9Pt+Q3yukAJg3bx68Xi82btyItrY2FBQU4D/+4z9gt9tDeRsAgF6vx/PPP4+e
      nh5cvHgRa9euxde//nVYLBa43e7gv3O73cErDr1eP2CswefzwWAI6U+QymCQP1httlhgZAAQ
      jVgtPV6pAeD2ibsuvmkM4ZgY0tFTp9Nh8eLFWLx4cSj/220pigKj0Qi73Y558+bh448/Rn19
      PdLS0nD16lXk5eVBCIGrV6+itLT0M5dHREQDReT0ube3Fxs3bsSUKVMwbtw4tLS0oL6+HklJ
      SbDb7fjlL3+J1NRUuN1uOBwOfP7zn49ENYmIxrSQAqCtrQ1erxcpKSmfqdCoqCgsWLAA58+f
      R1VVFWJjY/HVr34VVqsViqLgpZdewvHjx2EwGPCnf/qnI6qbh4horAjpyHrw4EHs3bsX//d/
      //eZlohWFAVZWVnIysq67etpaWlYvXr1Pb8/ERF9upBGE5cuXQqr1Yrt27dDVdXgfyIc8x+J
      iGhYhXQFcPLkSbS1teE///M/sXnzZiiKArPZjH/+539GUlKSrDoSEZEEIQXAlClT8PWvf33A
      czqdDrGxscNaKSIiki+kLqDk5GRMmjQJXq8XPp8PhYWFsFqtMJvNsupHRESShHQFUFNTg7/9
      279FUlIS3G43fvSjH+HHP/4xfvCDHyA+Pl5WHYfM0dYLj0/e0hBmox5p9ihp7/9Z9Xl8aOzo
      k1pGcpwVUWbOyiIaC0L6JW/atAmrV6/G5z73OfzFX/wFrFYr4uLi0NDQMCIC4L3j11DX2ivt
      /XOSbfj68iJp7/9ZOdp68ctdl6WW8aWFhbgvK/Q7v4lo5AmpC0hRFOj1+uAUUKfTCYfDAZPJ
      JKVyREQkT0hXAKtWrcJ3vvMdlJWV4fr16/jWt76F7OzsO87nJyKikSukAMjJycEvf/lLHDt2
      DEVFRcjJycHMmTNhNBpl1Y+IiCQJqQtICIHOzk50dnbC5XJBCMGduoiIRqmQjt7l5eV45ZVX
      gnsC//znP8f3vve9kNafJiKikSHktYCee+45/Mmf/AkURUFXVxdeeeUV3LhxA7m5ubLqSERE
      EoR0BVBQUACDwRCcBRQbG4u0tLTPtDAcERFFxpCuAH70ox+hpqYGXq8X165dQ1lZWfC1+vp6
      JCYmSqsgERHJMaQAWLRoEbq7u2/7mk6nG7BNIxERjQ5DCoBp06bJrgcREYVZSIPAp0+fxne+
      8x24XK7gc1arFW+99RYyMjKGvXJERCRPSAFw5MgRPPfcc3jxxRcHPH+3HeqJiGhkCmkWUHFx
      MbxeL6Kiogb8x5vBiIhGn5CuACZMmIAf//jHqKioCD5nMpnwd3/3d5wJREQ0yoQUANu3b8eU
      KVPw2GOPBZ/T6/WIiYkZ9ooR0ejU6/JC5jbhFpMeBj17HYZDSAGQkJCAuLg4zJs3T1Z9iGiU
      +9HWc+h2eaW9/5cXFWJyJvekGA4hBcDEiRPx3//93wPu/DUYDHj00Udhs9mGvXJERCRPSAGg
      0+kwY8YMOByO4HMmkwl+v3/YK0ZERHKFFADTp0/H9OnTZdWFiIjCKKQAOHr0KDZv3jzgObPZ
      jFdffRUJCQnDWjEiIpIrpADIzs7G8uXLg487OjqwY8cO3ghGRDQKhRQAWVlZA/b/VVUVe/bs
      QVtbG6Kjo4e9ckREJE9IAdDe3o6mpqbgY4/Hg6amJnR2dnJjeCKiUSakADh16hTeeuut4GNF
      UfDAAw8gPz9/2CtGRERyhRQAS5YswaJFi4KPFUWBTqfjjmBERKPQkALA4XDg0qVLt31Nr9fj
      gQceQFRU1LBWjIiI5BpyAOzZs2fQ8/X19aiursY777zDACAiGmWGFAAzZ87EzJkzAQBCCDQ3
      N2P9+vWoqanBF7/4Ra4ESkQ0Cg15DEAIgZaWFqxfvx779+/H8uXL8dOf/pQ3gBERjVJDCoD2
      9na8/fbb2LdvH1asWIE1a9YgPj5edt2IiEiiIQXA4cOHsW7dOixevBidnZ341a9+FXzNaDTi
      S1/6EgOBiGiUGVIAFBcX41vf+tbt38BggMEQ0mxSIiIaAYZ05M7NzUVubq7suhARURhxXzUi
      Io1iABARaRQDgIhIoxgAREQaFfEAUFUVQojbPq+qagRqRESkDRGbv+lwOLBr1y709PTA7XZj
      9uzZmDNnDoQQOHToEMrKygAACxYswPTp07niKBHRMItYAJw+fRrz5s1DTk4OXC4Xfvazn6Gk
      pATt7e04f/48vva1r8Hr9eJnP/sZ8vPzYbPZIlVVIqIxKWJdQI899hgmTpwIg8EAs9kMna6/
      KhcuXMDs2bMRFRWF2NhYTJs2DRUVFZGqJhHRmBWxKwCdTgchBLq6urBu3ToUFxcjOjoa7e3t
      KCoqAtC/4UxKSgpaWloAAD6fDz6fL/geqqrC4/EEHwvJYwaqqsLpdN7xda/Xd8fXhktfXx+8
      utt3h7ndnts+P5w8HvddPwOta+x04Uar3M+nZHw8jPqID9/dkcDgMb3h5HaP7Dao+uUeh4T4
      tOOQd8jvFbEAEEKgqqoK7733Hh599FFMmzbtU/v5P7nshM/nG/BY0cn9Ueh0urvue2A0yv84
      rVbrHX/8ZvPQv/h7ZTKZuffDXdy43oltZXVSyyjJTUJUlFlqGZ+FArnjdWbzyG6DOsnhrCif
      dhwyDvm9InYacf36dWzevBlf/epXBwzy2u12NDY2Bv9dY2Mj7HZ7pKpJRDRmRSwAdu/ejcce
      ewyxsbHwer3wer0QQqC4uBjHjx+Hy+VCd3c3zpw5g8mTJ0eqmkREY1ZEuoAC8/53796NvXv3
      Bp9/8cUXkZ6ejpKSEvz85z+HEAKPPvoooqOjI1FNIqIxLSIBoCgK/uzP/uyOr8+fPx+zZ8+G
      TqcLqT+LiIiGbkQu5K8oCszmkTvIRUQ0FozcuWRERCQVA4CISKMYAEREGsUAICLSqBE5CEx0
      r9Yf/RhXG7qkvX+izYI/e+Q+ae9PFE4MABpTevp86OiVtyaSycCLZho72JqJiDSKAUBEpFEM
      ACIijWIAEBFpFAOAiEijGABERBrFACAi0igGABGRRjEAiIg0igFARKRRDAAiIo1iABARaRQD
      gIhIoxgAREQaxeWgicaQtm4X3jtxTWoZD9+XjsKMeKllUHgwAIjGELdPxcf18jbEAYDpuYlS
      35/Ch11AREQaxQAgItIoBgARkUYxAIiINIoBQESkUQwAIiKNYgAQEWkUA4CISKMYAEREGsUA
      ICLSKAYAEZFGMQCIiDSKAUBEpFEMACIijeJy0EQ0pnxv/UdQVSHt/WcXpmBFaba09w8nBgAR
      jSl9Hh+EvOM/vD5V3puHGbuAiIg0igFARKRRDAAiIo1iABARadSIDQBVVaGqY2ewhYhopInY
      LCAhBGpqarB9+3bMnTsXJSUlAPoP/IcOHUJZWRkAYOHChZg2bRoURYlUVYmIxqSIBcDx48dx
      7tw52Gw2eDye4PM3b97E+fPn8bWvfQ1erxc/+9nPMHHiRNhstkhVlYhoTIpYF9DUqVPxyiuv
      IDMzc8DzFy5cwOzZsxEVFYXY2FhMmzYNFRUVEaolEdHYFbErgJiYmNs+397ejqKiIgCAoihI
      Tk5Ga2srAMDn88Hn8wX/raqqA64ehOQxA1VV4XQ67/i61+u742vDpa+vD17d7bvD3G7PbZ8f
      Th6P+66fQaT5Vb/U91dV8SltwCu1fADoc7lgVG7/d7rdLunlez3eu34GAhLvwgLgdke2Dfp8
      vruWr/rlHoeE+LTj0NDb4Ii/E/jWvn+DwQCD4Y9V9vl8Ax4rOrkXNDqdDlFRUXd83WiU/3Fa
      rVYY9bf/O81m+Qcfk8l8188g0vQ6vdT31+mUT2kDRqnlA4DVYkFUlPm2r5nd0ouH0WS862eg
      QO54ndkc2TZoMBjuWr7uDr/P4aIon3YcGnobHHGzgOx2OxobG4OPGxsbYbfbI1gjIqKxacQF
      QHFxMY4fPw6Xy4Xu7m6Ul5dj8uTJka4WEdGYE7EuoI0bN8LhcMDtdkOn0+HDDz/EsmXLkJeX
      h5KSEvz85z+HEALLly9HdHR0pKpJRDRmRSwAVq9efcfX5s+fj9mzZ0On04WlT5WISItG5CCw
      oigwm28/yEVERMNjxI0BEBFReDAAiIg0igFARKRRDAAiIo0akYPANHr9YNMZ+CVuyD0zLwmP
      TMuS9v5EWsIAoGHV3uOGxOM/+jxy1/oh0hJ2ARERaRQDgIhIoxgAREQaxQAgItIoBgARkUYx
      AIiINIoBQESkUQwAIiKNYgAQEWkUA4CISKMYAEREGsUAICLSKAYAEZFGMQCIiDSKAUBEpFEM
      ACIijWIAEBFpFAOAiEijGABERBrFACAi0igGABGRRjEAiIg0igFARKRRDAAiIo1iABARaRQD
      gIhIoxgAREQaxQAgItIoBgARkUYZIl2B23G73bh27Rr0ej3y8vKg1+sjXSUiojFnxAWA2+3G
      66+/jqSkJLjdbpw4cQIvvfQSdDperBARDacRd1S9fPkyEhIS8PTTT+P5559Hb28vHA5HpKtF
      RDTmjLgAqK6uRnFxMRRFgU6nw5QpU1BdXR3pahERjTkjLgB6enpgs9mCj202G3p6eiJYIyKi
      sWnEBYDFYoHb7Q4+drvdsFqtEawREdHYNOICIC0tDVevXgUACCFw9epVpKWlRbhWRERjz4gL
      gKlTp6K8vBzl5eU4efIkHA4H8vLyIl0tIqIxZ8QFQGxsLL74xS/i+vXraG5uxiuvvAKDYcTN
      ViUiGvVG5JE1NTUVTz75ZKSrQUQ0po24KwAiIgqPEXkFcCd+vx+qqgYfq6oKIUTw8eT0WKTE
      maWVn2izwOv13vH1tDgLZkwYJ618AFD9PnhV5bavRRl10su3WXR3/QxmTEiAest3Mtwyx939
      O5iYGoMYi7zzmlir8a7lJ9lM0r8DHcQd62DSQ3r59qi7fwZTx8fD5fVLKz/GrL97G8xNgIDM
      Nmi9a/n5KTbYLPKWr4m1mj6lDZqH3AYUIST+WofZJw/4qqpGbIkIIQRUVY3oOkV+vz+i5auq
      CkVRoCi3DyTZhBAQQkR0mZBIfweRLj/SbSDS5Y/2NjiqrgA++SELISLW+IUQEf/xeb1emEym
      iJUf+Psj1fj9fn/E24DP59N0GwgcgCP1GbANfrY2yDEAIiKNYgAQEWnUqA6AUC97PjmI/MnX
      +vr6Qnq/UC+9A91Gd3qtt7dXavkA4PP57vhaqOUbjcaQ+14Dl8y343K57lq/T9LpdCHfI6Kq
      6h3bgKqqIbcBo9EY0r8PXLLf6bXR1gYMBkPI3S9sgyOnDY7qAAj1i+/o6MDGjRsHfcC9vb1Y
      v349mpubQyo71PJ9Ph82bdqExsbGAQ3Q5/Nh//79KCsrC+n97qXf8+DBg7h48eKA8oUQuHTp
      Et5///2Q3utePoOqqirs27dv0CyG5uZmrFu3Dk6nU2r5PT092LBhA7q7uwc873a7sWHDBtTW
      1oZUfqjfgRACW7duxc2bNwd8B36/H8eOHcPRo0dDer97aQMnTpxAWVnZgINQYNmV9957L6T3
      upfv4E5t8OLFi2Frg/v37x/UBpuamrBu3bqQDsDD3Qbfe+893LhxI6TyP0sb1NcYE4UAAB4k
      SURBVH/ve9/7Xkj/9yhmtVphNpuxdetWJCcnIzY2Fg6HA++99x4efvhh5OTkSC1fr9cjLS0N
      77//Pvx+P1JTU+F0OrFhwwYkJCRg7ty50mczZGRk4KOPPsKVK1cwYcIEAMCePXtQV1eHJ554
      Qvpd1+PGjUNbWxv27duH8ePHw2Kx4PLly9i9ezdWrVoFu90utXyz2QybzYatW7ciNjYWdrsd
      ra2t+N3vfof7778fhYWFUr8DRVGQmZmJHTt2oKenBxkZGfB4PNi8eTOMRiMWL14svQ2kp6fj
      woULOHv2LCZMmACdToejR4/i0qVLePrpp0M+owzVndqgw+EIWxtsaWnB/v37g23w0qVL2LNn
      T9jaYExMzIA22NLSgt/97neYPXs2CgoKwtIGP/jgg9E1DXS49Pb2YsuWLdDpdHA6nfjc5z6H
      2NjYsJWvqir27t2LhoYG9Pb2YtmyZcjNzQ1b+UIIVFRU4NChQzAajZg8eTIeeOCBsM6kaG5u
      xpYtWxAdHQ2DwYBVq1aFdTaLy+XCtm3b4PP50NnZiWeeeQbjxsmdP38rVVVx9OhRXL16FW63
      GwsWLEBhYWHYyhdC4Nq1a9i7dy9MJhOys7OxYMGCsLUBIQQuX76MI0eOwGAwRKQNNjU1YevW
      rcE2+OSTT0oPv1vd2ga7urrwzDPPSA+fW6mqCgiN8vv94he/+IVwuVwRKV9VVbFhwwZx48YN
      oapqRMq/ePGi2LVrV0TKF0IIp9MpXnvttYiVr6qqeO2114TT6YxY+Tt27BCXL1+O2Gdw7do1
      sWXLloi1wQsXLojdu3dH7O/v7e3VdBvU1BVAe3s7tm7dGnzc1NSE5ORkAP2XRQsXLkRWVpa0
      8r1eL7Zt24auri4AQFtbG2w2W/CsY/Lkybj//vullQ8AR44cCS637XK54PF4glc/8fHxWLVq
      ldTyr169iiNHjgDoPwtsaWlBUlISgP4uslWrVg3YEGi49fT0YPPmzcHB+KamJiQlJQUvuefO
      nYuJEydKK19VVfz+979HW1sbAKCzsxNmsxkWiwUAMGHCBDz00EPSygeAU6dO4dKlSwAAj8cD
      p9OJ+Ph4AP37cTz77LNSy2cbHDltUFMBoKrqXUf472VGQyiEuPMt/MC9zSgIlc/nu+MMBODe
      ZpWEwu/333EmFHBvszpC8WnfgV6vl3pTj/jDDIw7/ey00AYiXT7b4B/boKYCAOj/4zs7O9HY
      2Ain0wmLxYKUlBTEx8eHpf9RCIG+vj40NDSgq6sLer0eiYmJSEpKCtuy116vF01NTWhtbYWq
      qrDb7UhJSQmehcqmqira2trQ2NgIj8eD6OhopKWlISYmJiy39Ash0N3dHRyDMZlMSElJwbhx
      48LWB+1yudDQ0ICOjg7odLpgGwhXH7TP50NzczNaWlrg9/sRFxeHlJQUREVFhaV8tsGR0QY1
      FQCqquKDDz5Ae3s7MjIyEB0djb6+PtTX10Ov12P16tXSk/fcuXM4deoUsrKyEB8fH/whtre3
      Y9WqVdIHIhsaGvD73/8eSUlJSExMhE6nQ3t7OxwOB+bMmYPJkydLLd/lcmHz5s3Q6XRITU2F
      xWJBd3c36urqkJWVJX0gUgiBffv24ebNm8jIyIDNZgsejP1+P1avXg2zWd6CgkIIVFZW4vDh
      w8jIyIDdboeqqmhpaUFTUxMef/xxpKSkSCsf6O8K3bRpE8aNG4ekpCTo9Xp0dnbi5s2bKC0t
      xbRp06SWf7c2OHfuXEyaNElq+SOhDe7duxcOhyNibfDKlSv93WDhGmwYCaqqqsT27dsHDfio
      qipOnDghjh8/LrX83t5e8cYbbwifzzfotYaGBrFu3Tqp5QshxNtvvy3a2toGPe9yucSaNWuE
      2+2WWv7+/fvFuXPnBj2vqqrYtGmTqKmpkVr+jRs3xKZNm4Tf7x/02tmzZ8W+ffuklu/z+cRr
      r70mPB7PoNc6OzvFm2++KX1A8ne/+51oamoa9LzX6xW//vWvRV9fn9Ty165dG9E2uG/fPnH+
      /PlBz6uqKjZu3Chqa2ullh9og7f7ns+ePSv2798vtfxb2+CovhEsVN3d3UhJSRl0iacoClJT
      UwfdmDHcfD4foqOjb3uVERcXd9d+weGsw+2mvJpMJlitd1/mdjj09vYGB9xupSgKEhISQroR
      7F7LD5x1flJycnLId6KGSggBk8l02+6+mJiYQSveyuB2u2873VCv18Nms8Htdkst3+/337EN
      Wix3X+57OIyUNni7rqakpCT09PRILV8IAbPZDIPBoK0bwex2O7Zv3w6DwRDsa+3p6UF1dTX2
      7t2LZcuWSe2DNBqNqKyshMPhQHR0NBRFgcvlQn19PXbu3ImSkhLpl/9erxdHjx5FXFwcFEWB
      1+tFa2srjh49CpPJhPvuu09qH6jNZsPvf//74NxrVVXR2dmJixcvoqKiAg899JDUsZC4uLjg
      XaBmsxlCCDidTtTW1mLnzp1YuHAhYmJipJWvKArq6upQXV09oA00NTVh586dKCwsREZGhtTv
      wGg0Yvfu3cFxL5/Ph/b2dpw8eRIulwvTp0+XWv6tbVCn0wXb4JEjR2A2m8PSBrdt2zaoDV64
      cAGVlZVhaYP79u2D3+8f1AZ37doVtjZ4/fp1bY0BCCHgcrlw+vRpOBwOuN3u4OBLaWlpWG4G
      8/l8uHz5MqqqquB0OqHT6WC321FSUoK0tDTpA1BCCNy4cQPnz59HR0cHhBCw2WwoKChAQUGB
      9GVthRDo6OhAWVkZmpub4fV6YbFYkJ2djZKSkrAMArpcLpSXl+PGjRtwuVwwGo1ISkrCjBkz
      gsEok9/vR2VlJa5cuYKenh4oioL4+HgUFxcjKysrLG2gvr4e5eXl6OjogKqqiI6OxsSJEzF5
      8mTpkxHu1AYLCwuRn5/PNhimNnjlyhVtBUBAIAi8Xi8MBgMsFkvY1xN3u93weDxQFAUWiyXs
      G9/7fD64XK7g5aDsqW+fFFj0yu/3w2g0wmKxhLX8W9uAXq+H1WoNexvweDxwu90jog2YTCaY
      TKawfgdsg5Fvg6NqQ5jPSgiBjz/+GEePHoWiKDCZ+rdW8/l8mDlzJoqLi6U3gLa2NuzatSs4
      BdXv98Pj8SAnJwfz58+XPg3Q7XbjwIEDqKurg9lshqIocLvdiIuLw5IlSxAXFye1fFVVUVZW
      hrNnz8JoNMJoNMLtdkOn0+Hhhx9Gdna29O+gpqYGBw8ehKqqMJvN8Hq98Hq9KCkpQWlpqfQf
      YVdXF3bt2oWurq5gF4Db7UZ6ejoWL14sfR681+vF4cOHce3aNZjNZuj1erjdbkRFRWHJkiVI
      SEiQWr7b7cb+/ftx8+ZNzbbB69ev49ChQ4Pa4LRp0zB9+vSwtMGdO3dqaxZQU1OTeP3110VP
      T8+A5/v6+sT69etFVVWV1PLdbrf49a9/LRwOx4AZAD6fT5w4cUJ88MEHUssXQogtW7aIsrKy
      AbNgVFUV169fF2+88cZtZ8cMp7Nnz4qtW7cOmunR2dkp1qxZI1pbW6WW397eLtasWSM6OjoG
      PO/xeMTmzZvF2bNnpZbv9/vF66+/Lmpqaga0Ab/fL86ePSs2bNggfRbQzp07xbFjxwa1AYfD
      IX75y1/edpbacIp0GywvLxfvv//+oDbY0dEh1qxZc9sZSsPp09rg7WbJDadAG6ytrdXWLKDr
      169j1qxZiI6OHvC8xWLBQw89hKqqKqnl9/X1wW63D+rr1+v1mD59OlpaWqSWD/RfgUybNm3A
      GYaiKBg/fjyio6Olz0C4fv065syZM+gsNzY2FkVFRaivr5davsPhQFFR0aCzTKPRiLlz56K6
      ulpq+aqqwmAwDOrr1+l0KC4uRnd3913vkh0ODocDs2fPHtQG0tLSkJycjM7OTqnlf1oblD0T
      605tMC4uDkVFRXA4HFLLv3nzJqZMmRKxNhjo8srMzBzd+wGEKicnBydPnhw0zcvtduPgwYMo
      KCiQWr7VakV7ezsaGhoGbW5/5swZJCYmSi0f6F8Kt7y8fNBa7LW1tejp6ZE6+wDo/w6OHTs2
      aKpfV1cXLly4gPT0dKnlZ2Rk4MKFC8H1mAJ8Ph+OHDkSXJ5YlsCsm7q6ukHfwfnz52Gz2aRf
      /qenp+PEiRODym9oaEBTU5P0Lpg7tcGamhr09vYOOkEbbrm5uTh69GjE2mBmZibOnz9/xzYo
      e2VgvV4Pj8eDuro6bQ0Ciz8sgXvo0CEoigKj0Qifzwe/349Zs2ZhypQp0vv+2tvbsXPnTjid
      TphMJqiqCq/Xi9zcXMyfP1/6QOCtYwCBQTev14u4uDgsXbpU+kyoQNiVl5fDYDDAYDDA6/VC
      p9NhwYIFYZkFU1tbiwMHDkBV1WAb8Pl8mD59+qAzUxm6u7uxc+dOdHV1wWg0BteGycjIwOLF
      i6WPA/l8Phw6dAjXrl2DyWQKTsWMiorC0qVLpd+Nfqc2GB8fjyVLlrANhqkN7tixQ1sBEKCq
      KtxuN/x+P/R6Pcxmc1hH34UQ8Hg88Pl8wcHocM4AEX/YmjJww09gICycMyAC5Qe6RAKDgeEi
      /jDw6vP5oNPpwj4TLHDQD5yFBm7MCWf5t7YBg8EQ1llAbIMjow1qLgCcTidOnz6N+vr64DTQ
      5ORklJaWSr/09fv9qK6uDi716vF4cPDgQbS0tCAnJwf3339/WOZA19TUBLtBhBCIjo5Gfn4+
      Jk2aJL38xsZGmEwm2O12CCFQV1eH48ePQ6/XY+7cuUhNTZVaPtA/B/vMmTOoq6uDx+OBXq9H
      QkICZsyYAbvdLn0lyKqqKuTn50NRlOBlf319PdLT0zF37tywzMN3OBwoLy9HV1cXVFVFVFQU
      JkyYgKKiIunlX716FdnZ2cGrn7KyMlRVVSE+Ph4PPfSQ9C4gAGhtbcWZM2eCi+GZTCZkZWWh
      pKQEVqtVatkulwuNjY0YP348gP6upwMHDsDpdGLq1KmYPHlyWO4DqKio0NYYQF9fH37zm98g
      NjYWixcvxqpVq7B06VKkpqZi3bp1aG9vl1q+z+fDqVOngo8PHDgAg8GAZcuWobW1FR9++KHU
      8oH+teBPnjyJadOmYeXKlXjiiScwe/Zs1NXV4YMPPpC+DEF1dTUaGxsB9Afgpk2b8NBDD2HW
      rFnYvHmz9NvwPR4P3nnnHRiNRixcuBCrVq3CsmXLkJOTgw0bNqChoUFq+UIInDx5Mvg5B8ak
      li1bBp/PhwMHDkj/Di5evIj9+/ejuLgYK1aswBNPPIE5c+agra0NmzZtkl7+mTNngmf+gZOR
      pUuXIjk5GVu3bpVevsPhwJYtW5CdnY1ly5Zh1apVWLhwIQwGA9avXw+XyyW1/J6eHpw/fx7A
      H/fnzc3NxZIlS/Dhhx+ipqZGavlCCGzfvh03b97UVgBUVFRgxowZKCkpQUJCAmJiYjBu3DhM
      njwZy5Ytw0cffRTW+tTX1+PBBx/EuHHjsGjRIly7dk16mZcuXcLq1auRmZmJ2NhY2Gw2pKam
      4pFHHkF3d7f0A/Ct6uvrUVBQgLS0NGRnZ2PixInSD8DV1dXIz8/HzJkzkZiYiJiYGNjtduTn
      52PlypU4ffq01PI/qaamBvPmzcO4ceOwcOFC1NTUSD8Anj59Gs8++yyys7MRFxcHm82GlJQU
      LFiwAEKIQYOTMl26dAkLFy5EQkICpk+fDq/XC4/HI7XMsrIyrFixAgUFBbDb7YiJiUFiYiJm
      zpyJCRMmSD8A36qvrw+qqqK4uBiJiYlYvHgxKioqpJbp9/vR1taGpUuXaisAbDYbmpqaBv3A
      hBBoamqSugtQwK2Lfd26+YcQIix9wHq9/rZTPb1eL/r6+sKyHn1gmqPL5QruRAWE5zOIjo5G
      a2vrbQ+yLS0tYel+uLUNCCEGLD2g1+ulX/6bzWZ0dHQMet7v96O7u1vqUsRA/98c+Ps/2QYA
      SO8Hj46Ovu2UayEEWltbw7InQqAN+P3+AW0uMCgsk6IowTFITd0JnJeXh8rKSrz99tuD9gMw
      Go1YvXq11PL1ej3i4+Px5ptvwmAwwOl0Bn/s586dQ35+vtTyAeCRRx7Be++9h8TERCQmJkKv
      16OtrQ0OhwPz58+Xfhdqeno6Dh48GFx4bNasWQD6u2YCdZApIyMDlZWVeOutt5CZmYmYmBi4
      3W7U19dDVVU8/fTTUssPrDy7du1aGAwGdHR0BNvAhQsXpE9DBYDly5djw4YNsNvtwY2IOjo6
      cPPmTcyaNUv6Wjjjx4/Hhg0boNPpUF9fj0ceeQRA/xVhTEyM9JOABx98EJs3b8aFCxeQlpYG
      s9mMnp4e3LhxAzk5OcjMzJRaflRUFPr6+vDmm29CUZQBU69Pnz6NOXPmSC0/cMfz66+/rr1B
      YCEEenp6UF9fj76+PpjNZqSmpiI2NjZsI/DiD1uyqaoaPODW19cjOTk5LFcBXq8XLS0taGlp
      gRACdrsdycnJYZsFIoQITn8NTMNzuVzo6uoK7tEsu/ze3t5BuzGFa1e4QB0CU5ADZ9wNDQ1I
      TEwMy1WYz+dDa2srmpubgzuCpaamhm0mTODsN7AQm6IoaGtrg8lkkn4vCtB/pt3R0TFoR7Co
      qKiwzQS6dYtak8kEIURwo6Jw1MHtdmsvAO6ksbERqqoiLS0tIuV7PB5UVVWhqKhIajmqqqK2
      thYejwe5ubkDDjZXr15FXl6e1PIBoLm5GU1NTRg/fvyAH3tdXR0SExOln4G6XC7cvHkTycnJ
      iImJQVNTEy5fvoyJEyeG5ccX+A6io6ORmJgIp9OJ8vJyxMXF4b777gv7gmAAcOzYMcyYMUN6
      98+dHD58GLNnzw5L+LlcLuh0uuDJl8vlQlVVFYxGIyZMmBCWPYkDay8FHldXV6Onpwd5eXnS
      u6LFH7altVqt2toP4G5qamrQ3d0dlmmIt+NyufDRRx9J3w7v5MmTuHLlCnw+Hz788ENMnjw5
      eMDZunWr9O0AHQ4Htm/fDpvNhoMHD2L8+PHBaXeHDx9GUlKS1H54v9+P3/zmN/B6vTh16hQS
      ExOxe/duFBYW4sCBA8jKypJavhACu3fvRm1tLSorK2EwGHD06FGkpqaipqYGXV1dSE9PlxpC
      Fy5cwJkzZ3D16tXgf2VlZcFtIWXfiVpWVoZz584NKP/06dPo7u5GU1MTsrOzpZfvcrmQkJAA
      n8+HtWvXwmw2o6WlBR999JH0EG5vb8exY8eQn58PIQT27NmD6upqmM1m7Nq1C4WFhVJPgoQQ
      2LBhQ/+UX2mljEAtLS04cuTIbee6NzU1YebMmVLL93g82LNnD/x+/6DXvF6v9NkfAHDlyhU8
      99xzMBqNKCsrw969e/HII4+E7bI3MOUvKysL+fn52LRpE770pS9Jv/8gIDDffvny5WhsbMRv
      f/tbrF69Gjk5ObBYLCgvL8fSpUulla+qKurr6/Hyyy/D5/PhzTffRH5+PqZPn46pU6di7dq1
      mDlzptTvo7a2Fn19fZgxY0bwQBeYkRWOAdDAwW7KlCnB8uvq6lBYWBiW8v1+f/C31tLSArvd
      jkWLFgEANm/ejIaGBqnjAIHuL6D/mHDjxg18+ctfDu4NUlZWhoULF0or3+/3w+/3Q1EUbQWA
      zWbD9evX8dxzzw1KeNkLwQH9d1s2Nzfj/vvvH7Tkbl9fH86cOSO9DoG/W1EUlJaW4t1330Vl
      ZaX0dZACFEUJ/vgSExNRUlKCXbt2Yfny5WEp3+PxBK84kpOTYTabg2u/REVF3Tach1vgOzAa
      jUhJSQkebG7tlpBp+fLlOH36NI4ePYoVK1YgPj4esbGxyMzMDMsB+Mknn8Tx48fx4YcfYsWK
      FYiJiYHNZkNWVlZY/v5bdXR0DOj2tdvt0rfEDAisCHDr+KPdbseNGzeklhtY+kMIoa0AMJvN
      mDlzJhwOB0pLSwe81tzcHByQkUWn02HZsmU4ePAgPv/5zw84y+vt7Q3LWXh+fj7Onz+PGTNm
      QFEUPPnkk3jnnXfQ2NgYloZfXFyM48ePB9dbmTFjBnbs2IFNmzZJXwUS6D/oBxZCUxQFs2bN
      Ch50GhoapK+DoyhKcPDTZDKhsLAwuAhgYHMU2e1Ap9Nh1qxZmDRpErZv346MjIywBF+AXq/H
      vHnz0N7ejq1bt6KgoCAsV78BUVFROHDgAC5evAifzxe8IzdwZ/onjw3DzWAwoLq6Gu+++y5i
      Y2MHrP5aWVkZrI8ser0eDzzwANauXau9MYD09HSYzeZBZzo6nQ4xMTHS54FHR0cjOTk5uB9s
      QGBxOtmbcaSkpKCrqyu4KbXBYEBxcTE6OzsRFRUlvf83Ojoaer0+ON1PURRMnDgRFosFfX19
      yMvLk3oWaDQaYTAYMG7cOOh0OmRkZARfq6ioQElJifSzULvdHtyJ7tZB72vXriErK0t6GwgI
      7L/b2dmJS5cuobS0NCyDsAFWqxVTpkxBQ0MDrl27hunTp4elKzA5ORklJSXIyMiAzWbD+PHj
      YbFYgttzjh8/XmoIm81mlJaWYsKECYiKisL48eMRFxcXDKCSkhLpEwESExORm5urvVlA4g8b
      MH9y+7XA3Yeyf/yBEfhPLgCnqipcLldYLsE9Hg9UVR000BSOpXiBP86CsFqtA35ogc8lHOsh
      BXZku7Usr9cb3KFJtr6+PhgMhgEH3EC9wvEdBHbCC0zBDEzJDVf5n2yD4S6fbbD/b9XUFYCq
      qtiwYQMuXbqE8+fPIycnJ/hBX7x4EW1tbVLnoQshcOLECRw6dAhnz56F3W4PbgDd29uLvXv3
      orCwUFr5QP8A4KZNm1BRUYHe3l5kZmYGfwAbNmxAcXGx1PJ7e3uxfv16VFZW4urVq5g4cWLw
      B7B//37Y7XapISiEwLZt23D27FmcPXsWWVlZwTGB6upq1NTUDLgqkFF+eXk59uzZg/Pnz8Nq
      tSIhISE4NrJp0yZMmTJFWvlA/5Tnd999F1VVVWhvb0d2dnbwaiwcbaCmpgabN29GRUUFnE4n
      MjMzg3dAh6P8np6e4NjXJ9vgvn37MG7cOOlt8P3338e5c+dw7ty5AW3w2rVrYW2DmloK4saN
      G4iOjsbzzz+P+fPnY+vWrcHleAMj4zK5XC5UVlbii1/8Ip599lns3bs3eEt+YEBItqNHj+Lp
      p5/GSy+9hIaGBly+fDnY/xqOMYAzZ85g+vTpeP7555Gbm4sdO3YEyw/HTKjGxkb4fD688MIL
      WLZsGbZs2RL83P1+v/RxoMB+tF/4whfw/PPP48SJE8HF8QCEpQ0cOnQIq1atwosvvoi+vj6U
      lZWFtQ3c2gYdDgcqKirC3gZLS0vx/PPPIycnBzt37gx7G1RVFS+88AIeeeSRiLZBTQVA4E5T
      RVGQlZWFvLw8HDlyJGwDUD6fD9HR0dDpdIiKisKjjz6Kbdu2Sf/Cb3XrrIOVK1fiyJEj0rcA
      vNWt30FJSQm8Xi+uXLkStu+gt7c3eMadkpKC0tJS7Nq1K2zlCyGCXQxmsxmPPfYYPvjgg7Ac
      +AOcTmfwM1iyZAkuXLiA5ubmsJXv9XqDbfDxxx/H4cOHw7oAXVdXF5KSkoJt0O12o7KyMmxt
      oKenZ1Ab3L17d0TaoKYCIDs7Gx9//HHwg77//vvR2tqK48ePh+XDt1qt6O3tDV51pKeno7Cw
      EJs3bw7bASApKQk3b94E0D/e8cQTT2D9+vVoa2sLS/m5ubm4cuUKgP6B78cffxyHDh3Cxx9/
      HJby09LSUFtbG5x5Eehu2L9/f1jagF6vhxAiuORwYmIiZs2ahXfffVf6MsQBWVlZwc/baDTi
      8ccfx6ZNmwZciciUmJg4qA2uW7cuIm0wEEIHDhzA1atXw1L+7dqgqqphWQoc6P+bA5tiaSoA
      YmNjkZ6eHmxoer0eTz31FLxeL44cOSJ9CQKDwYB58+YN6HaZNWsWioqKsHHjxrCsgfLQQw8N
      OONOSUnBU089he3btw/aI1WGwsJCuFwu9PX1Aej/TF5++WVcunQJVVVV0gffoqKiUFBQENx8
      XqfTYcWKFbBardizZ4/0NqAoChYtWoQLFy4Ev4OioiI8+OCDWLduXVhWpJ07dy6uXr0aLH/c
      uHH4/Oc/j3379qG7u1t6+fPnzx9wxh3uNjhp0iT09fUF26DRaMSXvvQlXLx4ER9//LH0Nhgd
      HT1g6XOdTofHHnsMFosFe/bskb4hjU6nw+LFi3H+/HntzQK6ncDiZIqiRGQdFuCPy8OG647Y
      Twp8BpEuX6fThXVbvlsFzsgi1QZGwnfg9/vDujXlJ8uP9N+vtTbIACAi0ihNdQEREdEfMQCI
      iDSKAUBEpFEMACIijWIAEBFpFAOAiEijGABERBrFACAi0igGABGRRjEAiIg06v8BNg8FKO7Y
      CVkAAAAASUVORK5CYII=
    </thumbnail>
    <thumbnail height='384' name='DVMT Per Capita' width='384'>
      iVBORw0KGgoAAAANSUhEUgAAAYAAAAGACAYAAACkx7W/AAAACXBIWXMAAA7DAAAOwwHHb6hk
      AAAgAElEQVR4nOzdeXhU5aE/8O/smUkme0L2hIRsgBFIkLDvVKBqUVAREK1La29vvdXb3uf3
      2Kfbvdr21ts+t/e21SpVQUAUq4CoqAl7kSUge/ZAVrKSTCaZ9Zzz+yN3powhATJnkgnz/TxP
      n6fOGc77npnJ+Z7zvu95X4UkSRKIiCjgKEe6AkRENDIYAEREAYoBQEQUoBgAREQBigFARBSg
      GABERAEqYANAEISRrgIR0YgK2AAQRXGkq0BENKICNgCIiAIdA4CIKEDddgHgcDjA2S2IiG5M
      PdIVuJHW1lZs2bLluttyc3OxZMkSAEBjYyM++OAD1NXVISIiAvfeey9yc3OHs6pERKOK3wdA
      SEiI+yTvIkkStm7dCqWy7wbGZDJhw4YNmDhxIh555BGUlZVhw4YN+MEPfoCUlJSRqDYRkd/z
      +wDQ6/X9ruTLysogCAJmzpwJADh37hyCg4PxzW9+ExqNBoWFhairq8PBgwexZs2akag2EZHf
      G3V9AIIgoLi4GLNmzUJQUBAkSUJ5eTny8vKg0Wjc75syZQrKy8vZH0BENAC/vwP4upqaGjQ1
      NWH16tVQKBQQBAFmsxnR0dEe74uOjobNZoPNZoNWq4XD4fDYLooinE7ncFadiMivjKoAEAQB
      +/fvR35+PsLCwgD0ncgdDge0Wq3He9XqvkOz2+3Q6XTu/3ZxOp1QqVTDU3EiIj80qgKgrq4O
      tbW1uOeee6BQKAAAKpUKer0ePT09Hu+1WCxQKpXQ6/VQKBT9TvaiKPYLBSKiQDJq+gBEUcSB
      AwcwYcIEj+YehUKBiIgI1NbWery/trYWoaGhPMkTEQ1g1ARAU1MTKioqMHv2bPfwT6AvACZP
      noyzZ8+io6MDkiS5m4oKCgrcdwpERORpVFwei6KI/fv3Izc3F3Fxcf22p6WlISMjA6+88gru
      vPNOVFdXw+FwoLCwcARqS0Q0OiikUTBOUhAEnD9/HklJSYiMjLzue2w2G7766itcvnwZ0dHR
      mDp1KoxG44D7dDgcHsNGiYgCzagIAF9gABBRoBs1fQBERCQvBgARUYBiABARBSgGABFRgGIA
      EBEFKAYAEVGAYgAQEQUoBgARUYBiABARBSgGABFRgGIAEBEFKAYAEVGAYgAQEQUoBgARUYBi
      ABARBSgGABFRgGIAEBEFKAYAEVGAYgAQEQUoBgARUYBiABARBSgGABFRgGIAEBEFKAYAEVGA
      YgAQEQUoBgARUYBiABARBSgGABFRgGIAEBEFKAYAEVGAYgAQEQUoBgARUYBSj3QFboYkSejt
      7UVXVxckSYLRaITRaIRCoXBvN5vN6Orqgl6vR3h4OFQq1QjXmojIv/l9AEiShPPnz2PXrl0Q
      RRGSJAEAnnzyScTFxUGSJJw7dw47duwAADidTuTl5eHee++FWu33h0dENGL8/gzZ2tqKLVu2
      4P7778cdd9wBpVKJpqYmhIWFAQC6urqwfft2LFmyBFOnTkVbWxteffVVJCcnY+rUqSNceyIi
      /+X3fQBFRUWYPHky8vPzodPpoNFokJKSAr1eDwA4e/YsIiIiUFhYCK1Wi4SEBCxatAgHDhwY
      4ZoTEfk3vw+AiooK95V8b28vBEFwb5MkCfX19cjNzfVo8584cSIaGxshiuKw15eIaLTw6yYg
      QRDQ0dGB4uJi1NTUoKenB0qlEnfddRe+9a1vQa1Ww2QyIT093ePfhYaGQhAE9PT0wGg0QhAE
      2Gw2j/dIkgSr1Tqch0NE5Ff8OgDsdjtEUURycjLuueceREZGorGxEW+99RY+//xzLF26FJIk
      uUcDuVw7OggAVCoVDAaDx3scDgc0Gs3wHAgRkR/y6yagoKAgGAwGTJo0CTExMVCpVEhOTsbS
      pUtRWloKoO9qv6ury+PfdXd3Q6lUIjg4eCSqTUQ0Kvh1ACgUCsTFxeHixYser+t0OvdVfnx8
      PCoqKjza+8vKyjBmzBg+C0BENAi/DgAAmDdvHoqKitDU1AQAsFqt+PLLL5Geng6FQoG8vDw0
      Njbi3LlzkCQJXV1d+OKLLzBz5swRrjkRkX9TSK6Gcj/ldDrx0Ucf4fjx44iLi4PJZEJoaCjW
      r1+P0NBQiKKIY8eOYefOnYiMjERXVxfS09OxZs0aaLXaAffLPgAiCnR+HwBA32ig9vZ2NDU1
      wWg0IjExEVqt1t0MJIoiOjs70dDQgPDwcMTFxd3w5M4AIKJANyoCwBcYAEQU6Py+D4CIiHyD
      AUBEFKAYAEREAYoBQEQUoBgAREQBigFARBSgGABERAGKAUBEFKAYAEREAYoBQEQUoBgAREQB
      igFARBSgGABERAGKAUBEFKAYAEREAYoBQEQUoBgAREQBigFARBSgGABERAGKAUBEFKAYAERE
      AYoBQEQUoBgAREQBigFARBSgGABERAGKAUBEFKAYAEREAYoBQEQUoBgAREQBigFARBSgGABE
      RAHqtgoAh8MBSZJGuhpERKOCeqQrcCMWiwWvv/56v9eXLl2KcePGAQDa2trwt7/9DZcuXUJo
      aCjuvvtu3HnnnVAoFMNdXSKiUcPvA6ClpQVXrlzBI4884vF6bGwsgL6A2LBhA5KTk/Hcc8/h
      0qVL2LRpE0JDQ5Genj4SVSYiGhX8PgCampqQnJyMCRMmXHd7eXk5nE4nHnjgAeh0OkRHR6O5
      uRlFRUUMACKiQfh9H0BzczPi4uKuu02SJJSVlWHixInQ6XTu16dMmYLy8nL2BxARDULWOwBR
      FNHd3Y2goCCPE7I3Wlpa0NnZiS1btiAoKAhJSUnIycmB0WiEJEno7u5GUlKSx7+Jjo6Gw+GA
      xWKBwWCAKIpwOBz96up0OmWpIxHRaORVAJw/fx7Hjh3D6tWrodFo8Oc//xkHDhxAQkICfvzj
      HyMhIcHrCs6ePRstLS0QRREmkwn79u3D3r178dRTTyE8PBx2ux0ajcbj36jVaigUCtjtdhgM
      BigUCqjVnofqdDqhUqm8rh8R0Wg15AAQRRE7duwAAGi1WpSUlGD79u145plncODAAfz1r3/F
      T37yE68rmJOTg5ycHPd/L168GBs2bMCXX36JpUuXIjg4GL29vR7/xmKxQJIkBAcHAwAUCkW/
      k70oiv1CgYgokAy5D8DpdKKhoQG5ublQKpU4fPgw8vLysGrVKqxevRrnzp3r1+wiB71ej8zM
      TLS2tgIAIiMjUVtb6/Geuro6hIaG8gRPRDSIIQeAUqmETqdDT08PrFYrjh07hsLCQigUCkiS
      BLVaDaXSuz5mh8OBL7/8Ek6nE5IkQZIk2O12lJaWupuXJk2ahNLSUrS0tECSJIiiiH379qGg
      oIDPARARDWLIl8gqlQozZ87EK6+8gqKiIrS0tGDOnDmQJAmHDh1Cdna2LG3sBw4cQFFRETIy
      MhASEoILFy4AAGbMmAGFQoG4uDjceeedePXVVzFlyhTU19ejubkZq1ev9rpsIqLbmULyYqyk
      zWbDp59+itLSUsybNw9Tp06FzWbDyy+/jBUrVmDixIleV9DpdKK0tBQNDQ3o7e1FfHw87rzz
      Tuj1evd7HA4HvvrqK1RXVyMyMhIFBQWIiIgYdL8Oh6Nf5zERUSDxKgBGMwYAEQW6W24COnz4
      MK5evXrD98XFxaGgoGBIlSIiIt+75QDYsWMHzp8/D6BvuKXNZkN4eLh7uyRJ6OjowLJlyxgA
      RER+7JabgMxmMwRBgCRJ+OMf/widToennnrKvV0URfzmN7/B9OnTcd9998leYbmwCYiIAt0t
      j9MMCQlBWFgYDAYDmpqaEB8fj7CwMPf/wsPDMWPGDOzcuROCIPiizkREJAOvngMwGAw4ceIE
      LBaL+3VRFHH58mWYzWbOtUNE5MeG/ByAWq3GPffcg5/+9Kd44YUXsHjxYiiVSpw5cwY7duzA
      ypUrZZsQjoiI5OfVMFBBEHDw4EFs3boVDQ0NcDqdMBqNmDt3LtavXw+j0ShnXWXFPgAiCnRD
      DgDXtAxA32RrV69ehcPhgNFoRGhoqN9Pw8AAIKJAN+QAEAQBv/3tb9HZ2YkXX3xx1E2tzAAg
      okDnVSdwSkoKjh8/jqqqKjnrREREw8CrPgCr1YrXX38dJ0+exEsvvYSYmJh/7Fih8Ho2UF/i
      HQARBTqvAuDtt9/G8ePHUVpaCp1Oh7Fjx7q35eXl4YknnpClkr7AACCiQOfViikhISGIiorC
      zJkz+23z5xFARETE2UBHuhpERCPG6zUTu7u7cfToUZSXl0MURWRlZaGwsBChoaFy1I+IiHzE
      qzuAxsZG/PSnP0VlZSWSkpKgUChQW1uLtLQ0/Md//AeSk5PlrKuseAdARIFuyHcAgiDgjTfe
      QE9PD1577TUkJSUBAFpaWvDCCy/gz3/+M1588UW/fyCMiChQDXmcptVqxfnz57Fy5UqMGzcO
      er0eer0eKSkpWLNmDUpLS9HT0yNnXYmISEZDDgCVSoWgoCC0trbi661ILS0t0Gq1UKu97mIg
      IiIfGfIZWqfTYeHChXjjjTcQFBSEadOmQaFQ4OTJk3j77bfx0EMPISgoSM66EhGRjLx+Evjt
      t9/Grl270NLSAkmSEBMTg2XLluGxxx6DXq+Xs66yYicwEQU6r58DEEUR7e3taGtrgyRJiIqK
      QmxsrN93/jIAiCjQeRUAp0+fxqVLl/DNb37TPRuoIAjYsWMHUlNTkZ+fL1tF5cYAIKJAN+RO
      YKfTibfffht1dXUek74plUq0trbi7bfflqWCRETkG14FQFtbm/sBsGvFxsbiypUrXBSeiMiP
      DTkANBoNEhMTsW/fPphMJvdQ0J6eHuzbtw/Jycl+PR00EVGg86oP4Ny5c/jxj3+MuLg49zDQ
      Y8eOob6+Hr/5zW9w5513yllXWbEPgIgCnVcBIEkSzp8/j23btqGyshIAkJGRgdWrV2P8+PF+
      PRKIAUBEgU6W6aBFUXRP+xASEuLXJ34XBgARBTqv5mqQJAlVVVX47LPPUF1djSlTpmDlypX4
      y1/+gqVLlyIjI0OuetJt5r2/V6Hqiskn+y4YF4NFeUk+2TfR7cSrADh69Ch+8pOfIDo6GgaD
      AVVVVVCr1TCbzfjwww/x/PPPy1VPus2YLA50mG0+2XeP1emT/RLdboY8TMdut2Pjxo1Yvnw5
      Nm/ejBUrVgDoWww+Pz8fp06dgiiKslWUiIjkNeQAcDgc6OzsRH5+vvspYBez2QxBEPrNEkpE
      RP5jyAGg0+mQmpqKPXv2oL293X2yb21txe7du5GZmdkvGLxlt9tx5coVdHd3e7wuiiI6OjpQ
      UVGBxsZGOBwOWcslIrodDbkPQK1WY/369fjJT36Cp59+GiqVCmazGSdPnoQkSfjXf/1XOesJ
      URTx+eefY9++fZg/fz6WLVvmfv3UqVPYvXs39Ho9LBYLxo0bh1WrVnGUDxHRILzqBM7Ozsb/
      /M//YO/evSgrKwPQ9xzAwoULkZCQIEsFXS5fvoxTp04hMzPT4wq/q6sLH374Ie69915MmjQJ
      nZ2deOWVV3DixAlMnz5d1joQEd1OvAoAhUKB+Ph4PPLIIx6vC4KAsrIy5OTkeFU5F5vNhh07
      dmDOnDm4evWqx7bTp08jJiYG+fn5UCqViImJwaJFi3Do0CEGABHRIIYUAKIo4uTJk/jqq6+g
      1+uxYMECxMXFQaFQQBAEvPXWWygvL8evf/1rrysoSRIOHToEtVqN6dOn4+OPP/bY1tjYiNzc
      XI95h3Jzc/Hee+9BFEUolcoBO6PZSX374ndLdGO3HACSJOHNN9/EK6+8gpCQEIiiiLfeegu/
      +93vkJOTg1deeQXvvPMOnnvuOVkq2NLSguLiYvzzP/9zvzZ9SZLQ3d3d74Gz0NBQiKIIs9ns
      /v82m63fv/36azR8BKfvxuo7HA73k+lENLBbDgCz2YxNmzZh5cqV+P73vw+z2Yyf/exn+POf
      /4yUlBR8/PHH+Ld/+zd885vf9LpyTqcT27Ztc99h3KyvX/2pVCoYDAaP1zgVxMhSqb1qfRyU
      RqNBSEiIz/ZPdLu45b/CS5cuwW63Y9WqVTAYDDAYDHjggQfwwgsv4MKFC/jZz36GBQsWyFK5
      kydPorS0FMnJyfjoo48AABUVFQCAjz76CPPmzYPRaERXV5fHv+vu7oZKpeJJgIhoELf8HIDd
      bodCoUBkZKT7taioKKjVavziF7/A/PnzZatcdHQ0VqxYgZCQEOh0Ouh0OqhUKiiVSuh0OiiV
      SiQlJaGsrMzjqeOLFy8iPj6e6xEQEQ1iSPfhkiShrKwMwcHBAPruCiRJgkajwYULFwAARqMR
      KSkpXlUuPT0d6enpHq/19vYCABYvXgwAuOOOO/D555/j1KlTmDJlCq5evYrPP//cvZ2IiK5v
      SAHgdDrx3HPPuad9FkURdrsdP/rRj9yvzZkzBy+++KJ8Nb3GtdNNh4eHY8WKFXj33XfxySef
      oLu7G3l5eZg6dapPyiYiul3c8noA3d3dqKqquuEwu7CwsH5X73Jw/t/oEfU1nYiSJKG3txdN
      TU0IDw9HRETEDaehYCfwyNpQVIqyhk6f7HtmThzuuyvNJ/smup3c8h2A0WjEpEmTfFGXm6K+
      zugRhUKB4OBgjBs3bgRqREQ0OrGXlIgoQDEAiIgC1C0HwPnz59HW1gYAqK6uRn19veyVIiIi
      37vlAPjlL3+Js2fPAgA2btyIXbt2yV4pIiLyvVsOgJCQEJw+fRqiKLr/R0REo88tjwJ66KGH
      8Mtf/hI7d+6E1WqFJEl47733+r1v9uzZ+Pd//3dZKklERPK75QBYsmQJUlJScP78eezcuROh
      oaGYO3duv/fJvSAMERHJa0hPAufk5CAnJwcWiwURERFYvny53PUiIiIf82pO3rVr10IURTQ3
      N6O1tRWSJCE6Otq9OAwREfkvrwLAYrHgrbfewu7du91DQyMjI3H33XfjiSee6DcHPxER+Y8h
      B4AkSdi+fTu2b9+O9evXo7CwEAqFAiUlJXj99deh0+nw9NNPy1lXIiKS0ZADwGq1ori4GKtW
      rcKaNWvcc+9nZGTAZrNh9+7dePTRRxEUFCRbZYmISD5DngrCtc5uVFRUv/b+6OhoOBwO98yd
      RETkf4YcAHq9HnfccQe2b9+OsrIy9PT0oKenBzU1NdiyZQvGjx/vXjCGiIj8z5CbgJRKJR57
      7DH8/Oc/x3e+8x0kJiZCoVCgvr4eGRkZ+O53v8uRQEREfsyrUUDx8fF4+eWXUVJS4l6XNzs7
      G1OnToXRaJSrjkRE5ANeBQDQt0DMvHnzMG/ePBmqQ0REw4XrARARBSgGABFRgPJqGGhRURE+
      +eSTGy4QT0RE/mfIAaBQKPDll19iw4YN6OrqkrNOREQ0DLwKgNWrV0Oj0eCdd96Bw+GQs15E
      RORjXo0CEgQB999/P7Zu3Qqn04kpU6a4t0VGRiInJ8frChIRkW94FQDvvfcePv/8cwDA+++/
      j/fff9+9bdasWVwRjIjIj3kVAN/73vfw7W9/+7rbdDqdN7smIiIf8yoAwsPDAfRNDd3T0wOF
      QgGDwcApIIiIRgGvngOwWq3YuHEj7rvvPixatAi///3vYbfbsX79ehQVFclVRyIi8oEhB4Ak
      SdiyZQu2bt2Kb33rW1i1ahUkSYJWq8WCBQvw6aef8vkAIiI/NuQAsFqt2L9/P55++mk8/vjj
      yMrKcm9LSkpCXV0dRFGUpZJERCQ/r+4AJEmCTqfzaPOXJAk1NTUICgpiXwARkR8bcidwUFAQ
      CgsL8cYbb0Cv16OtrQ09PT344osvsH37dqxYscK9TCQREfkfrxaEWbNmDTo7O/GLX/wCgiBA
      EAQcOnQIixcvxurVq+WsJxERyUwhedlT63Q60dbWhkuXLkGhUCAlJQUxMTFQq71eagBAX5NS
      ZWUl6urqYLPZEBsbi4kTJ3o8ZyAIAsrLy1FVVYXIyEhMmjQJBoNh0P06HA5oNBpZ6ki3bkNR
      KcoaOn2y75k5cbjvrjSf7JvoduJVG40kSRBFERaLBYIgAAA0Go2sbf9//OMf8fbbb6OpqQkW
      iwXFxcX41a9+hY6ODgB9J/9du3Zh69atsNlsOH78OH7/+9/DbDbLVgciotuRV5fpTU1NePHF
      F3Hy5Eno9XoAfXcEK1aswHe+850bXoXfjGXLliElJcV9R+FwOPDqq6+ipKQEixcvxpUrV3D8
      +HF873vfQ2JiIhwOB/7yl79g//79WL58udflExHdroYcAIIgYMOGDaipqcELL7yACRMmQKFQ
      4PDhw3jttdcQERGBxx57zOsKpqene/y3UqmEWq12B8Lp06cxbtw4JCQkAOi7A5k3bx7ee+89
      BgAR0SCGHAAOhwM1NTVYsWIFli9f7m72SUlJQXNzM/bv34/169d73RzkGm5qs9lw9epVHD16
      FCaTCZMmTYIkSWhra8PYsWM9yklLS0NXV5e7nd/VVHUtURThdDq9qhsNnS8fEpQkfrdEN2PI
      AaDRaJCamgqFQuFx8lUqlcjIyMCFCxdkqSDQN9Po2bNn0dPTg8TERKxbtw7h4eHuOYiCg4M9
      3u9qeurp6XG/7+snBFEU+aDaCJJ8+NmLosj1KYhuwi0HwJkzZ2AymQAAY8eOxWeffYaMjAyP
      ETVHjx7F1KlTZesMXrRoEaZMmYKWlhacPn0amzdvxvr16xEVFQWNRtPvj93pdEKSJHedlEpl
      v9lJOQpoZClVKp/tW6VSu/ukiGhgtxwAf/zjH3H69Gn3f4uiiP/3//6fx3skScLKlSu9rx36
      Vh6LiIhAREQEMjIyMHXqVLz77rv44osv8PDDDyM0NNQ9Isjl6tWr0Gg0snRCExHdrm45AH73
      u9+5h3wOumMZngNwNdNcuy+1Wo20tDScOHECkiQhMzMTn376KZYsWQKtVgtJknDixAlkZmZy
      KgoiokHc8ln66+3tvtTc3Iy//e1vmDVrFhISEqBSqdDc3IyioiLMnj0bCoUC2dnZ2LNnD3bv
      3o3Zs2ejqakJBw4cwOOPPz5s9RytLHYnzFbftJWrlUpEhHBRICJ/5tVlekNDA7Zs2YKWlpZ+
      2yZOnIj169d7s3tERkZi/Pjx+Oyzz2C1WqFSqaBSqTBjxgzMnDnTvQDNunXr8P777+NPf/oT
      VCoV7rnnHo/ZSen6TlS2YteJyz7Zd2xYEP71vkk+2TcRyWPIAeB0OvHKK6/g6NGjuPPOO6H6
      WqeeHBPB6XQ6zJ8/H3PmzIHFYoEoijAYDFCpVB7NO4mJifinf/ondHd3Q6/XQ6vVsvmHiOgG
      vAqApqYmrFq1Ck8++aRPT7gqlQohISE3fI9riUoiIrqxIV+m63Q65ObmwmQy8WqbiGgUGvId
      gEKhwIoVK/DTn/4Un3zyCYKCgjy2R0dH44477vC6gkRE5BtDDgBRFFFUVISKigr853/+Z7/t
      s2fPZgAQEfmxIQeA3W7H0aNHsXz5cjzzzDP9OoG1Wq3XlSMiIt8ZcgCo1WpER0cjISEBMTEx
      ctaJiIiGwZA7gdVqNRYuXIhjx46hqakJ7e3tHv/r7u6Ws55ERCQzrx4EO3XqFM6ePYsnnnii
      37j/6dOn44UXXvCqckRE5DteBcCSJUswYcKE626Li4vzZtdERORjXgXApEmTMGkSH/cnIhqN
      vAqA0tLS684DBABRUVED3h0QEdHI8yoAdu7ciaKiIo/XJElCZ2cnli5dil/84hdeVY6IiHzH
      qwB46qmnsHbtWo/XHA4Hfv7zn2PWrFleVYyIiHzLqwBwrdR1LUmSMGPGDBQXF2PRokWcJ4iI
      yE95P2fzdej1elRVVcFut/ti90REJAOv7gC2bduGkydPerwmCAK++uorTJw4kdNBEBH5Ma8C
      QJIkSJLk8ZpKpcLy5cuxevVqNv8QEfkxrwLg4YcfxsMPPyxXXYiIaBjdcgA4nU5YLJYb71it
      hl6vH1KliIjI9245AMrLy/HSSy9BEIRB3zd16lQ899xzQ64YERH51i0HQFRUFJYtW9av7d+l
      oqICe/bsQWJioteVIyIaqsOlV3Cqps0n+06OCsF9d6X5ZN/D6ZYDYMyYMXjkkUc8XpMkCdXV
      1di6dSsOHTqEqVOn9ntAjIhoOF0121DbavbJvrVqn4ygH3ZejwK6fPkyNm7ciL179yInJwe/
      /vWvkZeXxyGgRER+bkgBIIqix4k/NzcXv/3tbzF58mQolUoO/yQiGgVuOQCuXr2K//3f/0Vx
      cTHGjx+P3/zmNygoKOi3JjAREfm3Ww6A6upqfPTRR0hKSkJaWhoOHz6Mw4cP93vfuHHjcO+9
      98pSSSIikt8tB0BISAgmT54MQRBQXl4+4PuCgoK8qhgREfnWLQdAVlYW/vSnP93wfewHICLy
      b7ccAAqFgid3IqLbwO0xmJWIiG4ZA4CIKEAxAIiIAtSoCgBRFAecgwjoW4xmsO1ERPQPXk0F
      MRwEQcD58+dx8eJFXLlyBSEhIZg2bRrGjx8PpbIvv0wmE4qLi1FTU4OwsDDMmzcPY8eOZWc1
      EdEg/P4OYPv27di5cydiYmLwjW98AykpKdiyZQvKysoAADabDZs3b0ZTUxOWLFmC+Ph4vPba
      a2hqahrhmhMR+Te/vwOIiYnB3XffjbCwMABAdnY2LBYLTp8+jZycHFy6dAnNzc147rnnEBoa
      itzcXHR1dWHv3r1Ys2bNCNeeiMh/+f0dwPz5890nfxeTyeR+0vj8+fOYOHEijEYjAECpVGL6
      9Ok4e/bssNeViGg08fs7gGvb8W02Gz777DOUlZXh2WefBQB0dnYiKyvL431xcXGwWq2wWq0I
      CgqCKIpwOp0e+xVFEaIoDs9B+KmvfyZykkQJNpttwO2+/OwFQRi0bIt98NXsvKFVK6FSsu/J
      H9xo1UJviDf4fY8Wfh8AQN+6A83Nzdi+fTt6enrwne98B9HR0RBF0X2Sv5ZWq4VCoYDFYnFv
      c3UYX7vPQO8k9unx3+CJcV9+8jd6Wv1XH56BrwaLrZuTgeyEsBu/kXzOl79vhZRs+rkAACAA
      SURBVI/3P1z8PgAkSUJpaSneeecdTJgwAcuWLUNISAiAvi/AYDD0W6TeZrNBkiQYDAYAfSf/
      6wWARqMZnoPwU76cwluhwKCLAimUvmt9VCqVg5cNwFeDhdVqNRdD8hNf/5uXk0KpuC2+Z78P
      gMbGRmzatAlLly7FjBkz+p20wsPD0djY6PFaU1MTgoODb4sviIjIV/y+E/iTTz7BxIkTMXPm
      zH4nf4VCgYkTJ+LChQvo6uoC0Hdlf/jwYeTl5d0Wt2hERL7i13cAkiShrKwMEyZMwOeff+6x
      LTY2FpMmTUJKSgqSkpLwxhtvYMaMGairq8OFCxfwL//yLyNUayKi0cGvA0AURcyePRuCIKC3
      t9djm8PhANDXzrxmzRrs378fx44dQ0REBL73ve8hJiZmJKpMRDRq+HUAqFSqm1pW0mAwYOnS
      pcNQIyKi24ff9wEQEZFvMACIiAIUA4CIKEAxAIiIAhQDgIgoQDEAiIgCFAOAiChAMQCIiAIU
      A4CIKEAxAIiIAhQDgIgoQPn1XECBoNVkwaufXfTZ/n+wbCJCDVwXgYj6YwCMMFGUYOq1+27/
      vlr7kIhGPTYBEREFKAYAEVGAYgAQEQUoBgARUYBiABARBSgGABFRgGIAEBEFKAYAEVGAYgAQ
      EQUoBgARUYBiABARBSgGABFRgGIAEBEFKAYAEVGAYgAQEQUorgdARD5jcwho77b6bP/xEQYo
      FAqf7f92xwAgIp+53NqN178o9dn+f7Nums/2HQjYBEREFKAYAEREAYoBQEQUoEZFH4AkSbBY
      LDh9+jSysrIQFRXlsd3hcODcuXOorKxEZGQkCgoKEBoays4hIqJB+P0dgNVqxZEjR/Dyyy9j
      06ZNaG1t9djudDrxwQcf4KOPPkJwcDCqqqrwhz/8ASaTaYRqTEQ0Ovh9AOzfvx+HDh3C0qVL
      MXbs2H7bGxsbcebMGTz11FNYtmwZvv3tbyM2Nhb79u0b/soSEY0ifh8A+fn5ePbZZzFlyhSo
      1f1brM6cOYOsrCyMGTMGAKBWqzF37lyUlJQMd1WJiEYVv+8DiI6OBgAIgtBvmyRJaG9vR1pa
      mkd7f0pKCrq7u2G326HVaiFJUr9/L4oiHA6Hbyt/E5zO/scl7/6dcDiun/OiKPqsXEnCoJ+v
      5Muyb/DdSj4rue936g+/K39xvb9bOTmcTgzU0+fT37co3Rbfs98HwGAkSUJPTw+Cg4M9Xtfr
      9QCAnp6eQQNAknx5Krg5vv4DEQRhwDJ8GwD9P/Ovb/cVURJ9/rkOWLYwcmUP5mBpM05Utflk
      30lRwVhVmHbdbYLgu98YAAhO54CDPSRp5H7fo8WoDgAA0Ol0sNvtHq85nU5IkgSdTgcAUCqV
      7v/v4nA4oNFohq2eA9FZffsHotPpEBSku+626zWpyUWpVCAoKGjg7SqVz8pWqdSDlq2A7+4C
      NFrNoGWPFIegwNUe31yxRoaIAx6zVuu7aSAAICgoaMAAUKl8+PtWKf3ye75Vft8HMBiFQoGw
      sLB+I4Pa2tqg1WrddwJERNTfqA+ArKwsnDt3DlZr35WGJEk4fvw4srOz+RwAEdEg/L4JqKGh
      AQ6HA6Iowmq1orm5GUFBQVCr1UhMTERmZiZ0Oh0+/PBDzJo1C01NTfj73/+Op556aqSrTkTk
      1/w+AD777DN0dXUB6OvcPXXqFE6dOgWj0YjHH38cer0e69atw44dO7Bx40ZotVqsXLkS6enp
      I1xzIiL/5vcB8Pjjj9/wPWPGjMGTTz6J3t5eaLVaaDQaNv8QEd2A3wfAzVIqlQgJCRnpahAR
      jRqjuhOYiIiGjgFARBSgGABERAGKAUBEFKAYAEREAYoBQEQUoBgAREQBigFARBSgGABERAGK
      AUBEFKAYAEREAYoBQEQUoBgAREQBigFARBSgGABERAGKAUBEFKAYAEREAYoBQEQUoBgAREQB
      6rZZE5jI39W2duPdv1f7bP8/vCcPKqXCZ/unm/fW3jK0mqw+2ffc8fGYmhkry74YAETDxO4U
      0dJl8WEJEgAGgD/oMNt89l332Jyy7eu2aAKSJAlOpxNmsxl2ux2SJI10lYiI/N6ovwOQJAk1
      NTV499130dHRAa1Wi3nz5mHBggVQKm+LfCMi8olRHwBmsxmbNm3C9OnTMXv2bDQ2NuL1119H
      bGws8vLyRrp6RER+a9RfIl+4cAHBwcFYsGAB9Ho9MjIysHjxYuzfv3+kq0ZE5NdGdQC4mn/G
      jx8PtfofNzN5eXm4fPkyRFEcwdoREfm3UR8A3d3diIiI8Hg9PDwcTqcTvb29I1QzIiL/N6oD
      AACcTqfH1T8AKJVKKBQKOJ3yDZciIrrdjOoAUCgUMBqN6O7u9ni9p6cHABASEjIS1SIiGhVG
      fQDExsaiqqrKY+x/ZWUloqOj+90ZEBHRP4zqAACASZMm4dKlS6isrIQkSejt7UVxcTEKCwtH
      umpERH5t1F8iR0VFYf78+di0aRNSU1PR2tqK4OBgzJgxY6SrRkTk10Z9AKhUKsyfPx/jxo1D
      bW0t7rrrLmRkZECv19/Sft4oLoXZ6ptO44V3JGJ8csSN30hE5IVemwMbispu+v2jPgCAvhBI
      S0tDWlrakPfR0NELU69dvkpdw2x1+GS/RETXEkQJdW3mm37/qO8DICKioWEAEBEFqNuiCehG
      RFHs91CYKIoeQ0cXThgDu9M3U0ckhOtgt1+/eUmnApZPTvRJuQCggjhg2SlRep+VrdeqBiwX
      AArGRmBcbLBPyo6PMAxa9rLJifDVjOGRBvWAZYfpVT79rp0OBwTF9dcDyIoLRrDWN2WHGbQD
      HnO4j4/ZbrdDMcAxZ8eHwKgb/mMGgFnZMeiVcd7+a6VG6wcsWyEJt/R5K6QAmDxfkiQIgtDv
      tYF+OL6uC4ARK3skygX6Anekpufmdz28Ruq7DsRjBrw77oC4A1AoFP0eCnM4HCPyoJjT6YQo
      itBoNMNets1mg0qlGpHj7u3thV6vH/Y/UFHsuwMKCgoa1nKBvt8YgBH5vK1WKzQaDVQq1bCX
      3dvbC4PBMOzlWq1WaLXaYT8RS5IEu90+IscsCAIEQYBOpxvSv2cfABFRgGIAEBEFKAYAEVGA
      Cog+gOsZqQ4b11TVI0GtVo9Y2SPR5wFcv/9nuIzkmtRqtXrEyh+p7zpQf9/e9PMExCgguYii
      CIvFAofDAUmSoFQqodPpoNPpfP7DczqdsFgs7uGsarUaQUFBPv/Ruzq4bDYbBEFwn1ANBoPP
      TzCSJMFqtcJms7k/b61Wi6CgIJ9/3oIguD9vSZKgUqmg0+mg1Wp9XrbD4YDFYnGPXFOr1dDr
      9VCpVD7/rm02G2w2G0RRhEKhgEajgV6v9/l37U/H7PqN+fqYR/p8YrVaA/cO4FZIkoSqqioc
      OnQIJpPJPfZYoVBAp9MhJSUFc+bMQVhYmOxlOxwOnDp1CqdOnXKfkFypr9frMXHiRBQUFAx5
      FMBgzGYzDh48iOrqavcfCABotVoYjUZMnz4dWVlZsv+hSJKEpqYmHDhwAK2trbDZbO4/CJ1O
      h9jYWMyZMwdjxoyR/Q9FFEWUlpbiyJEjMJvNcDgc7u86KCgIGRkZmDlzpk/WmrDZbDh27BjO
      nTsHq9XqcTI0GAyYPHkyJk+eLPsdjSRJ6OzsxMGDB1FbW+sOXKDvuw4PD8fMmTMxduxY2b/r
      Gx3zlClTMGnSJJ8d84EDB1BXVwebzebedu0xp6Wl+eT3XVtbi/3796Ozs9PjfKLVapGQkIB5
      8+YhMjJS1nKBvhP/mTNnUFJSgp6eHt4B3IyzZ8/is88+w+LFi5GcnAyDwQCVqu9BJ5PJhNOn
      T6OsrAyPPfYYQkNDZStXEATs3r0bbW1tmDt3LmJiYtyT3FmtVrS3t+PgwYPQarVYuXKlrEP+
      bDYbXnvtNaSmpiI/Px9hYWHQ6XTuK+OGhgYUFRVh6tSpKCwslPVEXFtbi+3bt6OwsBBZWVkI
      Dg6GVtv34E1PTw/Ky8tx+PBhrF27FvHx8bKVK0kSvvzySxw/fhyLFi1CQkKC+yrUZrOhs7MT
      J06cQHNzM9auXSvrsD+n04nt27dDEATMmjULUVFR0Ol07rug1tZW7N27F7Gxsbjnnntk/bxN
      JhPeeOMN5ObmIi8vD6GhodDpdO6r8suXL2Pfvn1YsGAB7rjjDtnKdjqdePfddwEAs2bNQmRk
      ZL9jLi4uRnx8PJYvXy7rMXd1deHNN99Ebm4u7rzzThiNRvcx9/b2uo958eLFmDBhgqxll5WV
      YdeuXViwYAHS0tJgMBigVvc9QGg2m3H+/HmUlJTgiSee6LfcrTdEUURRURGqq6sxf/58jBkz
      BpDohl5++WXpypUrA24XRVHatWuXtHfvXlnLbW1tlf7whz9IPT09A77HZrNJv//976WmpiZZ
      yy4pKZG2bt0qiaI44HuuXLki/fd//7dksVhkLXvLli3SyZMnBy372LFj0tatW2UtVxRF6aWX
      XpI6OjoGfI/T6ZS2bt0qlZSUyFr25cuXpVdffVWy2WwDvsdqtUq/+tWvpK6uLlnL3rdvn/TR
      Rx8N+nlXV1dLr7zyimS322Urt6amRvrLX/5yw2N+6aWXZD/m4uJi6eOPPx70mCsrK6VXX31V
      1mOWJEn605/+JFVVVQ1adlFRkbR7925Zy+3u7pb+67/+SzKZTO7XOAroJlgsFkRFRQ24XaFQ
      YMyYMf2WpvSW3W6HTqcbdGprrVaLiIgI9zKYcjGZTIiLixv0ysdoNLqvXOTU09ODmJiYQcuO
      i4uT/fMG+j7z8PDwAberVCrExMTIXrbFYoHRaBy0M1Gn0yE0NBS9vb2ylm0ymW7YnBYeHg5J
      kmRdZ/tmjzksLEz2Y+7u7r7hMUdERFx3Ghlv9fb2Ijo6etCy4+Pj0dXVJWu5TqcTSqXSo/mS
      AXATUlNTcejQIUiS5DF/ENDXbGCxWHD06FFkZGTIWq7rh19dXT1g2Q0NDWhoaEBcXJysZWdm
      ZuLo0aMwm83XLVcURZw9exYajUb29vCxY8fi8OHD7s6xr5ftdDqxf/9+ZGZmylouAIwZMwbH
      jx8f8PPu6urCmTNnvJp6/HpiY2PR0NCApqamfuW6yq6pqcHVq1cRHR0ta9mZmZk4cuQILBbL
      gN91SUkJwsPDZe1riouLQ319Pa5cuTLgMVdXV6Ozs1P2Yx43btygxywIAkpKShARESF7/9rY
      sWNx6NChfvORucp2OBzYv38/srOzZS1Xr9dDrVbjwoUL7t83+wBuQm9vL958802IoojMzEz3
      j8JsNqOlpQUXLlzAjBkzMG/ePNk7JS9duoRt27ZhzJgxSE1NRVhYGBQKBbq6ulBfX4+6ujqs
      XLkS48aNk73sI0eOoKioCDk5OYiLi0NISAgcDge6urpQWVkJh8OBtWvXytpOCfRdhX/wwQeo
      r69HTk4OoqKioNfr0dvbi/b2dly8eBFpaWm4//77ZZ/qoLOzE2+99RZ0Oh0yMjIQHh4OjUYD
      s9mMpqYmVFRUYMGCBZg2bZrsn3dZWRm2b9+O1NRUpKSkwGg0QhRFmEwmXL58GU1NTXj00UeR
      mCjvBGeiKKK4uBjHjx9HTk4OYmNjERISApvNhqtXr6K8vBxBQUFYvXq17GFfWlqK999/H2lp
      aUhOTu53zFeuXMGjjz6KhIQEWcsVBAHFxcU4ceLEgMes1+uxevVqBAfLO2mh1WrF5s2bYTab
      kZ2djYiICAQFBaGnpwetra04f/48Jk+ejLvvvlv231hTUxM2b96MiIgIjB07lgFws0RRRE1N
      jfsqzOl0Qq/XY8yYMcjJyZH9JHgtm82G8vJy1NXVobu7G5IkwWg0IikpCVlZWbe8+tnNcl3x
      lpWV4cqVK+jt7YVKpXL/eMaOHeuzuWak/xsJVFlZiZaWFjgcDuh0OsTExGDcuHE3bJ7yhtPp
      RHV1NS5duoTOzk4IgoDg4GDEx8cjOztb1o7+r7NYLCgrK0N9fT3MZjMUCgVCQ0ORnJyMrKws
      aLVan5QrSRI6OjpQXl6O5uZmWCwWqNVqREVFIT09HSkpKT4bFtnb24uysjI0NDS4jzksLMz9
      +/blMbe3t7uP2TV/UmRkpM+PWfq/kUBVVVXo6OiAw+FAUFAQYmNjkZWVdcMmIm/Y7XZUVlai
      traWAUBEFKj4HMBNqKurQ3R0tMeVdnt7O44cOYL29nYkJydj2rRpst8qunR2dqKsrAyXL1+G
      yWQC0NcBm5yc7L77kPtqwWKxoK2tDcnJye7XnE4nTp8+jYsXL0Kn0yE/P7/vNtIHVypOpxM1
      NTWoqKhAS0uLu0M8NjYWmZmZSEtL88nY8MuXLyM+Pt6j3be5uRlHjhyByWRCeno68vPzfXbX
      1d7ejtLSUtTW1npcDaemprrvPuT+vLu7u2E2mz2G1DocDpw4cQIVFRUwGo0oKChAUlKST75r
      V7NeXV0dzGYzlEolQkNDkZqaipycHBiNRtnLNZlM6Onp6XfMx48fR2VlJYxGI6ZOnYrExESf
      HLPD4UBFRQUqKyvR3t7uvgOIi4tDVlYWUlNTZS9XEATU19cjISHB3fGu+vnPf/5zWUu5Db3/
      /vuIj493P+hlNpvx5ptvwmg0YsKECaipqcGXX36JvLw82U9KFy9exHvvvQcASE9PR3Z2NtLS
      0hAcHIyGhgbs3bsXwcHBsj8U1dTUhKKiIkyePNn92sGDB/H3v/8d06ZNg1arxaefforIyEjE
      xsbKVi7Q1ySwbds2lJeXIyEhAePGjUN2djZiYmLgcDhw7NgxXLx4Ebm5ubJ/3lu3bsW4cePc
      Yd7Z2YnXXnsNycnJGDduHC5cuIALFy5gwoQJsjd/nTx5Eh988IG7/yE7OxupqakwGAy4dOkS
      ioqKEBsbO+iItKGorKzEyZMnMX78ePdrn376Kc6dO4fCwkIIgoBPP/0USUlJsjd1lpSU4IMP
      PnA/ZJeTk4OUlBTo9Xr3McfFxcn+UFR5eTlOnz6N3Nxc92sff/wxLl68iMLCQjidTnz66adI
      Tk4edFTYUHR3d2Pjxo1oaGhAcnIyMjMzkZWVhaioKFgsFhw+fBi1tbXIycmRtQnKbrdj+/bt
      yM7Odk+PzjuAIaisrERoaCjuueceqNVq5Obm4q233kJZWRny8vJkK6e7uxufffYZHnjgAaSk
      pPQ7wU+ZMgXNzc148803kZ6eDqPRKFvZX2ez2XD8+HGsXr3a3SEXExODoqIi5ObmyvpDPXjw
      IMLCwvDwww9Do9H0O+4ZM2Zg586dOHjwIBYtWiRbuddz5swZZGRkYMmSJVAqlcjOzsaGDRtQ
      V1eH9PR02cppb2/H/v37sXbt2uuGeX5+Purr67Fx40b8+Mc/9uncMxaLBSUlJXj22WcRFhYG
      SZIQGhqKgwcPIjU1Vbbvuq2tDQcOHMC6desQGxvb75gLCgpQX1+PTZs24Uc/+tGwHPPzzz8P
      o9Ho7mdzHbOcF1cff/wxMjMzMWfOnOtewMyaNQtbt27FiRMnMG3aNNnKvR4OAx2ChoYGZGVl
      ub88lUqFzMxMXLlyRdZyuru7YTAYrnvyB/7x/EFiYiKam5tlLfvrTCYTDAYDoqKi3I+tJyYm
      QhRFWK1WWcuqq6tDQUHBgPPuaLVaFBYWorq6WtZyr6ehocEj4HQ6HVJTU9Ha2iprOa2trYiP
      jx/wTk6hUCApKQlRUVFoa2uTteyva29vd4+KcZWdmpqK3t5e9yI3cmhtbUViYuJ1T/6ucl13
      He3t7bKVez1tbW2Ii4tz3/kpFAqkpaXJfsxA32+qoKBgwLtXnU43bL9vBsBNEgTBPWbXbrf3
      u9q2Wq2ytwvrdDpYLJZBH/Ky2+1oaWnxyTxEoii6j1sQBGi1Wo8f7bXztsjJaDSisbHxumPD
      gb62+rq6Op+NvHJNAAf0fb5fH/potVplX2EsODgYV69e9ZiT5utsNhva29t9cqcniqJ7XLrT
      6YTBYPA4KTudTqhUKlmbvYKDg9HR0THoMbumPPH1MTscDgQHB1/3mOUeCRQcHDzgsw/AP/qi
      fDEXkOtv2VU2m4BuQkhICHbt2oXIyEhERkaisbHR4/bf6XSiqqoKy5cvl7XciIgIZGdnY9Om
      TZg3bx7GjBnjvg12Op1oa2vDvn37MG7cONnbhdVqNZqamrBx40aEhYVBq9V6rKvsekgnOjpa
      9mF6c+fOxebNm2EymTBx4kQEBQVBpVK55yE6d+4cTp06hccff1zWcgEgKCgIH3zwAcLDwxEd
      HY3W1laPk0Jvby+uXLmCuXPnylqu60p48+bNmDt3LqKiotzB6nA40NzcjKKiItx1112yj8XX
      aDSoqqrCxo0b3e3dron/gL7v2tUfI2fYJyYmIioqClu2bMHcuXMRGRl53WOePn267AMsNBoN
      KioqsGnTJvfF09ePuaysDImJibJf4HzjG9/Au+++i5kzZ7qHuapUKveMvydPnkRZWRmeeuop
      Wct13bm/99577t83h4HeBNdEYJ2dnWhvb0drayumTZvmfvq2uroax44dw0MPPeSTnvsLFy7g
      6NGj6O7udl+BucamT506FXfccYfsP1LXwzidnZ24evUqWltbYTAYMGPGDCiVSjgcDmzbtg3z
      58+X/cEkoK8Z4vDhw+7bYFcAKBQKpKenY+bMmbKPfpL+b2pg13fd1taGtrY2zJs3zz0Vwtmz
      Z1FbWyv75GTAP0ZZHT9+HBaLxeO7Dg0NxbRp02SfmMy1/2u/65aWFkRFRaGgoAAKhQK9vb3Y
      vn07li9fLvuFxmDHHBYWhmnTpmH8+PHDcszR0dHIz8+HQqFAT08P3n//fZ8cM9A3suzAgQOo
      r693z+4rCIK7n8kXM866Zi249vfNAJCBazpXX3ZSuU5Ortvl4Zo3fLD6uJpCfFkHQRDQ29sL
      QRA85ogfCdL/rY2gUql8usiM67N1zbEUFBQ0LOsQDEQURfcwXF/VIVCP2XVXKwjCsK29cC0G
      wE2ora2FXq9HTEzMiNWhp6cHLS0t7v4Ag8GA2NjYfu2WchJF0d0WHxcX1y/g7HY7GhsbZZ8X
      x/WTvPa4XE9ttrW1ITw8HLGxsT6Zp72mpgYRERE+fbL7Rrq7u9HS0gKLxQIA7mG+er3eZ9+1
      IAhobGyEQqFAfHx8v5Dt7e1FR0cHkpKSZCvTPR/N/zVNuNhsNtTX10OSJCQnJ/tkrQuTyYTW
      1lakpaWN2AWF0+lEc3Mzurq63H1skZGRiIqK8ukTyK67ntjYWPYB3IyioiLU1tYiKysLCxcu
      lH3c+2B6enqwe/duVFZWIioqyt0555oXJzk5Gffdd59POsl27NiBS5cuISQkBE6nE6tXr/YY
      E20ymfDJJ5/gmWeekbXc/fv3IzQ0FFOmTAHQ96N1zUuUkJCA1tZWpKen44EHHpD9j3f37t24
      evUqJk6c6LNFOQbS1dWFnTt3oq6uzr32gyRJ6O3tRVtbG7KysnDPPffI3gHtmpe/vb0dGo0G
      Go0Gq1ev9ljvoKWlBYcOHcLatWtlK7ezsxO7d+/Ggw8+6O5Hunr1Kv7617+67yw7Ozvx+OOP
      y7ruAwBcvnwZW7ZsQXx8PBYuXIicnJxhCwJJknDs2DF88cUXCAsLQ0hICLRaLaxWK7q6uiCK
      IlasWCHrMGOX0tJS7Ny5E2PGjEFLSwsD4GZIkoR169ahpaUFb7zxBhISErBo0SL3kD1fXZW5
      2tnj4+Pxwx/+0ON21DVy4e9//zvefPNNfPe735W1CaqhoQHV1dX4wQ9+AKVSiWPHjmHbtm14
      7LHHPK7Iru04k4vNZvOYYrqnpweff/45nn76aYwZMwY2mw0bNmzAmTNnPB5Uk4NKpcLjjz+O
      mpoavPrqq0hPT8fChQvdw199xWq1YtOmTbjjjjvw0EMPeSz1KYoiHA4HiouLsXHjRjz11FOy
      1qWyshLd3d347ne/C6VSieLiYrz77rtYt26d+6TomhVUTqIoekzzLEkSioqKkJ2djaVLlwIA
      Tp8+ja1bt+KHP/yh7J9/fn4+8vPz8cUXX2DPnj1YuHCh+wE/X37XX375JU6ePIlnnnnGPbmj
      QqFwf8YNDQ14++238eijj3o8ie8tm82GoqIiPPTQQ0hJSUF7ezuHgd4sjUaDwsJCPP/888jN
      zcXmzZuxadMm1NbWeoyOkVN7eztsNhuWLFnibht0/Vhc64fOmzcPGo0GTU1NspZdX1+P8ePH
      Q6PRQKVSYdq0aYiIiEBRUZFPTvo3qktKSgri4+OhVCqh1+sxZ84cXLx40SflBQUFYc6cOXj+
      +eeRnJyM119/HVu3bkVTU5PPvuv6+noYjUbMnj0bWq3W47tWqVQICgrC0qVL0d3djY6ODlnL
      rqurw8SJE93f9cKFCyEIAg4fPjzgUEVfcI38KSgocA85nTx5MiwWC8xms+zlKZVKjB07Fk8+
      +SRWrVqFkpIS/OEPf0BJSQmsVqvPjv3o0aN46KGHEBkZ6R5m6vqbVqvVSE1Nxd13341jx47J
      Wq7dbofD4XBPchcTE8MAuFVqtRoFBQV49tlnkZeXhw8++AAbN25EZWWl7AtH3CyVSiX7j9X1
      DIKLUqnE8uXLUVZWhtLSUlnLuhGz2dxvJIZer/d5EGm1WsyYMQPPPfccMjIysGXLFmzduhWX
      Ll3ySRDczFWnSqWS/biv912vWrXKPSXBcBEEAYIgeIx+USgUPv+uFQoFkpOT8e1vfxsPPvgg
      Ll68iFdffRVHjhyRfaGlm6VUKmX/m3ZdUFx7nmIADJFGo8HkyZPx/e9/330bKfcfS0REBDQa
      DQ4ePHjdhStsNhuOHj0Ki8Ui+4IwGRkZqKio8HhIJzg4GA8++CB27tyJc+fO+TTwysvLceHC
      BfcUwV8fdXP58mXZj3kgWq0W06ZNw/e//33k5OTgk08+kf1J4ISEBJhMJpw4ceK6D0ZZLBYc
      OHAAWq1W9mGJWVlZuHjxokezW2hoKB588EG88847KCsr89mdT1dXF06dhYEw8gAAD0tJREFU
      OoVLly6hvb0dkiR5tMVbLBb09vbKPiRyIElJSVi7di1WrlyJ2tpaHDx4UPYyJk+ejF27dqGj
      o6Pf37Qoiqivr0dxcbHszZt6vR5RUVGorKx0v8Y+AC+p1Wrk5eUhNzfXJ1fh999/Pz7++GOc
      OnUKoaGhHgvCmEwmhIeHY/Xq1bI/jGU0GpGfn4+DBw9i4cKF7qvTxMRErFu3Djt37oTJZJK9
      QxIAJk2ahAsXLuDMmTPumSrz8/Pd281mM7766is88cQTspc9GJ1Oh4KCAuTl5cneRmwwGLBq
      1Srs3r0bhw8fRnh4OEJDQ93PY3R3dyM6Ohpr1qyRfYRIbGws0tLSUFJSgsLCQvexuTra9+zZ
      g66uLtk7Yo1GIxYsWID6+nqcP38eNpsNSqXS4/j27t2LqVOnDutIHdc0Jw8++KDs05wAfXNZ
      OZ1ObNy4ETqdDuHh4e4FYTo7OyGKIhYvXix7J7BKpcK8efPwxRdfuBej5zDQm1BaWoqkpKRh
      uwr5OkEQcPXqVbS1tbnXojUajYiKikJERITPxqQ7HA709PS4Q8fF1QHd0dEBURRlX63pWoIg
      uBfqcIWc2WxGe3v7gHMkDZUkSbh48SLGjh3rs+meb8TpdLofvLt2QZjo6GiEh4f77ERot9th
      tVr7Tb3sevaho6MDSqUSY8aM8Un5rnKunYZCkiRUVVUhKSlJ9guNjo4OdHV1YezYsbLu92ZJ
      kuTuz7l2QZjo6GhERkb67PkaURTR3d2N4ODgvoEGDAAiosDEPgAZCIKA8vJyn4xUuBGz2Yzy
      8nKftdEOprm5eVg7Ca9VXV3t8xkir8dut6O8vNyj03S4dHV1oaKiYtjLBYDGxkY0NjYOe7md
      nZ0jdswNDQ0jcsxAXx9YV1eXz8thAMjA4XBg8+bNqKurG/ay6+rqsHnzZtmnrL0Zx48fx549
      e4a9XAD48MMPcfbs2WEvt7u7G5s3b5a9E/hmVFRUYOvWrcNeLtD3cN6BAweGvdyKigps27Zt
      2MsFgH379vmkE/hmbNmyBVVVVT4vh01AMnBNoeuLqWNvxDVl87UPDg0X17SyvpwXZyAOhwNK
      pXLYH+Mfye9aEASIoujTOacG4hrxNdzfdSAeM9D3+x6O3xgDQCZ2u939IAeR3K43P9LtXO5I
      lj1S5TqdTnR1dSEyMnLYyuaawDI5fPgwHA6H7GO0LRYL9u/fjyNHjsBisbifhr3W3/72N2Rk
      ZMh+NdzW1oZPPvkE586dc09Ude0P02QyoaioCJmZmbKWK0kSKioqsGfPHtTU1CA6OtpjXhqg
      b2RWfX297M8COJ1OHD16FPv370dHR4fHGgwue/fuhcFgkH1UmNVqxaFDh3DmzBkYDAaEhYXB
      bDZj586d2LNnD5qampCWlib71bBror19+/a5FyIJCgpCU1MT3nnnHRw6dAhOpxMJCQmyX5Fe
      e8zBwcEIDQ2F2WzGjh07sGfPHly5csVnx+xaT+P/t3elPU1tbXSd0tIKLbTQAlWwVRGwLSVG
      QIgxDlchQQFxipJgjDHxp/gL/KRBUeNARKmgRg3GSBxAQmtRQUEQKiBjKW2KdPJ+4D3n9Ygd
      4rt3ufe1K+kX0rC6zmn33ucZ1jMyMoLU1FRIJBKMjY3hxo0beP78Ofx+PxXNwWC323Hv3j0Y
      DIaoccZyAITgcrlCTjb6Hfj9fty9exd2ux1btmxBX18fTCbTsn6D/v5+4klgj8eD+vp6qFQq
      5Obm4sGDBzCbzTxuj8eDoaEhorwAMDQ0hNbWVuh0OiiVStTX12NycpL3ntnZWeJx+O/fv6O9
      vR09PT3YsmUL7HY7rl+/vuy+2mw2nocNCQQCAdy+fRtfvnxBSkoKGhsbYbPZ8PjxY8hkMtTW
      1gIAlZzLwsICGhoaACyFHq5duwan04mbN2+ipKQENTU1sFqteP/+PVHeQCCAW7duYXR0lKf5
      4cOHSE5ORm1tLQKBABXNbrcbly9fBsMw8Hg8uHbtGlwuFxobG1FaWoqamhpYLBYqXe/d3d1o
      bm5e9nr06BGGhobQ0tICk8lEPKfo9XrR1tbG44zFKyLA8PBw2B/89PQ0UeMm4L+WtadOnUJC
      QgKys7Nx/vx5dHZ2Uh8WPTAwgNWrV2P79u0AALVajYaGBqjVauINQT/DbDZjx44dKCgoALA0
      ke369es4c+YMlcazH9HV1YXTp09DoVAgJyeHO4nu37+f6qlsenoadrsdp06dgkQigUajQWtr
      K8RiMSorKyEUClFVVYWzZ89i3759RBv/2MlXrAHbkydPcOfOHeTk5MBgMIBhGJSXl+Pp06dE
      T6dTU1NwOByc5qysLNy7dw9isRjV1dUQCoWorq7G2bNnUVFRQVRzX18fsrKyOM1tbW24ffs2
      cnNzOc1lZWVob2+HTqcjeu+tVisYhsGmTZt4f3c6nbDZbDzPK5Lw+Xzo7OyE0WjkrO1jTwAR
      4NmzZ2hubobZbA76Im3GBoAbPsJ+EYRCIY4dO4YnT54QH0D/MxwOB1QqFecfolKpUFZWhqam
      Jp5lAA3Mz88vjav7D7fBYMDatWvx6NEj6v4/brebs7yOi4tDRUUF+vv78fHjR6q8LpcLSUlJ
      XAOQWq2Gz+dDZmYml1cSiUSQy+WYn58nyj01NcU11TEMg6KiIlitVuTn53MhP7lcDp/PR/Te
      O51OJCcnc5rXrFkDj8eDrKysZZrZBkhS+JVmi8XC6/JWKBTwer3EK+yqqqrg8XgwPDwMvV6P
      oqIiFBcXo6CgAEqlEoWFhSguLoZSqSTKK5FIcPjwYc7iZfPmzbENIBKUl5eDYRgcOXIEtbW1
      v3zl5+cT52VNsH40pJLL5di3bx8aGxsxNzdHnJNFWloaRkdHeQkxo9EItVoNk8lE1QdIpVLx
      hsILBAKUl5djZGQEXV1dVDcBuVzOCy1JJBIcP34cTU1NmJiYoMYbHx8Pj8fDaRMKhcjIyFg2
      j4AdXk4SCQkJvB6WpKQkrtOcBTuximSRg1gsXqY5PT39l5p/zgH9r/hZc3JyMmQyGY+bhmYA
      SElJwenTp6HRaHD+/Hn09PRExUiSYRhs3LgRZ86cwezsLC5duhTbACJBWloa1q9fj46Ojqjy
      SqVSZGZmwmKx8BZig8EAo9GIhoYGWCwWKj0AWq0WTqeT92TDMAwqKyvhdrtx9epVfP78mYpl
      7ubNm/HmzRte2C0hIQFHjx7Fy5cvcf/+fUxPT1PhLikpwbNnz3ibzOrVq3HgwAFcunSJmkNk
      SkoKXC4X73Sv1+t5E7jcbjfcbjfxMJhWq8XAwAAvj7Rz507e3IeJiQlIJBKihQapqamczxEL
      g8HAmzHtdruxsLBARfPPubNdu3bxNH/9+hUJCQlUQn/sU8eJEyfQ09ODpqYmzgyPNlatWoWq
      qirs3r07VgUUKTIzM+H3+4OOhZybm4NcLic6SpANBbB+KGwlBMMwWLt2LZRKJXciLi4uJnpS
      YX1fBgcHodFouMfiuLg4GAwGeL1edHV1ITU1FQaDgRgvsORz5PV64ff7eZVHiYmJ0Ol0GBsb
      Q39/PzZs2EB0RCHDMEhPT4fNZoNSqeTFYFUqFdatW4eenh7Y7XYUFBQQncImEom4oeDsKVSl
      UvEqjSwWC2QyGXJzc4nxAksLwszMDNLT07mFVqvVcguf3+9He3s7jEYj0Wl4IpEIXq8XQqGQ
      05yWlsbTbDabkZycjJycHGK8wNKBYnp6GhkZGTzN7HeN1VxQUEBtFCxrda3T6eDz+bhZG4WF
      hfTr/xlm6bcV6wP4d4MdLE17ilEo7pXofQgEAsusg6MBdmoTO8AjmnC5XBCJRFRm5IZCIBCA
      y+VCYmJi1K/3n6R5bm4Ok5OTyM7OjpWB/tvw4cMH6onZX4FhGHR0dKzIMJqFhQV0d3dHnRcA
      xsfHqZSghgPDMHj37l1UfFp+hlQqxatXr6LOKxAIEAgEVsR6Y6U1v337NmqccrkcOTk5EAgE
      GBwcjIq1TGwDIIRPnz6tiD8MsFS6uBIbwLdv3/DmzZuo8wJL8dmV2AAAoLe3l2oCPhRWYjEE
      lip2SPcBRIqV0uxwOFZM88jISFSM6GJ9ABHA5/OFrTyh4cbJ+s6Ei9LRiOIFAoGwm0okn+13
      8E+/3jSqkCLlpgF2FGMo0DhgsD5Wf5Jm9v/+U77fsQ0gAty8eRM2my1kI4rdbodWqyXK63Q6
      UV9fzxlDBQON8sTh4WFcuXIlpN2Bz+ejMjjl6dOn6OjoCPm/nU4nSktLiXNfvHgRdrs9pPXA
      zMwMSkpKiPLOzMzg4sWL3IDwYKARenr//j3u3LkT8l4vLi4SbwBkNYfLX5HuewCAd+/ewWQy
      hSypXVxc5FUkkUJLSwt6e3tDVjY5HA5UVFQQ5V1cXMSFCxfgdru5vF1sA4gAOp0ODocDdXV1
      Qd/T1tZGnFcqlUKtVkOhUIRc7C5cuECcm606qqysDOq3Mzc3hwcPHhDn3rhxI6xWK+rq6oL+
      SKxWK3E7BgDIy8vD0NAQDh48GPQ9JpOJOK9CoYBCoYBerw9ZVXXu3Dni3Fqtlhs/GszLamxs
      DF1dXUR5U1JSIJfLYTQaodfrg76Plub4+HgcOnRoWd8Bi9HRUZjNZuLceXl5GBkZwcmTJ4Mm
      e1+8eEGcVywWQ6PRwO/346+//gIQywFEBKPRCI/Hg5mZGchksl++SM/kBZYSUTt27EBvby8Y
      hgnKTaNiQCQSoaysDC9evIBUKv0lb2JiIpXKo6ysLGRkZGBwcDAoNw1LCLY2e3Z2Fk6nM+j1
      pmFNHBcXhz179uD169cQi8VBuWlcb6lUiq1bt8JsNge93uyYRpKIi4vD3r170dnZCYlEElXN
      MpkMRUVFITXTGguam5uL+Ph4jI+PB9VMo+qJYRhs27YNnz9/htfrXVo7iLP8H+JHS4BgYFvK
      SSM9PR16vR6jo6MhPx8N7vz8fKxatSpowpOWZoFAgN27d2N8fDxoLJRWGaZEIsHOnTtDJpjD
      hWl+FxqNBlqtNmRIj0ZJIsMwKCwshN/vD9rkRut6a7VaaDSaZWZ/P4KW5qKiIvh8vqhrFggE
      qKiowMDAQMjPR+M7plAoUFhYyFUYxfoAIkS4mnc2rkbjSSBczbvD4aDyJBCu5p1dMJKSkojy
      styh+hsWFxcRCASonNLC3WuXywWxWEzlScDv94dceNiGQ9IId719Ph++fftG3AIb+DM1h/uO
      LSwsgGEYKk+6P64nsQ0ghhhiiOEPxd/TGpqaMgrSAgAAAABJRU5ErkJggg==
    </thumbnail>
    <thumbnail height='384' name='GHG Target Reduction' width='184'>
      iVBORw0KGgoAAAANSUhEUgAAALgAAAGACAYAAAAebaizAAAACXBIWXMAAA7DAAAOwwHHb6hk
      AAAPYElEQVR4nO3b70+V9R/H8dfhiBAEQ0UURC3JogyzbjC941oyqn2xbOsP4FZ5I8fcWlt3
      6m6btrS5NlqrTe+1NacQprk1XStzs7I5d5ag/DDkLMyDjDPoHOB7g50TJ35m54rOq+fjljvX
      57yvM3nu4vpxCE1OTk4KMJW31B8ACBKBwxqBwxqBwxqBwxqBwxqBwxqBwxqBwxqBwxqBwxqB
      wxqBwxqBwxqBwxqBI1DxeHxJ90/gsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbg
      sEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbg
      sEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbg
      sEbgsLZsqT8A/pveOHrhH9kPR3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BY
      I3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BY
      I3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BY
      I3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI/AlMDo6qv7+fsXj
      8XueEYvFFIvF/vZnydacf6tlS/0B/iv6+vp0/vx5Xb9+Xb/88osmJyclSYWFhXrkkUe0fft2
      bd26VcuWzf0juXnzps6ePatr165pcHBQklReXq7NmzeroaFB1dXVi/os2ZqTC0KTqf9pBOaH
      H37Qxx9/rN9//z39Wjgc1vj4eMa6/fv3q7a2dtYZXV1dOnLkyJxH/aKiIu3bt0+bNm2a97Nk
      a85ixeNxFRUVzXj9jaMXsjJ/IRzBA3b69GkdP35ck5OT2rBhg3bt2qXNmzdr1apVGhoaUn9/
      v7799ltdunRJoVBo1hldXV06fPiwxsbGVFZWpj179mjLli2SpKtXr+r48eOKxWI6dOiQWlpa
      VFNTE+icXMIRPEDRaFRvv/22Jicn9dhjj2nv3r0qKCiYde3IyIiKiopmjfy9995TJBJRcXGx
      Xn/9dVVVVWVs7+/v18GDBzUyMqLa2lrt379/1n1ka85fsdRHcC4yA9TR0aHJyUlt2rRJr732
      2pxxS1JxcfGscUejUUUiEUlSc3PzjCglqaqqSs3NzZKkSCSiaDQa2JxcQ+ABGRwc1MWLFyVJ
      jY2NCofD9zTn3LlzkqSysjLV1dXNua6urk5lZWUZ7wliTq4h8ICcP39eExMTKi8v17Zt2+55
      zuXLlyVJ9fX1c56jS1IoFFJ9fX3Ge4KYk2sIPCADAwOSpB07dswb1EKGhoYkad6jbkpqTeo9
      QczJNQQekNT95T+f646Njam7u1u3bt2acZvwz+LxuBKJhCSlTxvmk1qTSCQybgNma04u4jZh
      QG7fvi1pKpaLFy/q+++/V19fn27fvp1+yBMOh1VVVaXdu3friSeemDFj+hG0tLR0wX1OXzM0
      NJS+e5GtOdP9lfCHh4cXvTbbCDwAIyMjGh0dlSQdOHBAExMTGdsLCws1Ojqq8fFx9fX16YMP
      PtC2bdv06quvKi/vj1+qqUfo+fn5KiwsXHC/hYWFys/PVyKRUCwWU2VlZVbn5CICD8D073ZM
      TEyourpa9fX1evDBB7Vu3ToVFxcrHo+rs7NTn332mQYGBvTjjz+qo6NDTU1N6femTmH+yh2Y
      cDisRCKRcfqTrTnTzXZUn008HldJScmi95ttBB6A+++/P/3vuR6/FxUVaevWrdqyZYvef/99
      RSIRdXR0aMeOHVq1apWkP04VRkdHlUwm5/2eijR1zpz6zTH9NCNbc3IRF5kBKC0tVX5+viTp
      vvvum3dtOBxWc3Ozli9frvHxcV2/fj29bfoF4d27dxfc7/Rz3envzdacXETgAQiFQlq5cqWk
      P+6mzGfFihXauHGjpKlv+qWUlJSkz8kXE2ZqTV5eXsZpQbbm5CICD0h5ebkkqaenZ1HrV6xY
      ISkzwFAolD5FuHHjxoIzUmtKS0sz7r1na04uIvCApO48fP311+l70PNJnRasXbs24/XU11Yv
      XFj4y0mpNbN91TVbc3INgQfk6aefVl5enkZGRvTdd9/Nu3ZsbEy9vb2SZj4Y2rlzpySpu7t7
      3i8/DQwMqLu7O+M9QczJNQQekNWrV2v79u2SpLa2NvX398+59vPPP9fIyIgKCgr0wAMPZGyr
      ra1VRUWFJOno0aOz/jZIJBI6duyYJKmiomLWuzbZmpNrCDxA//vf/xQOhxWLxXTgwAH9/PPP
      GduTyaROnDihL7/8UpL0wgsvzLioC4VCamhokCR1dnaqtbV1xmP41tZWdXZ2SpIaGhpmPW/O
      1pxcwx88BCwSieijjz5Kn2OXlpaqpqZGyWRSPT096YvK2tpatbS0ZDzJnK6trU3t7e2Spu5u
      VFdXKy8vT729veknpU1NTdq9e/e8nydbcxZrqf/ggcD/AXfu3NGHH36YcY87JT8/Xy+++OKi
      jphnzpxRe3u7xsbGMl4vKChQU1OTGhsbF/V5sjVnMQj8P2R4eFg3btxQb2+vCgsLtX79eq1f
      v37Rj72lqUf/PT096urqkiTV1NRo48aNcx75g56zEAKHtaUOnItMWCNwWCNwWCNwWCNwWCNw
      WCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNw
      WCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNw
      WCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNw
      WCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNw
      WCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNw
      WCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNw
      WCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCPwHBWLxRSLxf41c/6tli31
      B3A2MTGhTz75ZNHrn3zyST311FNzbr9586bOnj2ra9euaXBwUJJUXl6uzZs3q6GhQdXV1Yva
      T7bm5AICD9Cvv/6qixcvLnp9RUXFnIF3dXXpyJEjisfjGa8PDg5qcHBQly9f1r59+7Rp06Z5
      95GtObmCwAMUjUYlSYWFhXr++ecXXP/QQw/N+npXV5cOHz6ssbExlZWVac+ePdqyZYsk6erV
      qzp+/LhisZgOHTqklpYW1dTUBDonlxB4gAYGBiRJ69at03PPPXfPc06ePKmxsTEVFxerpaVF
      VVVV6W3bt2/Xhg0bdPDgQY2MjOjkyZPav39/oHNyCReZAUoFvnbt2nueEY1GFYlEJEnNzc0Z
      UaZUVVWpublZkhSJRNK/OYKYk2sIPECpQP5O4OfOnZMklZWVqa6ubs51dXV1Kisry3hPEHNy
      DYEHKBuBX758WZJUX1+vUCg057pQKKT6+vqM9wQxJ9cQeEDi8biGh4cl/b3Ah4aGJGneo25K
      ak3qPUHMyTUEHpDU+feyZctUXl5+TzPi8bgSiYQkpU8b5pNak0gkMm4DZmtOLuIuSkBSgY+P
      j+udd95Jv56Xl6eSkhKtW7dOVVVVevzxx1VUVDTrjOlH0NLS0gX3OX3N0NBQem625kz3V8JP
      /SZbCgQekJGREUnS5OSkenp6Zmz/6aefJElFRUXatWuXGhsbtXz58ow1qUfo+fn5KiwsXHCf
      hYWFys/PVyKRUCwWU2VlZVbn5CICD8gzzzyj1atXa3JyMv3axMSE7t69qzt37qi3t1eRSETx
      eFxtbW3q7+/XK6+8kjFjfHxckhQOhxe933A4rEQikX5vNudMN9dvnT+Lx+MqKSlZ9H6zjcAD
      Eg6HtW3btnnXRKNRffrpp7py5YouXbqk06dP69lnn01vT50qjI6OKplMatmy+X9ciURCo6Oj
      Ge/N5pxcxEXmElqzZo327t2b/t7HN998k7F9+gXh3bt3F5w3/Vx3+nuzNScXEfgSy8/PTx+1
      o9GoxsbG0ttKSkqUlzf1I1pMmKk1qQvZbM/JRQT+L7B+/XpJUxekqbsv0tRDl9Qpwo0bNxac
      k1pTWlqa8TAnW3NyEYH/C0w/JVi5cmXGttTpy4ULFxack1oz21ddszUn1xB4QP7Kvd8rV65I
      klasWDHjlGDnzp2SpO7u7nm//DQwMKDu7u6M9wQxJ9cQeAAGBwf15ptv6sSJExnn1LPp7e3V
      qVOnJEkPP/zwjO21tbWqqKiQJB09ejT9RHK6RCKhY8eOSZr6o4na2trA5uQaAg9IIpFQR0eH
      3nrrLX311Vfq6+tTMplMb08mkzpz5ozeffddJZNJlZaW6uWXX54xJxQKqaGhQZLU2dmp1tbW
      GY/hW1tb1dnZKUlqaGiY9bw5W3NyTWhy+pMIZEUymdSpU6d09uzZ9P1kaep7KZWVlUomk4pG
      o5qYmJA09eRw7969evTRR+ec2dbWpvb2dklTdzeqq6uVl5en3t7e9Jympibt3r173s+WrTmL
      FY/HZ30o9MbRha8FsoHAAzQ8PKwvvvhCP/zwg3777Tf9+b86FAppx44deumllxb1QOXMmTNq
      b2+fcdpTUFCgpqYmNTY2LupzZWvOYhD4f8TY2Jhu3bqlW7duKRwOq7KyUmvWrJnx/ZOFTExM
      qKenR11dXZKkmpoabdy4MX2f+5+esxACh7WlDpyLTFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgj
      cFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgj
      cFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgj
      cFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgj
      cFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFj7PxUw
      GzKSPeKVAAAAAElFTkSuQmCC
    </thumbnail>
    <thumbnail height='384' name='Household Vehicle Cost' width='384'>
      iVBORw0KGgoAAAANSUhEUgAAAYAAAAGACAYAAACkx7W/AAAACXBIWXMAAA7DAAAOwwHHb6hk
      AAAgAElEQVR4nO3deXAc5Z0//nf3HJqRRqP7tGyEbEu+LRt8YIPNaQjGTkK4dn8FwRRmwyYc
      taR2syzFpjZUCFBJfXdZ7nDHSwy2ZXzbHLaF7wufOizJlmTd0uice6b7+f3huIN8wAxoNDPu
      96vKBX145jPjZ/rd/fTT3ZIQQoCIiHRHjnYBREQUHQwAIiKdYgAQEekUA4CISKcuywBQVTXa
      JRARxTwGABGRTl2WAUBERN+NAUBEpFMMACIinWIAEBHpFAOAiEinGABERDrFACAi0qmoB0Bv
      by8GBgYumO/3+9He3o5v3qxUCIHm5mZUVlbC5XINZ5lERJcdKVq3gx4YGMC2bduwZ88eLF68
      GLNmzQIABINBHDp0CNu2bUNqaiqWLl0KSZIAAFu3bkVFRQUKCgpQU1ODpUuXIiUl5YLXDgaD
      MBqNw/p5iIjiTdSOAE6cOIHU1FTMnz9/0Pzu7m40NjbinnvuGXRFr9PpxKFDh7BkyRIsXrwY
      s2fPxo4dO4a7bCKiy0bUdpNnz54NANi+ffug+dnZ2bjrrrvQ398/aL7D4UBubi4SExMBAFOn
      TsX777+Pix3ACCF4Owgiou8QN/0kTqcTycnJ2rTNZoPH44GqqpAkCYFAQFsmhICiKNEok4go
      bsRNAFgsFvh8Pm3a7/fDZDJBkiTIsoyEhARtGc8BEBF9t6iPAgpVamoq2tratK6d+vp6ZGdn
      ayeIiYgoPFHbTW5qaoLH40FnZydMJhNqamqQk5MDVVXR2dkJt9sNl8uF2tpa2Gw25ObmIiUl
      BVu2bMHYsWOxdu1a3H333QwAIqLvKWrDQPft24fu7u5B86ZOnQq/34/KyspB83Nzc1FaWgqP
      x4OvvvoK3d3dKC0tRUlJyUUDgF1ARETfLWoBEEkMACKi78atJBFRiCrO9KDb6Y12GchItmB8
      QdoPfh0GABFRiPacbEdVc2+0y8DEkWlDEgBxMwqIiIiGFgOAiEinGABERDrFACAi0ikGABGR
      TjEAiIh0igFARKRTDAAiIp1iABAR6RQDgIhIpxgAREQ6xQAgItIpBgARkU4xAIiIdIoBQESk
      UwwAIiKdYgAQEekUA4CISKcYAEREOsUAICLSqZgNAEVRoKrqBfOFEAgGg1GoiIjo8mKM1hsL
      IVBbW4v169fjuuuuw1VXXQUAUFUVX375JQ4fPgwhBK677jrMmjULkiShrq4Oq1evhiRJyM7O
      xr333guTyRStj0BEFNeiFgC7du1CZWUlcnNzB+3RNzU1oaqqCr/61a+gqipefvlllJSUwGaz
      Yc2aNbjvvvuQk5ODsrIy7N+/H3PmzInWRyAiimtR6wKaPn06HnroIeTl5Q2aX1FRgVmzZsFi
      sSAxMRHTp09HVVUVOjo6YLfbkZ+fD6PRiPnz5+PYsWMQQkTpExARxbeoHQFYrdaLzu/t7cW4
      ceO06aysLLS2tiItLQ0ZGRmQJAkAkJGRAafTCVVVIUkS/H6/9neEEIOmiYiGgqoq0S4BwNlz
      pG63+we/TtQC4FIkSRq0Vy+EgCRJF51/bn1ZlmGxWLRlwWAQRmPMfTQiinOybIh2CQAAg8GA
      xMTEH/w6MTcKKC0tDe3t7dp0e3s70tPTYbfb0dXVpc3v7OyE3W7XjgiIiCg8MRcAU6ZMwa5d
      u+B2uzEwMICDBw9iwoQJyM7OhtvtxunTp6EoCrZs2YKrrrqKAUBE9D1FrZ+krKwMzc3NUJSz
      fWr79+/HggULMHbsWMyZMwevv/46AGDx4sVISkqCJEm4++67sWrVKvj9fowZMwalpaXRKp+I
      KO5JIgaH0Zy72EuSJBgMhkF7+aqqIhgMwmQyXXLvn+cAiCgS3vmiClXNvdEuAxNHpuHnN5T8
      4NeJya2kJEmXvMBLlmWYzeZhroiI6PITc+cAiIhoeDAAiIh0igFARKRTDAAiIp1iABAR6RQD
      gIhIpxgAREQ6xQAgItIpBgARkU4xAIiIdIoBQESkUwwAIiKdYgAQEekUA4CISKcYAEREOsUA
      ICLSKQYAEZFOMQCIiHSKAUBEpFMMACIinWIAEBHplDHaBZxPCIHu7m5UVVUhOTkZEydOhMFg
      AAAoioLa2lo4HA6MHz8eqampkCQpyhUTEcWnmDsCqK+vx7vvvgsAqKysxAcffABVVQEAGzZs
      wM6dOxEMBvHqq6/C4XBEs1QiorgWc0cAO3fuxM9+9jNceeWVEELg9ddfR3NzM+x2O2pqavDY
      Y4/BaDQiKSkJ5eXluPPOO6NdMhFRXIq5APD7/TCZTAAASZIwYcIEnDlzBvn5+cjLy9OWjR8/
      Hjt37oQQAgC0o4Rz/68oyvAXT0SXt79tb6JNCDEk27iYC4DS0lKsWrUK8+bNg8/nw+HDhzFh
      wgS4XC4kJSVp61mtVvh8PqiqClmWLwgAESP/UER0+VBjZLsihEAwGPzBrxOTAWC321FXVweb
      zYaCggKkpqYiMTERHo9HW8/r9cJsNkOWZUiSpB0ZAGePHIzGmPtoRBTnZDk2TpvKsoyEhIQf
      /jpDUMuQkmUZY8aMwa233oprrrkGra2tuOKKK5CWloa2tjYEg0EIIVBTU4P8/HyOAiIi+p5i
      bje5ubkZJ0+eRE5ODo4cOYKcnBxkZWUBAPLy8rBy5UqMHTsWmzZtwkMPPRTlaomI4lfMBUB6
      ejrMZjPq6uowYcIETJkyRdvLv/POO/H111+jo6MDS5YsQW5ubpSrJSKKXzEXAFarFXPnzr3o
      MqPRiBkzZgxzRUREl6eYOwdARETDgwFARKRTDAAiIp1iABAR6RQDgIhIpxgAREQ6xQAgItIp
      BgARkU4xAIiIdIoBQESkUwwAIiKdYgAQEekUA4CISKcYAEREOsUAICLSKQYAEZFOMQCIiHSK
      AUBEpFMMACIinWIAEBHpFAOAiEinjNEu4GICgQD6+voAAGlpaTAYDAAAIQQGBgbg8XiQlpYG
      s9kczTKJiOJazAVAX18fPvzwQ1itVqiqCkVR8OCDD8JiseDgwYMoLy9HWloaXC4XHnroISQm
      Jka7ZCKiuBRzAbB//35MmDABN954IwBg1apVOH78OCZOnIht27Zh6dKlsNvt+Oyzz7Br1y7c
      fPPNUa6YiCg+xdw5gIyMDHR2dkJRFCiKAofDgfT0dHR1dSE7OxspKSmQJAkzZ85EdXU1hBDR
      LpmIKC7F3BHAlClTcOzYMfzud79DMBjE1VdfjSuvvBKVlZVITU3V1rPb7fB4PFBVFZIkwe/3
      a8uEEIOmiYiGgqoq0S4BAKAoCtxu9w9+nZgLgK+//hpmsxlPPfUUVFXFihUrUFNTA7PZPGij
      HgwGYTAYIEkSZFmGxWIZtMxojLmPRkRxTpYN0S4BAGAwGIbk/GfMdQEdP34c11xzDZKTk5GS
      koKZM2fixIkTsNvt6Ojo0Lp8mpubkZ6eDkmSolwxEVF8irkAKCwsRHl5ORwOB7q6urBjxw4U
      FhYiMzMTRqMR+/fvR3d3NzZs2IBZs2YxAIiIvqeY6ye59tprsWfPHqxevRqSJGHGjBmYOnUq
      ZFnGvffeiw0bNuDQoUO46qqrUFJSEu1yiYjiliQuw2E0PAdARJHwzhdVqGrujXYZmDgyDT+/
      4YfvAMdcFxAREQ0PBgARkU4xAIiIdIoBQESkUwwAIiKdYgAQEekUA4CISKcYAEREOsUAICLS
      KQYAEZFOMQCIiHSKAUBEpFMMACIinWIAEBHpFAOAiEinGABERDrFACAi0ikGABGRTjEAiIh0
      igFARKRTYQWAz+dDQ0MDAKCtrQ0rVqxAXV1dRAojIqLICisAtm7dirKyMggh8Mwzz6CiogLP
      Pvssuru7I1UfERFFSFgBUF9fj5KSEpw5cwaBQAC/+c1vMHr0aDQ3N0eqvgsIIaCq6rC9HxHR
      5coYzsq5ubnYt28fTpw4gauuugpGoxEDAwNISkoasoLKy8tRU1OjTfv9fkybNg2zZ89Ga2sr
      1qxZA6/Xi7Fjx+LWW2+FwWAYsvcmItKTsALglltuQU1NDXp6evDkk0+is7MT6enpGDly5JAV
      dPXVV2PKlCna9IcffoiMjAwoioIVK1ZgwYIFuOKKK/DRRx/h6NGjmDZt2pC9NxGRnoQUAEII
      CCFgtVrx1FNPDVr2H//xH5AkacgKSkxMRGJiIoCzJ5pVVUVRURE6OjqQkJCA4uJiSJKEBQsW
      YOPGjSgtLR3S9yci0ouQAuCll17C6dOnL7osISEBTz/9NLKzs4e0MCEENm/ejFtuuQUGgwH9
      /f3IysrSNvY5OTno6+vTwikQCGh/V1XVQdNEofD4FZzudEa7DABAUbYNFhO7N2ONqirRLgEA
      oCgKPB7PD36dkAJg0aJFcDqd2L9/PxwOB2677TYAgMfjwUcffaTtsQ+lzs5OdHV1Yfz48QDO
      BsI39/QlSdI2/rIsw2w2a8sUReG5AQqbw+XC8l310S4DAPDkHZORmmyJdhl0HlmOje2KwWCA
      xfLD20dIAXBuI3zo0CGMGzcOM2bMAHB2o7x69Wo0NTVh3LhxP7iYc4QQ+OKLL3DTTTdp85KT
      kwcNN+3u7kZycjJkWb5oFxC7hShsMdZk2Ibp2wxF+whrGGhhYSHWrl2LhoYGDAwM4Pjx46it
      rR3yve2uri60tLRg8uTJ2ofMyspCX1+fdl6gvLwckyZN4o+EiOh7CmsU0A033IDq6mo8+eST
      MBqN8Pl8WLx4MYqKioa0qMOHD2P+/PmDgsVsNmPhwoVYtmwZzGYz7HY7Zs6cOaTvS0SkJ2EF
      gN/vxwMPPIAHH3wQAwMDsNvtsNlsQ74Xfsstt1x0fnFxMR5//HF4vd6IvC8RkZ6EFQAbNmxA
      eXk5Xn75Zdjt9kjV9K1MJhNMJlNU3puI6HIS1jmA22+/HbIs4/PPP4cQIlI1ERHRMAjrCODQ
      oUPw+/34r//6L5SVlcFgMMBsNuM3v/kNsrKyIlUjERFFQFgBMHbsWNx///2D5hkMBthstiEt
      ioiIIi+sAMjLy0NqaioqKyvR29uLK664AoWFhbzoiogoDoUVAA6HA//2b/8Gr9eLrKwsNDQ0
      4LrrrsPjjz/OECAiijNhBcCqVatQXFyMp556CgaDAb29vfjlL3+JmpqaIb0SmIiIIi+sUUDd
      3d0oKSnR9vZTUlKQmZmJgYGBiBRHRESRE9YRwI033ojf/e53EEIgOzsbBw8eREdHB4qLiyNV
      HxERRUhYAXD11VfjmWeeQVlZGXp6ejB27Fj88Y9/REpKSqTqIyKiCAkrAPr6+pCamornn38e
      wNlbQ1RUVCAnJ4cngYmI4kxY5wDWrFmD3bt3a9MGgwGvvfbasD4UnoiIhkZYAdDX1zfoAfDn
      HsridruHvDAiIoqssLqA5s+fj2eeeQYWiwX5+fn46quv4Pf7MWrUqEjVR0REERJWAEyePBnP
      PPMM/vrXv6K7uxvjx4/HCy+8EJFHQhIRUWSFFQCSJGHGjBkoKChAIBBAXl4enM7YeIg2ERGF
      J6xzAD09PXjiiSfwL//yL3j++efhcrnw7LPPwuVyRao+IiKKkLACYMWKFSguLsbbb78NWZZh
      t9uRmJiIpqamSNVHREQREvYooJKSEm0kkKIo6OnpiUhhREQUWWGdA7j55pvx3HPPoaWlBZ2d
      nfj9738PVVU5CoiIKA6FdQQwdepUPPfcc3A4HJg4cSIKCwvx0ksvwWKxRKo+IiKKkLCOAIQQ
      KC4uHnTr5wMHDqCwsBCZmZlDXhwREUVOSEcAiqLgjTfewC233IIf/ehH+PTTTyGEwGeffYY/
      /OEPQ16UEALt7e04fvw4mpuboaqqNr+lpQVVVVUceURE9AOFdARQXV2N9evX47//+7+hKAr+
      /d//HQ6HA+vXr8d//ud/IiMjY8gKOhcsp06dwhVXXIF9+/bhpz/9KdLS0rB9+3acOHECI0aM
      wLp167B06VLeiZSI6HsKKQBOnz6N6dOnY9KkSRBC4KqrrsKaNWvw4osvDvmzANra2lBbW4ul
      S5fCZDJBCAEAcDqdOHjwIB599FFYrVbs2LEDO3bswMKFC4f0/YmI9CKkAFAUBUajUbvqNyEh
      Affccw/y8/PhcrlgtVohy2GdT76k6upqTJ06FR6PB729vUhJSYHZbIbD4UBubq5224nS0lK8
      //77WkB8kxBC6zYiCtXF2lK0sA3TdxmK9hFSAMiyjC+++AJ79+4FcHZvfPv27fjoo49gsVjw
      P//zP8jLy/vBxQBnHztZXV2NiooKyLKMrq4uLF26FE6nE8nJydp6NpsNHo8HqqpCkiQEAgFt
      maqqUBRlSOoh/fD7A9+90jDxBwLw+/3RLoPOEyuhrKpqyO3D4w/iox2nL7ospAC48cYbMX36
      9IsukyQJWVlZIRUSCrPZjDvuuAOTJ08GAOzZswfl5eWYPHkyfD6ftp7f74fJZIIkSZBlGQkJ
      CdqyYDAIozGsAU5ESEgIRrsETYLZzOHVMWioejp+KFmWQ24fARFAfdfFB82EtJVMTEwctjt+
      5uXloa6uTguAzMxMNDY2IjU1FW1tbVBVFbIso76+HllZWZAkaVjqIiK63MRGnH3DhAkTcPr0
      aezbtw9NTU3YsmULJk+ejPT0dKSkpGgjhNatW4e5c+cyAIiIvqeQAmDdunVwOp04ceIEjh49
      GtGCrFYrlixZgvb2dnz11Ve49tprMW7cOEiShLvuugsAsHfvXtx+++244oorIloLEdHlLKQu
      oLKyMkyaNAknTpyAz+fDlClTIlpUamoqFi1adMH8xMRE3HrrrRF9byIivQgpAObMmYN//dd/
      haIoCAaDKC8v15YlJCTg2WefRXZ2dsSKJCKioRdSACxZsgRz5szBunXrEAwGcdNNN2nLzj0X
      gIiI4kvI1wGMHz8emZmZUFUVOTk5ka6LiIgiLKxRQGlpadiyZQt+/OMf4/rrr8dDDz2E/fv3
      R6o2IiKKoLACYMuWLfjyyy/x0ksvYdWqVfjHf/xH/OEPf0BbW1uk6iMioggJKwCOHDmCn/3s
      ZyguLkZ6ejpuuukmFBYWorGxMVL1ERFRhIQVAEVFRdi2bRscDgfcbjeqq6vR2NjIcwJERHEo
      rBvmLF68GCdPnsT999+PpKQk+Hw+PPTQQ3wmMBFRHAorAKxWK5555hn09fWhv78fOTk5g27C
      RkRE8SPsW2ZKkoTU1FSkpqZGoh4iIhomMXczOCIiGh5hBcCnn36KF154IVK1EBHRMAorAKZN
      m4ZDhw6hrq4uph6fR0RE4QvrHIDb7ca0adPw61//GosXL4bBYIDRaMQdd9zB+wEREcWZsALA
      5/PBYrFg/vz56OvrAwCYTKaYeU4mERGFLqwAmDp1KqZOnQpFUeDz+WC1WvlELiKiOBXWOQCX
      y4XnnnsOt99+O5544gl0dnbiV7/6Fbxeb6TqIyKiCAkrAFauXAkAWL58OUwmEzIyMmC1WnHm
      zJmIFEdERJETVgC0trZi7ty52kVgQgg4nU4Eg8GIFEdERJET1jmAa6+9Fq+99hr8fj/6+vrw
      5ptvor+/HyNHjoxUfUREFCFhBcA111wDIQTWrl2L5ORkDAwM4Pe//z1sNluk6iMioggJKwAk
      ScKcOXNQWFgIr9eLESNGwGKxRKo2IiKKoLACoLOzE0899RTa29uRlpaGnp4e/OIXv8BPf/rT
      YRsOKoSAoigwGsO+jx0REX1DWFvRsrIyFBYW4q233oLRaMTJkyfx9NNPY/bs2cjPzx+SgoLB
      IN56661BF5fdeeedyMvLQ11dHVavXg1JkpCTk4N77rkHJpNpSN6XiEhvwgoAi8WC0tJSrdtn
      /PjxGDVqFJxO55AV5Ha7oaoqHn74YW2eyWRCIBDAmjVrcN999yEnJwdlZWU4cOAArrnmmiF7
      byIiPQkpALZu3Yru7m4EAgFs2LBh0DKn04nc3NwhK8jpdCI5OfmCB820trbCbrcjPz8fkiRh
      /vz5KCsrw+zZs3k1MhHR9xBSADQ0NKC5uRnA2ecCV1VVacsmTpw4pHcG7evrQ2NjI958801Y
      rVZMnz4dEyZMwMDAADIyMrSNfUZGBpxOJ1RVhSRJ8Pv92msIIQZNE4XC6/VFuwSN1+eD280d
      m1ijqkq0SwAAKIoCt9sd0rpe36Wv0wopAB588MGQ3mgoFBcX44knnoCqquju7sYnn3wCi8UC
      SZIGBc25/5ckCbIsDxqNFAwGeZKYwmbxxM5NDS0JCUhMTIx2GXQeWTZEuwQAgMFgCLl9KFLg
      ksvC2kp2dHTgtdde0+4ECgBmsxm//vWvkZmZGc5LXZLBYEBycjIAICUlBddddx1OnTqFSZMm
      oaurS1uvs7MTdrud3T9ERN9TWAGwYsUKOJ1OLFq0SJtnMBiQlJQ0ZAUdOHAAKSkpKCoqQiAQ
      wKFDh3DzzTcjOzsbbrcbp0+fxqhRo7BlyxZMnz6dAUBE9D2FFQAZGRmw2+244YYbIlUPCgoK
      sGXLFqxfvx5CCEybNg1jx46FLMu46667UFZWBr/fj9GjR6O0tDRidRARXe7CCoDrr78eTz/9
      9KD+dqPRiJtvvnnIbgeRm5uL+++/H8FgEJIkwWAwaHv5I0aMwKOPPopgMAiz2cy9fyKiHyCs
      ANi6dSv6+/tx8uRJbZ7JZML8+fOHtChJki55gZfBYIDBEBsnYoiI4llYAdDf34+7774b99xz
      T6TqISKiYRLW8wDmzp2LAwcOoKmpSfvT3NzM5wEQEcWhsI4AGhoaUFNTg8cee0ybZ7FY8Kc/
      /Ql5eXlDXhwREUVOWAFw2223YcGCBYPmnbsQi4iI4ktYAdDY2Ijjx48PmmcwGDBv3rwhvRaA
      iIgiL6wAaG9vx759+7Rpt9uNhoYGzJw5kwFARBRnwgqAWbNmYdasWdq03+/Hk08+yfH4RERx
      KKzOe0VR4Pf7tT+qqsLv96OpqSlS9RERUYSEdQSwZcsWvPXWW9p0IBCAyWTCiBEjhrwwIiKK
      rLAC4LrrrsOUKVO0aUmSkJmZCbPZPOSFERFRZIUUAF6vFy6XCwAG3QcIOPsEr5SUFN6egYgo
      zoQUALt378Zrr712wfxgMAiv14u33nqL3UBERHEmpACYO3cupk+frk0rioIjR47gww8/hNFo
      5JOLiIjiUEgBYDabYTabEQwGcezYMXzwwQcYGBjAAw88gDlz5vAcABFRHAopAFRVxZEjR/D+
      +++jt7cXDz74IK699lo+d5eIKI6FtAX/4osv8Nvf/haLFy/GvffeC1mWcfDgQQCALMuYPHny
      BSeHiYgotoUUAMnJyZg2bRrOnDmDZcuWDVqWkJCAwsJCBgARUZwJKQDOvwUEERHFv5ACgPf6
      ISK6/PBG/kREOsUAICLSqZgNACEEGhoaBt1pVFVVVFdXY/fu3ejp6YEQIooVEhHFt5gNgNbW
      Vrz33nv44osvtHnr16/Hjh074Pf78eqrr8LhcESxQiKi+BaTARAIBLB69WosWrRIm9ff34+a
      mho88MADmDdvHm677TaUl5dHsUoiovgWk5fy7ty5E0VFRcjPz8exY8cAAN3d3cjLy4PJZAIA
      jB8/Hjt37tS6gVRV1f6+qqpQFGX4C6e4pirqd680TNiGY1VsdDsLIUJuH6p66fViLgDa29tx
      4sQJPPzww+jp6dHmu1yuQc8dtlqt8Pl8UFUVsixfEAA8P0DhiqUNrqIoCAaD0S6DzqOqsbFd
      EUKE3D6UYJwEgKqqWLVqFfLy8lBRUYHe3l44HA5UVVXBYrHA4/Fo63q9XpjNZsiyDEmStCMD
      4Ox1C7xPEYXLZA5EuwSNyWRCQkJCtMug88hybPSay7Iccvvwq5euOTY+zTdcffXVyMrKgtPp
      hNvtRiAQgNvtRlpaGtra2hAMBiGEQG1tLfLy8niRGhHR9xRTu8myLGPGjBnadFtbG7q6ujB9
      +nQIIZCXl4eVK1eiuLgYGzduxJIlS6JYLRFRfIu5I4BvSklJwbx58wCc7da58847ceWVV6Kt
      rQ0PPvggcnNzo1whEVH8iqkjgPNZrVZceeWV2rTRaMTMmTOjWBER0eUjpgOAiL7b8cZuNHQO
      RLsMjC9IQ1GOPdplUBgYAERxrqalD7tPtke7DNgsJgZAnInpcwBERBQ5DAAiIp1iABAR6RQD
      gIhIpxgAREQ6xQAgItIpBgARkU4xAIiIdIoBQESkUwwAIiKdYgAQEekUA4CISKcYAEREOsUA
      ICLSKQYAEZFOMQCIiHSKAUBEpFMMACIinWIAEBHpFAOAiEinYu6h8EIIBINBeL1eSJIEq9UK
      g8GgLfN6vfD7/UhKSoLRGHPlExHFjZjbgtbW1mLNmjWwWq1QFAVmsxk///nPYbFYcPz4cWza
      tAk2mw2SJGHJkiVISEiIdslERHEp5gIgOzsb//zP/wyr1QoAWLlyJY4fP47Jkydj8+bNePjh
      h5GamoqNGzdi165duOGGG6JcMRFRfIq5cwApKSnaxt/n86GtrQ3Z2dno6OhAVlYWUlNTIUkS
      Zs+ejcrKSggholwxEVF8irkjAAD4+uuvceDAAbS0tODWW2/FyJEjUVVVpW38ASA1NRVutxuq
      qkKSJPj9fu3vCyEGTROFwuv1RbsEjdfng9sthbRuUAlGuJrQBAIBuN3uaJcRUaqqRLsEAICi
      KCF/117fpdtHTAbA6NGjYbfb0dDQgH379qGoqAhGoxHB4N8/iKIokGUZkiRBlmVYLBZtWTAY
      5AliCpvFo0a7BI0lIQGJiYkhrWs0xEZbN5lMIdccr2TZEO0SAAAGgyHk71qRApdcFhst5zx2
      ux12ux2jR49GcnIy9u7di5kzZ6KzsxNCCEiShJaWFqSlpWlHBEREFJ6YOwewe/du1NXVwev1
      wuVyobq6GhkZGcjKyoKqqjh+/DhcLhc2bdqEGTNmMACIiL6nmDsCyMvLw44dO9Dd3Q2j0Yhx
      48Zh1qxZkGUZ9913H1avXo0vvvgC06ZNw8SJE6NdLhFR3Iq5ACgsLERhYeFFl8iF660AABZB
      SURBVKWnp+Ohhx4a3oKIiC5TMdcFREREw4MBQESkUwwAIiKdYgAQEekUA4CISKcYAEREOsUA
      ICLSKQYAEZFOMQCIiHSKAUBEpFMMACIinWIAEBHpFAOAiEinGABERDrFACAi0ikGABGRTjEA
      iIh0igFARKRTDAAiIp1iABAR6RQDgIhIp2I2AIQQEEJcdL6qqlGoiIjo8mKMdgHn8/l82LVr
      F2pra+HxeDBq1CgsXLgQJpMJra2tWLt2LTweD4qLi7FgwQIYDIZol0xEFJdi7gigsrISLpcL
      9913Hx555BG43W4cPXoUiqJgxYoVmDdvHv7pn/4JbW1tOHr0aLTLJSKKWzEXAJMnT8bChQuR
      nJyMhIQEZGVlwePxoKOjAwkJCSgpKYHFYsGCBQtw8ODBi3YTERHRd4u5LqBzXTpCCOzevRuH
      Dx/GL3/5S5w5cwaZmZmQJAkAkJOTg76+Pu1cQSAQ0F5DVdVB00Sh8Pl80S5B4/P54PGEtn8W
      VIIRriY0gUAAHo8n2mVElKoq0S4BAKAoSsjftdd76fYRcwEAAC6XC8uXL4fRaMRjjz0Gi8UC
      VVUhy3//QUiSpG38ZVmG2WzWlimKwnMDFDazOTZ+3ACQkJAAi8US0rpGQ2z8jE0mU8g1xytZ
      jo3tisFgCPm7DuLSO8Ox0XK+wefz4f3338fkyZMxd+5cbaNvt9vR3d2trdfd3Y3k5GTIsqwd
      FXzTxeYRfZtYazLx2IbjseZ4Ffp3fen1Yu4cwIkTJ2Cz2XDNNddAVVUEg0GoqoqsrCz09fWh
      ra0Nqqriq6++wqRJk9jgiIi+p5g7AnA4HGhubsYrr7yizZs1axZmz56N22+/HcuWLUNCQgJs
      NhvuuOOOKFZKRBTfYi4AbrnlFtxyyy0XXVZSUoKioiJ4vV7YbDbu/RMR/QAxFwDfxWQywWQy
      RbuMYSOEQKwMdJXDCNxYqVsC+6VjUay0D0DfbSTuAkBvdla1Yc3+hmiXAbNRxnP/ODPk9f/f
      umNo7XFHsKLQzB2Xix/PLIx2GXSe4409+HD7yWiXAQD47b1XIzFBn5vCmDsJTEREw4MBQESk
      UwwAIiKdYgAQEekUA4CISKcYAEREOsUAICLSKQYAEZFOMQCIiHSKAUBEpFMMACIinWIAEBHp
      FAOAiEinGABERDrFACAi0ikGABGRTjEAiIh0SjePwRFCwBdQol0GAMBklGGQmb1EFF26CQB/
      UMWzfz0Q7TIAAP/fvLGYWpgR7TKISOe4G0pEpFMMACIinYrJLiAhBM6cOYPOzk5Mnz4dkiQB
      ALxeL3bv3g2Hw4HS0lKMHj1aW0ZEROGJuSMAl8uFZcuWYcWKFdi/f782XwiBlStXwu12o7S0
      FGVlZThz5kwUKyUiim8xFwDBYBBTp07FkiVLBs3v7u6Gw+HAbbfdhjFjxuCOO+7Ajh07olQl
      EVH8i7kuoJSUFEyePBn9/f2D5vf19SE3NxcGgwEAUFRUhM2bN0MIAQBQlL8P8VQUBaqqDvr7
      geDg6WhSgkH4/f7Q1lViY+gqgJBrBqD9u0Sbqioh1x0MBiNcTeiCYbSR89t6tChK6N+1osTQ
      dx0IwC+F9h3GSrsWQgxJu465ALgUj8cDi8WiTZvNZgQCAaiqCvm8MfWSJF1wbiCmzhVcpL5v
      WTmipYQjpr7DkMXndx1WG4mRsi/2u/u2dWNGWL/H2BHyd/0tDSRuAsBms8HpdGrTLpcLVqsV
      sixDkiQYjX//KMFgcNA0AKiInT1pg8EAk8kU4rqx00sXas1A7PzAZVkOuW6j0RDhakJnDKON
      yFJstJFwvmtZjqHv2miEyRTapjBW2rUkSaFvQ77lYCs2Wk4IMjIy0NbWBq/XCyEEjh49isLC
      wpj5ByEiijcxdwRQV1eHo0ePwu/3o6WlBatXr8aIESMwY8YMTJkyBe+++y5GjhyJiooKPPLI
      I9Eul4gobsVcAOTk5GDGjBkAgLlz5wIArFYrJEnCjTfeiOLiYvT29mL+/PlITk6OZqlERHEt
      5gLAZrPBZrNddJksyxg1ahRGjRo1zFUREV1+4uYcABERDS0GABGRTjEAiIh0igFARKRTDAAi
      Ip1iABAR6RQDgIhIpxgAREQ6xQAgItIpBgARkU4xAIiIdIoBQESkUwwAIiKdirsAEELEzHM5
      iYjiWczdDvrbOJ1ObNy4EQ6HAzNnzsS0adP4RDAiou8pbo4AVFXFsmXLUFBQgPvuuw9fffUV
      ampqol0WEVHcipsA6OrqQjAYxOzZs5GamopFixZh9+7d0S6LiChuxU0A9Pf3IycnR+vyKSgo
      QHd3N1RVjXJlRETxKW4CwO/3w2QyadNGoxGKovCEMBHR9xQ3AZCcnIz+/n5tur+/H1arFbIc
      Nx+BiCimxM3WMzMzE+3t7XA6nRBC4ODBgxg7dixHARERfU9xMwzUarVi3rx5eOWVV5CTkwOH
      w4FHH3002mUREcWtuAkAAJgxYwbGjx+PgYEBZGdnw2iMq/KJiGJKXG1BJUlCcnIykpOTo10K
      EVHci5tzAERENLQkcRmMo1RVFX6//1vXEQCaHa7hKeg7ZCRbYDUbQlrX6Q2g1/Xtn204SJKE
      EemJIa/f3udBIBj9azRsFhNSk8whrRtQVLT3eiJcUWhyUq0wGULbP+t1+eH0BiJc0XdLTTLD
      ZjF994oAPH4FjgFvhCsKzYj0JIQ6lsQx4IXHr0S2oBBYzUZkJCeEtK4qBFq63RdddlkEwPkU
      RYHBENoGNlYoytlGFW91+/1+mM2hbWBjhaqqUFU17s4hBQIBGAyGuBr6fO5anXj8ro1GY1yN
      MhRCIBgMDrpe6rvE179KiOIx0+KxZgBxeyV2PNYdr20kHuuOx/YBhF93/OxKEBHRkLosAyCe
      Dtu+KR7rjqfuiG+Kx+86HmsG4rPueKwZCP/3eFmeAyAiou8Wn7tvRET0gxl++9vf/jbaRUSb
      EAJdXV04cuQIamtrAQApKSkXPQz0eDz4+uuvUVFRAVVVkZ6eDkmS4Ha7ceDAATQ0NCAtLQ0J
      CaEN0fohAoEATp06haNHj6K1tRV2ux0JCQmD6na5XKiqqkJ7e7v2x+FwICsrC0IItLS04MCB
      A3C5XMjMzIz4oa8QAk6nE8eOHUNlZSXcbjfS0tIuOvqps7MThw4dQlNTE5KTk2G1WgGcHVly
      9OhRVFRUIDExEUlJSRGvW1VVtLS04MiRI6ivr4fZbIbNZrvgfRVFwYkTJ3Ds2DFIkoTU1FRI
      kgQhBPr7+7F37150dHQgMzNzWEfG9PT0oKKiAllZWRd810II9PT0YO/evejq6hq0jt/vx6FD
      h1BTU4OUlBRYLJZh6x4RQqC2tha9vb1IT0+/6DrBYBDV1dVITEzURqOd+z3v27cPPT09yM7O
      HtauSq/Xi6NHjyIpKemi2wGn04mKigpUVlZClmVtW6OqKqqqqnD06FGYTCbY7faIf9c8AsDZ
      jeSyZcsQDAZhs9mwbt067Nq164L13G43/vznP6O7uxtZWVmorKwEcLYRvvPOO+jt7YXf78cb
      b7yBQCDy47L37duHffv2ISkpCT6fD6+88gr6+voGrePz+dDR0aH9qampwerVqwEADQ0NWLZs
      GWw2G/bt24fPPvss4jUrioKPP/4YXV1dSE1NxeHDh/Hxxx9fMFLkzJkz+POf/wyj0QhVVfH6
      66+jo6MDQgisWbMGx48fR2JiIt577z20tbVFvO4zZ85g7dq1kGUZCQkJ+OCDD3Dy5MlB6wgh
      UFZWhoqKCqSnp2PNmjU4dOgQgLM7Dq+++ioMBgNaW1vxl7/8RRv6G2nBYBCffPIJVq9eDZ/P
      d8Hy/v5+vPHGGzCbzaivr8dHH32kDZX96KOP0NjYCLPZjNdffx0DAwPDUjMANDc34+OPP8a2
      bdsuuryurg6vvvoq3n33XXR3d2vzu7q68Pbbb8NisaC6uhorV64ctpFIQgh89tlnKCsrQ3Nz
      8wXL29ra8Nprr6GzsxM2mw2ffPIJKioqAADbt2/Hjh07YLfb8de//hWnTp0aloLpPI2NjeLt
      t9++YP7WrVvF559/fsH8iooK8Ze//EWoqipUVRWrVq0SBw4cGI5SB1m+fLmorKz81nVWrFgh
      9u7dK4QQ4p133hG1tbVCCCE8Ho948cUXhdfrjXid3xQMBsULL7wgfD7foPmbN28Wu3fv1qa3
      bNkidu/eLVwul3j++edFMBgUQghx5MgRsXz58mGtWQghDhw4INauXTtontvtFn/84x+FqqpC
      CCG6urrEn/70JxEMBsX27dvF5s2btTby5ptvivr6+mGptby8XKxbt0787//+rxgYGLhg+ebN
      m8W2bduEEEKoqipefvll0draKpqbm8Urr7yifZ6tW7eKzz77bFhqDgQC4pVXXhGHDx++6G9R
      CCF27twpWlpaxP/93/+JxsZGbX5ZWZnYv3+/EOLs53nxxRdFb2/vsNTd0NAg3njjDbFy5UpR
      UVFxwfLe3t5BtVRVVYlPPvlE+Hw+8dJLLwmn0ymEOLsNev3117XvPlJ4BPA3Qgj4fD40NjZi
      w4YNuPrqqy9Y5/Tp0ygsLERFRQWOHz8Oj+fsVaMtLS0oLi6GJEmQJAnjxo1DU1PTsNStqiqc
      TicOHjyItrY2jBw58pLr9vT0oL6+HtOmTQMA9PX1YcSIEQCAhIQEZGVlDdqTihTxtwtWOjs7
      sXLlSkyaNOmCi1fGjx+PgwcPor6+HrW1taiqqsLo0aPR0dGBK664QjukHzVq1LDUfK5uj8eD
      2tpalJeXa9/jOaqqDtrTTE9Ph9lshsvluqCNjBkzBq2trRGv+VzX5k033XTJddrb2zFmzBgA
      0Gpra2tDW1sbRo8erXVDnJs/HMrLyzFmzBjk5eVdcp05c+ZcdHlXVxcKCwu16aKiIrS3t0ei
      zEGCwSBWr16Nn/zkJ5fs3ktJSUFKSgqAsz0KX375JUpKStDX14e0tDQkJp692j4vL0+79X0k
      XZYXgn0fzc3NWL58Obq6ujB9+nSMGzfugnUcDge2bNmCwsJCuN1urF+/Ho8//jhcLhdyc3O1
      9RITE+FyDc9tJz7//HMcPHgQTqcT//AP/6A1oIv56quvMHv2bG1jqyiKtiGVJAlWq1ULtUjy
      er1455130NHRgYyMDPz85z+/oK8zJycHiYmJWLt2LYLBIEaMGIG0tDR0dnYO6vM3mUzD0t0G
      ACdOnMDGjRvR2dmJBQsWXLDxsVqtyMrKwscff4zRo0ejtbUVbW1tCAQCcLvd2jkMAEhKShqW
      7pS1a9fi1ltvhcViueQ6Ho9n0PJz7VdVVSQlJWnzLRbLsLSPjo4OVFRUYOnSpYMeAhWqb16d
      LkkSkpKSIv57FELgyy+/xKRJk5CVlfWd69bX12PVqlWYNWsWJk2ahKampkHn786dN4o0BsDf
      jBgxAk888QTcbjd27tyJTz/9FHffffegdVJSUnDnnXciIyND64uurKy8oIG5XK5BP5xIuvnm
      m3H99dfD4XBg5cqVsFqtGD169AXr9fX1oba2FgsWLNDmGQwGrR/63N7ttwXIULFYLPjFL34B
      n8+H6upqfPjhh3jkkUcG3VJi69atKCoqwnXXXQchBDZt2oS9e/eioKBA2zOSJEm7ZH84TJw4
      ESUlJXA6ndi4cSPKy8tx/fXXa8tlWcZ9992HiooK9Pb2YuzYsairq4PVakViYiLc7r/fj8Xl
      csFms0W0XqfTiZ6eHnR2dqKrqwvd3d3Yv38/pk2bhtTUVG2984Pf5XIhLy8PQohBe84ej2dQ
      iEWCEAIrVqxAVlYWDh06hP7+fnR2duLgwYO46qqrQnoNs9msnesQfxt0cLHfxFByOBzYtm0b
      fvSjH2HPnj1oampCIBCAzWa74Kj866+/xvbt23H33Xdj1KhRAM7+Jrxer9auz/030tgFhLOH
      buJv9yux2+2YM2cOzpw5A+BsAzqXxLm5uTh58qQ2bTKZIEkSCgoKtPlCCFRVVX1rV8xQ8fv9
      kGUZZrMZeXl5mDp1Kk6fPq3V8c09iF27dmH69OmD9vTS0tK0riqPx4Ouri6kpaVFtGZVVbV7
      NSUmJqK0tBSBQAAej2dQ3a2trSgoKIAsyzAYDMjPz0d7ezuys7PR1NSEYDAIAKivr//OPa6h
      cO4ow2QyIS0tDfPnz0dNTQ2AwW3EbDajtLQU119/PZKSkpCYmAir1YqCggJUV1dDCAFVVVFb
      W/ut3RtDwWAwYP78+TCbzTCZTJBlGUajEbIsD6o5Ly9PG/2mqirq6uqQl5eH3NxcnDp1Sru9
      QE1NDfLz8yNaMwBce+21GD16NEwmk/YbO3fUen67vpjs7GycPn1aW7++vh45OTkRrdlsNuPO
      O++E1WrVvmuDwQCDwTCoXXd3d+Pzzz/HkiVLtI0/cHbnsr+/H06nE8DZHonhGAXEYaA4e2i/
      Zs0aeDwetLe348svv8SVV16JsWPHory8HM3NzRg1ahSys7Px6aefIhAIoKWlBQcPHsTChQuR
      mZmJnTt3oqurCw0NDaiursbixYsjfmO35cuXo6amBk6nE7W1tdizZw9uvPFGWCwWvPfeeygq
      KoLVatX2WH/84x8P2stOS0vDypUrIcsytm3bhjFjxqCkpCSiNff09ODtt9/W9k53794NRVEw
      c+ZM1NfX4/PPP8ekSZNgsViwevVqSJKE+vp6bNu2DQsWLEBGRgY6Ozvx9ddfY2BgANu2bcOi
      RYsivje9Y8cOlJeXw+12o6WlBZs3b8asWbOQn5+vjarKyspCeXk5Wltb0dTUhE2bNmHhwoVI
      T09HZmYmPv30U6iqiiNHjsDr9WLevHkRHZ5oMpmQn5+v/Tl27Bhuu+02JCcn4/Dhwzhy5AjG
      jBmDzMxMlJWVQQiBAwcOwGAwYPbs2bDZbKiqqkJdXR0cDgf27NmDn/zkJxEd4ixJEnJycrSa
      bTYbmpqacNtttwEAPvjgA2RlZSExMRH79+/HqVOnUF1drY12y8zMRG5uLsrKygCc3fFJS0vD
      tGnTIroxTUhIGPRdnzvnU1RUhM7OTnz88ccoLS3FiRMnUF9fD1VVcerUKZw6dQperxd5eXnw
      er0oLy9HIBDApk2bcPvtt19y+OtQ4ZXAOLuX0NTUhMbGRgSDQRQUFODKK6+ELMtoa2vTGiVw
      dsjc0aNHoaoqSktLYbfbAZzdgz5y5AgURcHUqVMjvkECzh4BnDx5Ep2dnbBarSgpKUFaWhpU
      VUVNTQ0KCwuRkJCgbWwvdlTS0tKCqqoq5OTkYPz48cMyXrq7uxs1NTVwuVzIysrC+PHjYTQa
      0d/fj66uLhQVFQE42xd88uRJyLKMcePGaT8GVVVx9OhR9PT0YMKECRHfuzv3nqdPn0ZzczMk
      SUJRURHy8/MhSRIaGhqQkpKC1NRUrf9akiRMmDBh0NFJX18fjhw5AovFgtLS0mG/i2p9fT0K
      CgpgNBrhcDjgdru1NtHT06ONXZ86daq2xx0IBHDkyBG4XC5MmTIl4keI5zu3YT9X57mjEIvF
      gpMnT15w/qe4uBgWiwWdnZ04ceIE7HY7SktLh/2WJW1tbbDZbLDZbPB6vWhsbMTYsWPR3d19
      wfDQtLQ0jBw5Uus9aG1tRXFxMQoKCiJe5/8PcPCc6CBPkBkAAAAASUVORK5CYII=
    </thumbnail>
    <thumbnail height='384' name='Low Income Household Vehicle Cost' width='384'>
      iVBORw0KGgoAAAANSUhEUgAAAYAAAAGACAYAAACkx7W/AAAACXBIWXMAAA7DAAAOwwHHb6hk
      AAAgAElEQVR4nO3deXAU950+/qfn1ug+0IkuJIRACIHEaTDGEDAGX9jGhpSdOD6SVDmb7G42
      talN9lvJbo6qLe+6knV2k1obk9ixY2LMJZnDGMSNOQTilISQ0H2MrtE1d/fvD/3UQSASjbrH
      g9TPq8pV1kh+z8c9M/30fK4WJEmSQEREmqMLdgOIiCg4GABERBrFACAi0igGABGRRhmC3QB/
      iKIIURTlnzl+TUQ0fsJEngXk9XphMEyoDCMium+wC4iISKPu6wDw+XzBbgIR0aQVtP6T5uZm
      lJSUyD+LogiTyYQXXngBoihi9+7daGpqgsViwcaNGxEbGxusphIRTUpBGwPwer1wOBzyz5cu
      XUJdXR02b96M0tJS9PT04LHHHkNlZSVOnDiB1157DTqd7q4aHAMgIhqfoHUBGQwGhIeHIzw8
      HGFhYTh79ixWrlwJALh69SqWL18Oo9GIvLw8DA4OYmBgIFhNJSKalO6LMYCKigpER0cjISEB
      Xq8XHo8HUVFRAABBEBAdHY3+/v4gt5KIaHIJegCIoogDBw5gzZo1EARBfvzOf5/As1WJiO5L
      QQ+AmpoaWK1WJCYmAhjqGjIYDOjt7ZX/pqenB2FhYcFqIhHRpBTUABBFEZ999tmIq39BEDBj
      xgycOnUKkiShpqYGBoMBoaGhwWwqEdGkE9QpNJ2dnbBarUhLSxvx+IMPPog//elPeOutt+D1
      evH8889Dr9cHqZVERJPTfbsVhCRJGBgYQEhIyD1P/pwGSkQ0fvdtAIwFA4CIaPyCPghMRETB
      wQAgItIoBgARkUYxAIiINIoBQESkUQwAIiKNYgAQEWkUA4CISKMYAEREGsUAICLSKAYAEZFG
      MQCIiDSKAUBEpFEMACIijWIAEBFpFAOAiEijGABERBrFACAi0igGABGRRjEAiIg0igFARKRR
      DAAiIo1iABARaRQDgIhIoxgAREQaxQAgItIoBgARkUYxAIiINIoBQESkUQwAIiKNYgAQEWmU
      IZhPbrfbUV5ejoGBAcyYMQOZmZkQBAE9PT24cOEC9Ho95s+fD6vVGsxmEhFNSkH7BtDT04O3
      334bOp0OU6dORW1tLSRJwsDAALZs2QKDwQCPx4O3334bXq83WM0kIpq0gvYN4MiRI3jooYcw
      f/78EY9fuXIF06dPx4MPPghJktDY2Ija2lpMnz49SC0lIpqcghYAzc3NKCgokLt6cnNzYTKZ
      0NjYiDlz5gAABEHAjBkz0NTUhOnTp0MURUiSJNcQRZHfDoiIxikoAeD1emGz2XD48GGkp6ej
      q6sLBw8exOuvvw6HwwGLxSL/rdVqRVdXFwBAkiSIoij/7s6fiUhd/7u/ArY+p+I6jxWmonBa
      7F2P/2x7OcTbLurG62sPZSNjSpjiOloTlADQ6/WIiYnBc889h9DQUEiShPfffx8NDQ0IDQ3F
      wMCA/Lf9/f0IDQ2V/zu9Xi//zuv1wmAI6jg20aTmEUW4vSpcZOl0MJlMdz3s9vogKj//Q6fT
      j1qf/rqgDAILgoC4uDhUV1fLP+t0Ouj1eqSlpeHatWuQJAmSJOHatWtIS0sLRjOJiCa1oF0+
      P/zww/jDH/6Anp4euN1udHd3IyUlBYmJiThx4gT27dsHt9sNn8/HACAiCoCgBUBCQgJeffVV
      XL9+HRaLBa+++qr8Fe7ll1/G1atXodfrsXbt2hHdPkREpI6gdqBHR0fjgQceuOvxsLAwLFq0
      KAgtIiLSDm4FQUSkUQwAIiKNYgAQEWkUA4CISKMYAEREGsUAICLSKAYAEZFGMQCIiDSKAUBE
      pFEMACIijWIAEBFpFAOAiEijGABERBrFACAi0igGABGRRjEAiIg0igFARKRRDAAiIo1iABAR
      aRQDgIhIoxgAREQaxQAgItIoBgARkUYxAIiINIoBQESkUQwAIiKNYgAQEWkUA4CISKMYAERE
      GsUAICLSKAYAEZFGMQCIiDSKAUBEpFEMACIijWIAEBFpFAOAiEijGABERBrFACAi0ihDsJ7Y
      7Xbj1q1b8s9GoxHp6enQ6XRwOBy4evUq9Ho9Zs+eDaPRGKxmEhFNWkH7BlBfX4/du3ejsrIS
      lZWVqKmpgSiKcDqd+N3vfof29nbU1dXhnXfegc/nC1YziYgmraB9A2htbcXixYuxbNmyEY+X
      l5cjLS0Njz76KADgnXfeQX19PTIzM4PRTCKiSStoAdDW1oakpCS0trYiPDwcVqsVgiCgvr4e
      s2bNgiAIAIBZs2bJASCKIiRJkmuIoshvB0QBdNvHTZFAf1Z5LhifoAVAcnIyamtrcePGDdhs
      NhQVFWHFihUYGBhAaGio/HehoaFoamoCAEiSBFEU5d/dGQhEpDZ1Pl+iKMLr9apSazQ+0RfQ
      +pNV0AJgyZIlWLJkCQBgcHAQv/rVrzB//nxYrVY4HA757xwOB6xWKwBAr9dDr9fLvxMEAQZD
      0P4XiCa94W/iShkMBpjNZlVqjcZoMAa0/mQVlEFgSZLgdrvlq/eQkBBYrVZ4vV6kpKSgqqoK
      kiRBkiRUVVUhJSUlGM0kIprUgnL5LIoi3nnnHaSmpiI1NRU3b96E2WxGREQEZs+ejf/5n/9B
      VFQU3G43Ojs7MW3atGA0k4hoUgvKNwCdTodXXnkFKSkpaGlpQVpaGl577TXo9XqEhobim9/8
      JpxOJwRBwLe//e0R3T5ERKSOoHwDEAQBJpMJ8+bNG/X3kZGR+MpXvvIlt4qISFu4FQQRkUYx
      AIiINIoBQESkUZxETzSB+UQRogprtXSCAL1OnTn/NHEwAIgmsPdKb+BaY7fiOstnJeGx+ekq
      tIgmEnYBERFpFAOAiEijGABERBrFACAi0igGABGRRjEAiIg0igFARKRRDAAiIo1iABARaRQD
      gIhIoxgAREQaxQAgItIoBgARkUYxAIiINMqvALDZbDh48CAA4MSJE/jHf/xH7Nq1C5Kkwobk
      RET0pfIrAA4dOoSGhgZ4PB68+eabWLt2LXbu3Inm5uZAtY+IiALErwDo7u5GUlISKioqkJiY
      iFWrViE9PR3d3cpvSEFERF8uv+4IlpeXh3feeQdGoxGrV6+Gz+dDS0sLYmJiAtU+IiIKEL8C
      YOnSpQAAu92ONWvWoLe3F+vXr0dSUlJAGkdERIEzpgBwOBzw+XwAgHnz5gEA3G43LBYLVq1a
      BUmSIAi8oTQR0UQypgD40Y9+hIqKCni9XrjdblitVgCAKIoQBAFbtmzhtwAioglmTAHws5/9
      DKIoYs+ePXC5XHj22WcBAC6XC//+7/+OsLCwgDaSiIjUN6ZZQFarFWFhYbDb7QCAsLAwhIWF
      ISYmBl6vF7W1tQFtJBERqc+vQeBFixbhX//1XxETE4O0tDRcvHgRt27dwpQpUwLVPiIiChC/
      AmDu3Ln4l3/5F2zfvh02mw0JCQn4yU9+gsTExEC1j4iIAsSvAKisrITRaMR//ud/Bqo9RET0
      JfFrJXBTUxPefPNNeUooERFNXH59A1iyZAmuXr2KN998Ey+//DJ0uqH8CA8Ph16vD0gDiSYy
      l8eHxs4BxXVCzQYkRltVaBHRX/gVAEeOHMGRI0fQ39+PU6dOQRAEWCwWvPHGGxwHIBqFrdeB
      3x24prjOrKnReGnlDBVaRPQXfgXAww8/jEWLFo14TBAEREREqNooIiIKPL8CwGw2o7m5GXv3
      7kVXVxemT5+OdevWsfuHiGgC8msQuKKiAt/97nchSRJyc3Nx8eJF/OAHP8Dg4GCg2kdERAHi
      VwCUlJTgxRdfxHe+8x1s3LgRP//5z6HT6VBZWRmo9hERUYD4FQBmsxm9vb3yz16vF06nE0aj
      UfWGiaIIURRVr0tEREP8GgN46qmn8Pd///eoqqpCQkICLl++jKlTpyInJ2fcDWhoaMC7776L
      559/HjNmzIAoijh69CjKysogSRJWrFiBwsJCbjdNRKQyvwJg6tSp+L//+z+cOHECXV1d+Na3
      voWioiKYTKZxPbnb7cauXbuQnp4Ol8sFYGix2eXLl/Htb38bPp8Pv/nNbzB9+nTONCIiUplf
      AVBdXY2amho8/vjjAIZuFPPRRx9h48aNCAkJ8euJJUnC0aNHkZubC7fbLT9+5coVLF68GFar
      FZIkYe7cuaioqMDChQv9qk9ERH+dXwHw2Wefjbjxi8ViwRdffIEHHngA2dnZfj1xa2srqqqq
      8Nprr+Gzzz6TH+/p6cGsWbMADK0xSEhIQHt7O4ChMQev1yv/rSiKI8KD6H7jcrpUqeMTfaPO
      tlNrWxav1ztqfVGUVKnvdnsCOlvQ5XJxNuI4+BUARqMRra2t8i0gnU4nenp65C0hxsrr9WL3
      7t1Yv369vIZAkiRI0t1vttsfMxgMMBgMI+rc/jPR/cbsUGcig16nl+/EN+JxldbgGAyGUevr
      dOqMvZlMxlHrq8VsNge0/mTl19nziSeewLe//W00NDQgOTkZZ86cQU5ODtLT0/160ra2NnR2
      dqKkpET+OSQkBE6nE9HR0Whra5Nrtre3IzY21q/6RET0t/kVAPHx8diyZQsOHjyIzs5OvPba
      a1i8eLHfVyHJycn453/+Z/nnffv2ISUlBXPmzEFLSws+/vhj5Ofnw+v1oqysDH/3d3/nV30i
      Ivrb/O4/8Xg8sFqtiIyMREFBAS5cuIAFCxb4NU1TEIQRoREVFYWwsDDodDokJyejsLAQ//u/
      /wsAWLduHe85TEQUAH7fEObHP/4x8vLy0NLSgmXLlmHLli2YNm0a4uLixt2IpUuXyv8uCAKW
      LVuGRYsWQRCEgCwyIyKicWwF8fWvfx0//elPYTAYYDabERERIc/SUYsgCDCZTDz5ExEFkF8B
      YLFY0NvbK2/R0Nvbi4aGBo6+ExFNQH51AT3zzDP43ve+h2PHjuHmzZt45ZVXMH/+fKSmpgaq
      fUREFCB+BUBCQgLee+89XLhwAXa7HRkZGcjKyuI+PUREE9CYA8DhcKCqqgp6vR5FRUUwGAzw
      er3YunUr1q5di5SUlEC2k4iIVDamABgcHMQPfvADNDU1AQDmzp2LH/7wh3jrrbdQXV2N559/
      PqCNJCIi9Y0pAC5duoTe3l68//77EEURr776Kr7//e9Dr9fjF7/4BefpExFNQGMKAJvNhpkz
      ZyIsLAySJCE/Px+dnZ342c9+xpM/EdEENaYAkCQJ3d3duHDhAgDAbrcjOzsbN27cgE6nQ25u
      Lsxmc0AbSkRE6hpTAERGRqK5uRlvvPGG/FhLSwtOnToFs9mMX/7yl0hISAhYI4mISH1jCoDl
      y5dj2bJl9/y9v9tBExFR8I0pAO7cvI2IiCY+XroTEWnUmALgF7/4Bbq6unDw4EHs27cv0G0i
      IqIvwZi6gOrq6lBdXY2WlhZ4PB709/eP+L3VauU4gAJlNTacrlK+o2pStBUbFmWq0CIi0oIx
      BcCLL76In/70p+ju7oYoitiyZYv8u5CQEGzdupVbQSjQM+DGrfY+5YXUuX83EWnEmAJg2bJl
      KCkpwa5du+DxePDss88Gul1ERBRgfu0G+thjj8Hn8+HGjRvo6+tDcnIyEhISuBsoEdEE5FcA
      9Pf348c//jHa2tpgtVpht9vx2GOP4Rvf+AbHAIiIJhi/AmDHjh2YMmUK/uM//gMWiwXNzc34
      /ve/jxUrViArKytQbSQiogDw67K9tbUV8+bNQ0hICARBQHJyMhITE9HV1RWo9hERUYD49Q1g
      yZIl+M1vfoOIiAgkJCTg/PnzaGpq4tU/EdEE5FcAPPjgg3C5XPjggw/Q09OD7Oxs/PKXv0RM
      TEyg2kdERAHiVwDodDqsXr0aq1evhiRJEASBM4CIiCYovwIAgHzC54mfiGhi82sQuKenBzab
      LVBtISKiL5FfAXDo0CH827/9GySJew4QEU10fgXA6tWrYTQasX//foiiCEmS5H+IiGhi8WsM
      4Pz58+jr68PPf/5z7Ny5E4IgwGw240c/+hGmTJkSqDYSEVEA+BUAM2fOxGuvvTbiMZ1Oh/Dw
      cFUbRUREgedXF1BCQgJmz54NnU4HnU6HvLw8REZGwmKxBKp9REQUIH4FQH19PV599VVs2bIF
      v/vd7yCKIt544w309PQEqn1ERBQgfgXAjh078Pjjj+Ott96C0WiE1WpFZGQkWlpaAtU+IiIK
      EL8CQJIkWCwWeRGYw+FAS0sLTCZTQBpHRESB49cg8JNPPokf/OAHuHjxIurr6/EP//APSEpK
      QlpaWqDaR0REAeJXAGRmZuJ3v/sdjh8/jqysLGRkZGDx4sUwGo2Bah8REQWI311AAwMDcLlc
      AACTycQ7gRERTVB+nb0vXbqEV199FRcvXoTNZsOvf/1r/Pu//zu8Xm+g2kdERAHiVxdQaWkp
      Nm7ciFdffRWCIKCnpwff/OY30dDQgMzMzEC1kYiIAsCvAMjOzkZnZ6c8CygqKgopKSnjemK7
      3Y5r166ho6MD0dHRmDt3LsLCwgAAnZ2dOH/+PPR6PRYtWiQ/TkRE6hlTAPz6179GXV0dPB4P
      qqurUV5eLv+uubkZ8fHxfj/xoUOHEB4ejuzsbDQ2NuLtt9/G66+/DpfLha1bt2Lp0qVwu914
      ++238Z3vfAcGg9+3LiAior9iTGfVpUuXIj8/f9Tf6XS6cd0cZsOGDfK/5+bmory8HG63G5cv
      X0Zubi4WL14MSZJQV1eHmpoa5OTk+P0cRER0b2MKgKKiItWfWJIk+Hw+dHd348SJE0hNTUVI
      SAiam5sxZ84cAEN3HcvJyUFTUxNycnIgiiJEUZRr+Hy+SbEV9e3/T0pIkgSPx6NKLVKHz+tT
      pY4kiaO+tqKkzntHFEevr9bHy+fzBfS9Gej6k5Vf/SoXLlzAD3/4QzgcDvmq32KxYMuWLX6P
      Bfh8PnzwwQeoq6uD0WjE1772Neh0OjgcjhGby4WEhKCzs9Ov2hONWiEmYeKH4WSj1mvCV/av
      43vff5+WNfgXAMeOHcOGDRvw1a9+VX5MEASEhob6/eR6vR4vvPACfD4f2tra8N577+H1119H
      aGgoBgYG5L/r6+uTB4GHdyG93WQYG9Dr9arU0Qk6Lsq7z6j1/rzXa6sT1FmHo9ONXl+tW3/r
      9fqAvjcNegPf+34SBJ1/6wDy8vIgSRIiIiLkf8LDw/1eDCaKItxut/ymmzp1KuLi4tDV1YWM
      jAxcuXJFvtPY1atXkZ6e7ld9IiL62/y6PJk+fTp+9atf4caNG/JjJpMJ//RP/4S4uLgx13E6
      nfj973+PhIQETJkyBe3t7XA4HEhMTER8fDxOnjyJPXv2wO12Q6/XY+rUqf40k4iIxsCvACgp
      KUFubi7WrVsnP6bX6/3uArJarfj617+Ouro62O12zJo1C48//ri8q+g3vvENVFRUQK/XY+bM
      map1kRAR0V/4FQBRUVEoKCjAihUrFD+x1WrFzJkz7/m7wsJCxc9BRET35lcAzJgxA2+88caI
      wRaDwYA1a9ZwtS4R0QTjVwBIkoQ5c+agpqZGfsxkMnH+LRHRBORXABQVFQVkURgREX35/AqA
      U6dOYffu3SMeM5lM+O53v4vY2FhVG0ZERIHlVwAkJyePGADu6enBZ599NmLlLhERTQx+BUB6
      evqIRVmiKOLIkSPo7u4e12pgIiIKHr8CwG63w2azyT97PB60t7eju7ubi7WIiCYYvwLg9OnT
      2Lp1q/yzIAgoLCzE9OnT1W4XEREFmF8BsHr1aqxcuXJkAYNhXPcDICKi4BpTALS0tKCysnLU
      3+n1esyfPx8hISGqNoyIiAJrTAHQ0NCA4uLiux5vb2/HrVu38MEHH3AMgIhoghlTACxcuBAL
      Fy4EMLQauKurC9u2bUNjYyO++tWv+rUTKBER3R/GPAYgSRJ6enrw0UcfYf/+/VizZg3eeust
      nvyJiCaoMQVAT08PPvjgAxw4cABr1qzBli1bEB0dHei2ERFRAI0pAI4dO4b3338fX/nKV+By
      uUZMBTUajXjhhRcQFRUVqDYSEVEAjCkAZs2ahddff330AgYDb9hCRDQBjSkAsrKykJWVFei2
      EBHRl8i/u7kTEdGkwQAgItIoBgARkUYxAIiINIoBQESkUQwAIiKNYgAQEWkUA4CISKMYAERE
      GsUAICLSKAYAEZFGMQCIiDSKAUBEpFEMACIijWIAEBFpFAOAiEijGABERBrFACAi0igGABGR
      RjEAiIg0igFARKRRhmA+uc/ng9vthk6ng8lkgiAIAABRFOF0OqHT6WA2m+XHiYhIPUELgKqq
      Kuzduxcmkwl9fX3Iy8vDo48+CkmSUFxcjJs3b8Ln82H+/PlYsWIFQ4CISGVBC4DGxka88MIL
      iImJgSRJePPNN7Fs2TJ0dHSgtbUV3/ve9yBJEv7rv/4LBQUFiImJCVZTiYgmpaCNATz88MOI
      jY2FIAhwuVzwer0wGAy4fv06Fi1aBL1eD71ej6KiIlRUVASrmUREk1bQvgEIggBJktDc3Ixt
      27bhoYcegtVqRW9vr3y1LwgCYmNj0draCgDwer3wer1yDVEU4Xa7g9J+NXk8HlXq+EQfBgcH
      ValF6nA5XarUuddr6/P5VKnv9XpHrS+Kkir13W5PQN+bLpeL730/eX3e4AWAJEk4e/Ysjh49
      ig0bNmDatGkQBAF6vX7Em9rn80Gv1wMADAYDDIa/NHn4W8NEZzQaVamj1+lhtVpVqUXqMDtE
      Verc67Ud/mwoZTAYRq2v06kz9mYyGQP63jSbzXzv+8mgNwSvC+j69es4ffo0vvWtbyErK0se
      5I2NjUVzczOAoZBoampCbGxssJpJRDRpBSUAJElCaWkpVq1aBUEQ0N/fj/7+foiiiDlz5uDU
      qVOw2+3o6OjA5cuXMXPmzGA0k4hoUgta/0lcXByOHz+O48ePy49t3LgR8fHxWLNmDd59913o
      9Xps3rwZISEhwWomEdGkFZQAEAQBzz333D1/n5+fj/z8fPlviYhIffflCCpP+kREgce9gIiI
      NIoBQESkUQwAIiKNui/HAIi+LG6vD+8duaFKracWZiA23KJKLaIvAwOANE0UJVQ29ahSy+VR
      Z1sGoi8Lu4CIiDSKAUBEpFEMACIijWIAEBFpFAOAiEijGABERBrFACAi0igGABGRRjEAiIg0
      igFARKRRDAAiIo1iABARaRQDgIhIoxgAREQaxQAgItIoBgARkUYxAIiINIoBQESkUQwAIiKN
      YgAQEWkUA4CISKMYAEREGsUAICLSKAYAEZFGMQCIiDSKAUBEpFGGYDdADU6PD3XtfarUyk6K
      hF4njHisz+FGc9eg4tp6nYDspEjFdfzV0j2I3kG34joRVhOSoq0qtIgo8NxeH2rb1DkvTEuM
      gFH/5V4vd/e70G53KK5jNuqRER8+6u8mRQB09TnxzucVqtT6t03zoTeNPCy17X14/8gNxbXD
      LAb8v+fmK67jr2PXWnDupk1xnflZU/Dc0iwVWkQUeP0Oj2rnhX95Zh6iQs2q1Bqr643d2Hnm
      luI6iVEh+McnCkb9HbuAiIg0igFARKRRDAAiIo1iABARadR9GwA+nw+iKAa7GUREk1bQZgFJ
      koTq6mqUlJRg+fLlKCwsBACIoojPP/8c5eXlkCQJy5cvx8KFCyEIwt+oSERE/ghaAJw4cQIV
      FRVISEiA1+uVH29oaEBVVRW+853vwOfz4b//+7+Rm5uLyMgvf/48EdFkFrQuoKKiIrz88stI
      Skoa8fjVq1exaNEiWCwWWK1WFBYWoqJCnbm8RET0F0H7BhASEjLq43a7HXl5eQAAQRAwZcoU
      tLe3AwC8Xu+IbwuiKMLtdsPlcqnWLofTCdE7MhfdLuWraAFAkoDBwbtXFHs8HlXq+0TfqPV9
      Pu8of+0/r887av2JzOVVb5zJ6XRhcHBkV6XLqc57896vrU+V+l7v6K+tKEqq1He7PQF977hc
      rrvqO53qfG6HajkxKKhzrMfKrdJ5QZSkUY+91+e9/1YCC4IASfrLm06SJLn/32AwwGD4S5O9
      Xi8MBgPMTnXepAAQYrHAcsdKYJNZ+XJsABAEwGq9eysFo9GoSn29Tj9qfb1enZfZoDeMWn8i
      07nVCUcAsFjMdx0fs0OdgLn3a6tXpb7BMPprq9OpM/ZmMhkD+t4xm+8+9k6feh0cQz0SX+5K
      YJOxV5U6OkEY9dgb9Ib7bxZQdHQ02tra5J/b2toQHR0dxBYREU1O910AzJkzBydPnsTg4CB6
      e3tRVlaGWbNmBbtZRESTTtC6gHbs2IGmpiZ4vV4IgoAzZ87gkUceQXZ2NhYvXozf/va3EAQB
      Tz755KTrdiAiuh8ELQA2bNhwz98tXrwY8+fPhyAII/r8iYhIPffl2VUQBNUGRomIaHT33RgA
      ERF9ORgAREQaxQAgItIoBgARkUYxAIiINIoBQESkUQwAIiKNYgAQEWkUA4CISKMYAEREGsUA
      ICLSKAYAEZFGMQCIiDTqvtwNlIjofldn68OuM7cU19HrBLz+6GzlDRoHBgAR0Ti4PD40dg4o
      rmPQq3Pf5fFgFxARkUYxAIiINIoBQESkUQwAIiKNYgAQEWkUA4CISKMYAEREGsUAICLSKAYA
      EZFGMQCIiDSKAUBEpFEMACIijWIAEBFpFAOAiEijGABERBp1X94PwO12o6GhATqdDmlpadDr
      9cFuEhHRpHPfBYDb7cYf/vAHWK1WeDweGI1GbNq0CTodv6wQEanpvjurVlZWwmq1YvPmzXjx
      xRfR3d2NlpaWYDeLiGjSue8CoKamBgUFBRAEATqdDnPmzEFNTU2wm0VENOncdwHQ19eHiIgI
      +eeIiAj09fUFsUVERJPTfRcAFosFbrdb/tnlcsFisQSxRUREk9N9FwCJiYlyl48kSaipqUFi
      YmKQW0VENPncdwGQn5+Pixcv4urVq7h48SIaGhowffr0YDeLiGjSue+mgUZGRuKrX/0qTp48
      Cb1ej5dffhlGozHYzSIimnTuuwAAgJSUFGzcuDHYzSAimtTuuy4gIiL6ctyX3zGV2eEAACAA
      SURBVADuRRRFiKI44mev1wuTHpiTFqXKc0j/f83bhZv1qtS3GPV31QaAuHCTKvWnRISMWn9q
      TAjcHuX1U2JGrz+RSZKo2nvHqMddx8ekF1SpnxJrHfXYp8dZYdBJiusnRppHrZ+TFIHESOWz
      8KKtxlHrz0mLhigpb3+IUXdXfb0gqfba6iDdVd9q1KlSX68TRj020aFGVepHhY7+2iZGWSBI
      kgpH/0tyZwBIkgRBEALyXMOHJVD1RVEM6PYWrH9vgX5t+d4JXn2+tv7Vn1DfAHQ63YjGe71e
      GAyB+V/weDzQ6XQB24jO5XJBr9cH7I3kdDphMpkCUjvQ9SVJgs/nC9hr6/P5IIpiwOoPX20F
      qv5Efm2HL+Im6msb6POC2+2+6zynpjtfW44BEBFpFAOAiEijJtQYwJ0m+hiAIAiTpi9RTZIk
      QZIk9hPfA1/bv14fmNiv7Zd5XpjQAfC3tLa2wmKxICoqCpIkobGxEceOHYPRaMTy5csRHx+v
      6EBLkoS2tjacP38eHR0d8Pl8CA8PR3Z2NubMmaO4n/DmzZtIS0uD0WiEJEk4f/48rl+/jilT
      puChhx5CSEiIovqiKKKyshLXrl1Db28vBEFATEwM5syZg/T0dEXHxuPxoL6+HllZWQCG+jY/
      //xzdHR0YMaMGZg/f77ik4DL5UJZWRlu3boFh8MBo9GIxMRELFiwAJGRkYrab7fb4XA45G1I
      bDYbSktL4fV6sWTJEsXHRxRF3Lx5E1euXEFPTw8AIDo6Gvn5+cjMzFR0bHw+H2pqauQV9B6P
      B0eOHEFLSwumTZuGJUuWKD72brcb5eXluHnzJgYHB2EwGJCQkICioiLExsYqOjYDAwPo6upC
      amoqAKC7uxuHDh2Cy+XCggULkJ2drfhz29vbi3PnzqG5uRkejwchISFIS0tDYWGh4s9VQ0MD
      YmJiEBoaCkmScPPmTXzxxRewWCxYuXIloqKiFLe/sbERFy9eRGdnJyRJQkREBHJzc5Gbm+vX
      eUf/k5/85Cfjbsl97vLlyxBFEXFxcXC5XHj//ffx1FNPISkpCTt37sTs2bMVrTKuqqrC4cOH
      sWjRIixYsABz585FSkoKGhsbceLECeTn5yt6oT///HNMmzYNJpMJNTU1OHfuHDZs2ACn04mT
      J09i9uzZ464tSRL27t0Lu92OxYsXY8GCBZg9ezaio6Nx6tQp9PX1YerUqeNuv8vlwpEjR5CX
      lwcAKCkpQUxMDJYvX47y8nIMDg4iOTl53O13u9147733kJKSgkWLFmH+/PnIycmBXq9HcXEx
      UlJSEB4ePu76DQ0NaGxsRFpaGiRJwu9//3s8/PDDyMnJwe7du5GVlaXoRHHo0CE0NTVh8eLF
      WLhwIfLz8xEXF4fz58+jra0NGRkZ4z72Xq8XBw8eRH5+PgDg8OHDEAQBq1atwo0bN9Da2or0
      9PRxt93r9eLDDz9EdHS0/N6ZMWMGzGYz9u3bh+joaERHR4+7fkdHB65du4asrCxIkoQ//vGP
      WLRoEfLz81FcXIz09HSEhoYqqv/xxx8jPz8fixYtQlFRETIyMtDf3499+/Zh5syZis4Lp0+f
      RlRUFMLDw2G327Fz50489dRTiI2NxSeffILCwkJFAXzhwgWUlZXJx76goACJiYmoqqrClStX
      kJubO+b3jmbGAFpaWjBz5kxMmTIFU6dORU5ODlpbWxXVPH36NF588UWkp6fDarXCZDIhNjYW
      Dz74IEJCQuQrOzVcu3YNa9asQUREBAoLC+F2u+FyuRTVbG9vx9q1axEfHw+z2QyLxYLk5GRs
      3LgRFRUVI6bcKtXR0YEFCxYgIiICq1atws2bNxXVq62tRW5uLoqKihAVFQWj0YjQ0FBMnz4d
      Tz75JM6fP69Sy4Guri7Ex8cjLS0N8fHxWLJkCaqrqxXVrK+vx+OPP47ExESYzWaYzWYkJibi
      qaeeQkNDw4gdcZVqaWnBkiVLEB4ejpUrV+LWrVuK6jU3NyMpKQmLFy9GdHS0fOynTZuGDRs2
      4MKFC+o0HEBvby/CwsKQnZ2N2NhYrFq1CteuXVNU8+LFi1i7di1yc3MRFhYGg8GAyMhIzJs3
      D3PnzkVVVZVKrQfq6upQWFiI2NhYZGRkIDk5WfF54dKlS3j++ecxdepUhISEwGQyYcqUKVi9
      ejVcLhf6+/vHXGvSB4DT6YQkSXA6nYiMjJQfF0VR8R5DVqsVNpvtrsd9Ph96enoUb2MtiiJc
      Lteo7Qeg+Gu81+sdNUT6+vpU6Yf0er3weDwAhqZEDn81VWOaXlhYGGw2G0brwWxra0NYWJii
      +sDQt5jh1yA8PFw+Hj6fT/E0SUmSMDg4eNfjg4OD8Pl8irsPh4/98DjZcHslSVJcOyQkBN3d
      3aNeINhsNlitVkX1gaFveMMLPUNDQ+Vj7/V6FR/7sLAwtLW13fW4JElob29X5b1z+3nn9vub
      qDG92WQyobu7+67H3W43BgcH/To+E2odgL+Sk5Nx6tQplJWVwe12Y+HChQCGDlRLSwseeugh
      RfUfeeQRfPLJJ4iMjERsbCyMRiN6e3vR3NyMuXPnKg6AjIwM7N27Fx6PBzabTT7hNzY2IjIy
      UnGArVixAn/84x+RlJSEqKgoiKKI7u5utLe3Y/369YoCxmg0IjIyEh9++CGAkStky8vLkZOT
      o6jtycnJiIyMlNsfFhYGp9MJm80Gn8+Hp556SlH96OhoXLhwAb///e8hiiIyMzMBDH2Ar1+/
      jieffFJR/ZUrV2Lbtm2Ij4+Xu0u6u7vR1taG1atXKzpJ6PV6JCQk4E9/+pN8Ehp26dIlxbvr
      xsXFISUlBe+//z6SkpIQEREBl8uFjo4OOJ1Oxcc+LCwMg4OD+MMf/gBRFOVxGFEUUVZWhvXr
      1yuqX1hYiOLiYtTU1CA+Ph4hISEYGBhAS0sL4uPjMW3aNEX1MzMz8cUXX6C0tBQDAwN45JFH
      AAyFQl9fn6KuSQBYvXo1du/ejdjYWMTGxkKn08Fut6OlpQWLFi2C2Wwec61JPQg8bPhDYDQa
      YTAY4HQ60dvbi/j4eMW1fT4fbDYbOjs74fV6ERERgfj4eEV9lKM9h9PphNVqhSAI6OzshMVi
      UeU5nE4n2tvb0dPTA51Oh+joaMTHx6u2A6skDS2h93q9ciC2tLQgISFB8ZWoJEno6+tDe3s7
      BgYG5K/CMTExqs0ykSQJLpcLOp0OJpMJXq8X7e3tisYvhrlcLvnYA5CPvZqLsLxeL9xutzxe
      0draiilTpii+CpUkCf39/Whvb0d/fz+MRiPi4uIQFxen6rEf7gozm80QRRHNzc2YOnWq4trD
      Fzs2mw0ulwtWqxXx8fGIiIhQbQaOKIpwOp0wm83Q6/UYGBiA0+lEbGys4trDF4VdXV0QRRGR
      kZFymPljUncBSZIEm82Ga9eujeh2GL7r2O1XRuPhdDohCAISExORl5eHrKws2O12tLa2wufz
      KW7/jRs30NnZCZ1ON+JrcGxsrGoBMxyODocDDocDbrdbnqqnlNvtxvXr12Gz2WCxWORupdjY
      WDQ3NyuqLYoi3G43IiIikJ2djdmzZ8NkMqGxsREOh0Nx+3t7e1FZWQmfzweLxSKflA0Ggyon
      f+Av4TJ87Ie7+9Q49l6vF9evX5dnwg0f+ylTpqCxsVFR7du7xbKyspCfnw+r1Yqmpib09/cr
      bv/g4CCuXbsGr9crj48AQ12eapz8gb9cmAwfe4fDAVEUVTn2kiShpaUFlZWVI1YNh4aGore3
      V/G5wel0Qq/XIzk5GbNnz0Z6ejq6urrQ3t7u97jdpJ4F1NLSgpKSEkRERODQoUPIzMyUE/Lo
      0aOIj49X1F/56aefyqk7ODiIrVu3Ijo6GrW1taiqqsKMGTMUXU3s3bsXFRUVqK6uRkJCAkJC
      QlS7OpEkCdevX0dxcTH0er3c79nQ0IDS0lIkJSWN6Pf2lyiK2LZtG3Q6Ha5fv47e3l55VpHd
      bsfZs2cxY8aMcbe/s7MTR48eRU5ODiRJwueff46bN2/CYrFg7969yM3NVdQFV19fj/379+Pq
      1avy1a2a87Orq6uxc+dOAENdHjqdDs3NzTh8+LA8i0bJsf/kk0/k6aC3zypyuVw4dOiQohlk
      vb29+OyzzzBz5kxIkoSTJ0+ivLwc4eHh2L9/PzIyMhRdoNhsNuzatQvXr1+HJEnydG21jr3L
      5cKf//xnNDU1ITQ0FFarFX19fTh79izq6+uRnZ2t6FtMTU0NDh06BKvVitLSUkyfPl2+gNi/
      fz9ycnIUfQPbtWsXUlNTYTab0dvbi61bt2LKlCm4evUqWltb5anXYzGpxwAuXbqEtWvXIiUl
      BdnZ2di+fTtefvnlgCxCuXHjBgoKCrBs2TJIkoQtW7bA7Xb71R93J7PZjA0bNqCvrw+fffYZ
      AODhhx9GQkKCKh+G48eP4+WXX77rzehwOPDhhx/ipZdeGvfz2O12WCwWrFixApIk4cMPP0Ry
      cjIyMjIUt/tOHo8HdXV18msbGxuL8+fPY+XKlYrqFhYWYsGCBTh58iS2bNkizxJRY5+ZI0eO
      4Gtf+9pdIeVyufDHP/4R6enp434el8sFj8eDlStXQpIk7Ny5ExUVFZg5c6bidt9JFEVcvXoV
      r732GnQ6HZKTk3H27FnF/fR5eXl4+OGHcebMGbz77rvIy8vDggULVOmaPHv2LPLy8lBQUDDi
      8aVLl+LAgQOorq5WdHFSVlaGp59+GhEREUhNTcWuXbuwefPmgJx3ysvLsWLFChQUFMDn8+Hd
      d9+Fx+MZ83Ga1F1AOp1O/koUFxeHefPmYd++fap8zbtTT08PkpKSAAytEgwPD5dnwCiVmJiI
      TZs2YfXq1Th27Bg++OAD1NfXK657+8yc25nNZgiCoOg43f7fC4KAJ598EgcOHEBfX9+4a96L
      y+VCRESE/AGLjIxUbRqlyWTCQw89hK9//evweDzYunUrTp48qfi1FQRh1A+pyWSCXq9XbQqu
      IAh49NFHcfLkSXR1dalS83bDs3SGj31YWJgq3Z/A0ESCBx54AC+99BJMJhO2bt2KI0eOKJ7+
      fOfMnNsND2grcft5JzU1FVOnTsWxY8cU1byX2887er0eoaGhfm3ZPqkDID8/H+fPn5eXVw8v
      wNi1a9eo06j8FR4ejk8//RR79uxBdXW1fDL1eDzo7e1VdSBYEATExcXh2Wefxdq1a1UJgNTU
      VOzZswf19fXo7OxER0cHampq8NFHH2HOnDmKrlgiIiLg8Xjk4xwaGopHH30UH330kfzVXgmD
      wYD6+np88sknOH78+IgT5o0bN5CWlqao/u2Gp1EuWbIEL774Isxms+L3z/Tp07Fz507cunUL
      nZ2d6OzsxK1bt/Dxxx8jOztb0ZWuxWKB0WhER0eH/PNjjz2G7du349KlS4qPvV6vR3t7O7Zv
      3y6vjh5WW1ur2hgJ8JegnD9/Pl566SVERUWNOvXaH4WFhfj8889RXl6OtrY2dHd3o6WlBadO
      nUJFRYXiWVKFhYU4ffq0PAV32bJl6O7uxqeffqrKBVBISAiKi4tRUlKChoYG+bwzODgIt9vt
      V9fnpJ4FJEkSqqqqkJ6eLh8UURTlPvrly5crOkkPz84ZGBhAR0eHPMbQ3t4Om82GWbNmKeqq
      OX36NObNm6eoG+mvGT4WlZWVGBgYgCAIiIiIQF5eHpKTkxV3M3V1dWFgYEBe0g8MdQ1dvHgR
      cXFx8irh8bh9ALWnpwcGgwGpqamQJAknTpzA4sWLFXXVtLS0oLe3V1FXwF8jiiIaGhpw7do1
      eeFOeHg4Zs2ahdTUVMXHvre3F52dnSNWFPf39+PixYsICwvD3Llzx117eHaOw+GA3W6HJEnI
      yMiAJEk4ffo0CgsLFb1nu7q60NjYiDlz5oy7xt/S19eHS5cuoa2tTV5bkJqairy8PMXTt4fH
      127v6/f5fKiursbNmzexevVqRQHv8/ngcDgwMDAAm82GGTNmwGg0or6+Hh6Px68xgEkdAERE
      dG+TugvorykpKfFrybQ/vF4vPvnkk4CMNQBD/X779+8PSG0AaGpqwvHjxwNWv7KyEuXl5QGr
      f+7cOdTU1ASsfmlpKdrb2wNS22az4fDhwwGpDQx10Zw9ezZg9S9duoSKioqA1T9x4gSampoC
      Vv/AgQOqbuFypx07dqg2NningYEBlJSU+PXfTOpZQH9NZmamaoud7iQIArKzswNSGxgapFWy
      mdffEhYWpmo/7p2io6MDem/hhIQEVbYjuJfhPVgCISQkRLW57qOJiIgI2N2sgKHJFoGsn5yc
      rOrY2p3S09MD1uUKAFlZWQHbCttgMMgr1seKXUA0wkTfT/3LqB+I2sOLFqdMmRKQ+j6fD93d
      3YiLi1O9NjA08aGvrw8xMTEBqT+8R5LSbRRopEm9EMzlcmH37t04c+YMenp6kJaWJn+4Dh48
      iKioKEVXcgMDA9i2bRsuXLgAn8+HpKQkuf6f//xnxYPA7e3t+Pjjj3Hp0iWEhISMWEL+5z//
      WdEgKjC0q+PHH3+M6upqpKen4/Lly9i9ezfOnTuneDEPMNTVs3v3bnlxTWlpKQ4cOIDLly8j
      Oztb8ZXWmTNnsH//fnnv+OLiYpSWlqKqqgozZ85UdCUqiiIOHz6M0tJSuN1uxMXFYdu2bTh5
      8iSampoUL/IbXvnrdrvlf0pKSjBt2jTFGxUObzR3e+2+vj4cPnwY6enpkCRJ0QC5KIp31e/o
      6MCZM2eQkpICAIqOvc/nu6t+dXU16urq5L1vlFxF19fXo7e3F5GRkZAkCZcuXcKuXbtQVlYm
      r+xX8tpeuXIFJpMJFosFkiTh+PHj2LdvH8rKyhAVFaVokR8AnD9/Xt6FVRRFlJSUoLS0FJcu
      XUJiYqJfITmpu4AuXryIlJQUFBYW4sSJEzh+/DiWL18OYGgusNK51ocPH8aSJUuQmpqKkpIS
      mM1meYXlaDs9jqf+I488gsjISGzbtm1E18zAwICi2pIkYf/+/XjiiScwMDAgjym89tprsNvt
      2L9/PzZt2jTuN6rT6cSJEyewceNGNDY2Ys+ePTCZTPjmN7+J6upqnDp1CmvWrBl3++12O65f
      v45Nmzbh4sWL2Lt3L2JjY/HEE0/g1KlTuHz5MgoLC8ddv7GxET09Pdi8eTMOHjyIffv2obCw
      EDk5Odi7dy/q6uoULWrbunUrQkND5RO9JEmora1FcXExEhISFC1ic7vd+O1vfzvigsTr9aK+
      vh579uxBdnY2FixYMO76PT09eOedd5CSkiLXH95TamBgAPPmzUNubu646zc2NuKjjz4aMXus
      p6cHbrcbjY2NWL58uRw04zG8vUpqaio8Hg+OHj2KV155BQCwbds2pKWlKfqm1NTUhNjYWERF
      RaG9vR01NTV46aWX5HtYvPLKK4oufm7duoXp06cjJCQElZWV8Hq9+MY3voHOzk7s2bMHr776
      Ku8HAAwNqE2bNg1GoxEPPvgg6urqVB1A6uzsRGZmJkwmEx599FGcOXNGlfUFwxwOh7wFxNNP
      P43i4mLF+xfdTqfTISYmBqmpqXC5XMjMzIRer0dMTIy8He94DQ4OIi4uDuHh4cjNzUV9fT0K
      Cgrk1aJKB9qGX1ur1YqioiJUVFTIN+DJyspSfK+H5uZmzJw5ExaLBQ899BAqKirkO3VlZWWh
      paVFUf3ly5fDZDJh+fLl2LRpEzZt2oTp06fj+eefV7yCefj9bjQasXbtWmzatAlPP/00Zs2a
      hc2bNys6+QOQ70kRERGBxx9/HJs3b8YTTzyBgoICbN68WdHJHxha+Jibm4uEhAQ8/fTT2Lx5
      M1atWoUlS5Zg8+bNik7+d2pvb0dWVhasViusVityc3MVv3du19TUhNmzZ8NsNiM8PBxTp06F
      3W5XrX5dXR2KiopgNBqRkJCA0NBQOByOMf/3kzoAkpOTUVdXB2DoK+lTTz2F4uJi1WZwJCUl
      yQuyLBYLHnnkEezcuVOeG61UeHg4Ojs7AQwNzC5fvhw7duzw6wX+ayRJkhfJJScny11Mw/3c
      SreDHt4TXRAEpKSkyP3DauzpHhoair6+PkiSBLPZLO9dBEDeOVWJsLAw+YMaHh6OlJQUuc2D
      g4OKu8fy8vKwfv16nDt3DocPH1Z1ZohOp8OCBQuwcuVKHDhwAGfPnlVtdS4wNNi4cuVKFBQU
      YPfu3bh69aqqNw8ym814/PHHkZKSgo8//hi1tbWqz6iz2Wyw2+0YGBgY8Vr29/erMsjc2tqK
      vr4+DA4OjngvDg4OqnIr17a2NvT398PhcMj1h9dn+PPZmtQBkJ+fL98vFhj6ID/77LPYt28f
      ysvLFQ+2LVu2DGfOnJHf/CkpKVi1ahW2b98uB48SS5cuxdmzZ+WTaG5uLgoKCvD++++rMlUt
      Pj5eDpglS5bIs0/q6+vlzc/Ga3iDs+GusPXr18tv/Js3byqeZZSQkAC73S7PJnrmmWfkwLp2
      7ZriPYcyMzPR0NAg//zMM8/I21vcuHFjRPfEeIWHh+OJJ55AcnIyPvroI1WnJQ/vurpx40bo
      dDp88sknirc4uLN+amoqnnvuOXR0dKC4uFjVEBAEATNmzMAzzzyDiooKlJaWqlY7MzMTBoMB
      n332GU6dOiVvC+F0OtHS0qJ4FlZBQQE6Ozuxb98+XL9+XT5BD+8EqjRgFixYgJqaGpSUlKCl
      pUXuRqyvr/d7q2/NzgLy+XyKr3LvZXirWYPBELAZI2rcWehe+vr6YDKZAjYdrqenR74VXyB0
      dnYiOjo6IK/t8D7yauzpfjuXy4WqqirMnj07IO+Z3t5etLa2Kr4Rz73YbDYMDAwEZLM/YGiX
      WpPJhISEhIDUB4bOCcNbUAfC8B3a1Lzfw+1cLhf0er1fn6tJPQtIkiRcvXoV586dg9VqHbEB
      1LVr1xAaGqroxRBFEWfOnMGVK1cQHR0tJ70gCDh37tyIQbLxcLvdOHHiBG7cuDHiRiGCIODs
      2bOKr1QGBwdx+PBhNDc3IzExUX7jmM1mnD9/XnH97u5uHD58GN3d3SMGJIeXxSu9IU9zczOO
      Hj0Kl8slbxkMDH2Nt9lsim5MLkkSampqcPLkSej1ennmhiAIaG1thcfjUXQl53Q6cfToUdTV
      1SEhIQEWi0Xe5fWLL75QfOz7+vpw6NAhdHR0ICkpSZ5F5na7UV5eLm8gNl42mw1HjhxBf38/
      EhISEBYWhqioKHR3d6OhoUFRQEqShIaGBhw7dgyiKCI2NhaRkZEICwtDY2Mj+vr67rmZ21jr
      V1ZW4osvvoDZbEZUVBSAoa6zmzdvwmAwKNoOQpIklJWVoby8HJGRkfL7RK/Xo7y8XPFaCa/X
      K+9bNGXKFDmwDAaD3+eFSd0FVFVVhcrKSsydOxeHDh0acTPsW7duKR5Q/eKLL9Db24u8vDzs
      2rVL3nwLgCqrIQ8ePAiz2YyMjAx8+OGHI9p7/fp1RbUlScKOHTuQmpqKiIgIbN++fUQ/tNL6
      Ho8HO3bswKxZs+Dz+bB79265i8DpdKK2tlZR/cHBQRQXF2Pu3Llobm7GkSNH5N91d3crHshr
      b2/HsWPHMH/+fJw/fx6XL1+Wf9fa2qq4C66kpAQxMTFISEjAtm3bRuxeqvTY+3w+bN++HTk5
      OTAajdixY4c8BuDz+XDjxg1F9V0uF3bt2oXZs2fDbrfjwIED8u/6+/tHdJ2NR19fH/bt24ei
      oiJUVlaOWLlss9lGfM7Go66uDhcuXEBhYSGOHz8+4njU19crnmF34cIFtLa2Ij8/H59++umI
      CQNVVVWKx2OOHDkCURSRk5ODjz76aMQGc/6+dyZ1ANy8eRPLli1DcnIynn76aezfv1+1AVRg
      aNfJhx9+GKmpqVi/fj1KSkpUHcyz2WxYsGABsrKysGrVKuzatUvVflaPx4Pc3FzMmTMH06dP
      R2lpqWqDbX19fYiNjUVGRgYWL14Mi8WCixcvqla/paUFM2bMQEpKCr7yla/I0+3UcuvWLSxY
      sACJiYnYsGEDzp8/r/jEc7u+vj7MmTMHubm5KCoqUnWbcpfLBYvFgqysLBQVFSE+Pl7enVIN
      HR0dSE1NRVpaGpYvXw6Hw6HKDq/D6uvrMW/ePCQlJWH9+vWoqqpSdfZeTU0NlixZgqSkJDz7
      7LM4dOiQqtuUD58Xpk6diieeeAJ79+5VdfylqakJDzzwADIyMvDII49gz5494w6VSR0Aw7dg
      G/731atXq7oXh9VqlQfuEhISkJeXhwMHDqg242L4/sWCICAzMxNRUVH44osvVPug3T4LqLCw
      UJ5br4Y7ZwGtWrUKly5dUu2DPHzsJUmCXq/HunXr8Pnnn6u2j0tYWJj83jEajVi3bh2Ki4tV
      Wd8BDHU3eL1eCIKAWbNmAYBqAanX6+X+ZkEQsHTpUty6dUu1gLRYLBgcHIQkSdDpdFi3bh2O
      HTumWkAOf26HF6ytW7cO+/btU+0kPTyDDBjq7ly3bh127typ2kl6+A5jABATE4MFCxbg008/
      Ve28YDKZ5AvZtLQ0JCcny91l/prUYwBRUVE4fvw4cnNzodPpEBUVBa/XiwMHDsBms2H27NmK
      pgtGRUXh5MmT8qrQxMREtLS04OTJk2hra8MDDzygaAxAEATU1tbKK5gzMzNRVlaGK1euoLu7
      GwsXLhx3bWBoMZXL5ZJv5D1t2jQcPnwYtbW18oKe8TKZTKipqUF4eLi8/0xmZiZKSkrQ3NwM
      vV6vaKvl0NBQlJWVIS0tTb5nb0pKCj755BN0dHQgKipK0UydyMhIHDt2TF5RHBYWBqvViuLi
      YthsNqSnpyvq53Y6nXL//PCxH+6O6O3txfz588ddW6/Xo6WlBQaDAVFRUdDr9Zg2bRoOHDiA
      hoYGeL1eRbeEDAkJwdWrVxEfH4/Q0FAYDAZkZGRgx44daGtrg9VqxbRp08ZdPyIiAidOnJC3
      OR4ev9i1axdsNhuSkpKQmJg47vpRUVHya6vT6RAZGQmdToe9e/eio6MDSE2y3gAAAZZJREFU
      M2bMUDTGEBMTg2PHjiE3NxeCICA+Ph5dXV0oLS2Vv9UrmQBhMplw9epVZGZmyrOxKioqUFZW
      hs7OTixevHjMtSb9LKCOjg5ERESMGOzt6+uTtydQMuIvSRLa2toQHx8vzziRJAnd3d3yAhAl
      ASCKImw224gBzuHntNvtiveq93q96OrqGrH/jCRJaGxshNfr9XtjqTs5nU4MDg6O2B/G5/Oh
      rq4OZrNZ8YKe/v5+iKI44sPq8XhQU1OD2NhYxfvedHd3w2KxjJi37XA4UFtbi9TUVEX70vh8
      PnR0dNz12jY3N8PhcCjeTNDj8cButyM2NlauL4oi6uvrodPpFN8wZ3irhuEBVGDo/VRbW4uI
      iAjFs3XsdjsMBsOIgXaXy4Xa2lokJiaOeN7x6OzsRFhY2IjPf39/P+rq6pCZmal4HUlbW9uI
      wV5JkmC329HQ0IBZs2Yp3qakvb19xK1hh/eS6ujokL9RjsWkDwAiIhrdpB4DICKie2MAEBFp
      FAOAiEijGABERBrFACAi0igGABGRRv1/2d6eZbTA8s4AAAAASUVORK5CYII=
    </thumbnail>
    <thumbnail height='384' name='Truck Delay' width='184'>
      iVBORw0KGgoAAAANSUhEUgAAALgAAAGACAYAAAAebaizAAAACXBIWXMAAA7DAAAOwwHHb6hk
      AAAPYElEQVR4nO3b70+V9R/H8dfhiBAEQ0UURC3JogyzbjC941oyqn2xbOsP4FZ5I8fcWlt3
      6m6btrS5NlqrTe+1NacQprk1XStzs7I5d5ag/DDkLMyDjDPoHOB7g50TJ35m54rOq+fjljvX
      57yvM3nu4vpxCE1OTk4KMJW31B8ACBKBwxqBwxqBwxqBwxqBwxqBwxqBwxqBwxqBwxqBwxqB
      wxqBwxqBwxqBwxqBI1DxeHxJ90/gsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbg
      sEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbg
      sEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbgsEbg
      sEbgsLZsqT8A/pveOHrhH9kPR3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BY
      I3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BY
      I3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BY
      I3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI3BYI/AlMDo6qv7+fsXj
      8XueEYvFFIvF/vZnydacf6tlS/0B/iv6+vp0/vx5Xb9+Xb/88osmJyclSYWFhXrkkUe0fft2
      bd26VcuWzf0juXnzps6ePatr165pcHBQklReXq7NmzeroaFB1dXVi/os2ZqTC0KTqf9pBOaH
      H37Qxx9/rN9//z39Wjgc1vj4eMa6/fv3q7a2dtYZXV1dOnLkyJxH/aKiIu3bt0+bNm2a97Nk
      a85ixeNxFRUVzXj9jaMXsjJ/IRzBA3b69GkdP35ck5OT2rBhg3bt2qXNmzdr1apVGhoaUn9/
      v7799ltdunRJoVBo1hldXV06fPiwxsbGVFZWpj179mjLli2SpKtXr+r48eOKxWI6dOiQWlpa
      VFNTE+icXMIRPEDRaFRvv/22Jicn9dhjj2nv3r0qKCiYde3IyIiKiopmjfy9995TJBJRcXGx
      Xn/9dVVVVWVs7+/v18GDBzUyMqLa2lrt379/1n1ka85fsdRHcC4yA9TR0aHJyUlt2rRJr732
      2pxxS1JxcfGscUejUUUiEUlSc3PzjCglqaqqSs3NzZKkSCSiaDQa2JxcQ+ABGRwc1MWLFyVJ
      jY2NCofD9zTn3LlzkqSysjLV1dXNua6urk5lZWUZ7wliTq4h8ICcP39eExMTKi8v17Zt2+55
      zuXLlyVJ9fX1c56jS1IoFFJ9fX3Ge4KYk2sIPCADAwOSpB07dswb1EKGhoYkad6jbkpqTeo9
      QczJNQQekNT95T+f646Njam7u1u3bt2acZvwz+LxuBKJhCSlTxvmk1qTSCQybgNma04u4jZh
      QG7fvi1pKpaLFy/q+++/V19fn27fvp1+yBMOh1VVVaXdu3friSeemDFj+hG0tLR0wX1OXzM0
      NJS+e5GtOdP9lfCHh4cXvTbbCDwAIyMjGh0dlSQdOHBAExMTGdsLCws1Ojqq8fFx9fX16YMP
      PtC2bdv06quvKi/vj1+qqUfo+fn5KiwsXHC/hYWFys/PVyKRUCwWU2VlZVbn5CICD8D073ZM
      TEyourpa9fX1evDBB7Vu3ToVFxcrHo+rs7NTn332mQYGBvTjjz+qo6NDTU1N6femTmH+yh2Y
      cDisRCKRcfqTrTnTzXZUn008HldJScmi95ttBB6A+++/P/3vuR6/FxUVaevWrdqyZYvef/99
      RSIRdXR0aMeOHVq1apWkP04VRkdHlUwm5/2eijR1zpz6zTH9NCNbc3IRF5kBKC0tVX5+viTp
      vvvum3dtOBxWc3Ozli9frvHxcV2/fj29bfoF4d27dxfc7/Rz3envzdacXETgAQiFQlq5cqWk
      P+6mzGfFihXauHGjpKlv+qWUlJSkz8kXE2ZqTV5eXsZpQbbm5CICD0h5ebkkqaenZ1HrV6xY
      ISkzwFAolD5FuHHjxoIzUmtKS0sz7r1na04uIvCApO48fP311+l70PNJnRasXbs24/XU11Yv
      XFj4y0mpNbN91TVbc3INgQfk6aefVl5enkZGRvTdd9/Nu3ZsbEy9vb2SZj4Y2rlzpySpu7t7
      3i8/DQwMqLu7O+M9QczJNQQekNWrV2v79u2SpLa2NvX398+59vPPP9fIyIgKCgr0wAMPZGyr
      ra1VRUWFJOno0aOz/jZIJBI6duyYJKmiomLWuzbZmpNrCDxA//vf/xQOhxWLxXTgwAH9/PPP
      GduTyaROnDihL7/8UpL0wgsvzLioC4VCamhokCR1dnaqtbV1xmP41tZWdXZ2SpIaGhpmPW/O
      1pxcwx88BCwSieijjz5Kn2OXlpaqpqZGyWRSPT096YvK2tpatbS0ZDzJnK6trU3t7e2Spu5u
      VFdXKy8vT729veknpU1NTdq9e/e8nydbcxZrqf/ggcD/AXfu3NGHH36YcY87JT8/Xy+++OKi
      jphnzpxRe3u7xsbGMl4vKChQU1OTGhsbF/V5sjVnMQj8P2R4eFg3btxQb2+vCgsLtX79eq1f
      v37Rj72lqUf/PT096urqkiTV1NRo48aNcx75g56zEAKHtaUOnItMWCNwWCNwWCNwWCNwWCNw
      WCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNw
      WCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNw
      WCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNw
      WCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNw
      WCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNw
      WCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNw
      WCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCNwWCPwHBWLxRSLxf41c/6tli31
      B3A2MTGhTz75ZNHrn3zyST311FNzbr9586bOnj2ra9euaXBwUJJUXl6uzZs3q6GhQdXV1Yva
      T7bm5AICD9Cvv/6qixcvLnp9RUXFnIF3dXXpyJEjisfjGa8PDg5qcHBQly9f1r59+7Rp06Z5
      95GtObmCwAMUjUYlSYWFhXr++ecXXP/QQw/N+npXV5cOHz6ssbExlZWVac+ePdqyZYsk6erV
      qzp+/LhisZgOHTqklpYW1dTUBDonlxB4gAYGBiRJ69at03PPPXfPc06ePKmxsTEVFxerpaVF
      VVVV6W3bt2/Xhg0bdPDgQY2MjOjkyZPav39/oHNyCReZAUoFvnbt2nueEY1GFYlEJEnNzc0Z
      UaZUVVWpublZkhSJRNK/OYKYk2sIPECpQP5O4OfOnZMklZWVqa6ubs51dXV1Kisry3hPEHNy
      DYEHKBuBX758WZJUX1+vUCg057pQKKT6+vqM9wQxJ9cQeEDi8biGh4cl/b3Ah4aGJGneo25K
      ak3qPUHMyTUEHpDU+feyZctUXl5+TzPi8bgSiYQkpU8b5pNak0gkMm4DZmtOLuIuSkBSgY+P
      j+udd95Jv56Xl6eSkhKtW7dOVVVVevzxx1VUVDTrjOlH0NLS0gX3OX3N0NBQem625kz3V8JP
      /SZbCgQekJGREUnS5OSkenp6Zmz/6aefJElFRUXatWuXGhsbtXz58ow1qUfo+fn5KiwsXHCf
      hYWFys/PVyKRUCwWU2VlZVbn5CICD8gzzzyj1atXa3JyMv3axMSE7t69qzt37qi3t1eRSETx
      eFxtbW3q7+/XK6+8kjFjfHxckhQOhxe933A4rEQikX5vNudMN9dvnT+Lx+MqKSlZ9H6zjcAD
      Eg6HtW3btnnXRKNRffrpp7py5YouXbqk06dP69lnn01vT50qjI6OKplMatmy+X9ciURCo6Oj
      Ge/N5pxcxEXmElqzZo327t2b/t7HN998k7F9+gXh3bt3F5w3/Vx3+nuzNScXEfgSy8/PTx+1
      o9GoxsbG0ttKSkqUlzf1I1pMmKk1qQvZbM/JRQT+L7B+/XpJUxekqbsv0tRDl9Qpwo0bNxac
      k1pTWlqa8TAnW3NyEYH/C0w/JVi5cmXGttTpy4ULFxack1oz21ddszUn1xB4QP7Kvd8rV65I
      klasWDHjlGDnzp2SpO7u7nm//DQwMKDu7u6M9wQxJ9cQeAAGBwf15ptv6sSJExnn1LPp7e3V
      qVOnJEkPP/zwjO21tbWqqKiQJB09ejT9RHK6RCKhY8eOSZr6o4na2trA5uQaAg9IIpFQR0eH
      3nrrLX311Vfq6+tTMplMb08mkzpz5ozeffddJZNJlZaW6uWXX54xJxQKqaGhQZLU2dmp1tbW
      GY/hW1tb1dnZKUlqaGiY9bw5W3NyTWhy+pMIZEUymdSpU6d09uzZ9P1kaep7KZWVlUomk4pG
      o5qYmJA09eRw7969evTRR+ec2dbWpvb2dklTdzeqq6uVl5en3t7e9Jympibt3r173s+WrTmL
      FY/HZ30o9MbRha8FsoHAAzQ8PKwvvvhCP/zwg3777Tf9+b86FAppx44deumllxb1QOXMmTNq
      b2+fcdpTUFCgpqYmNTY2LupzZWvOYhD4f8TY2Jhu3bqlW7duKRwOq7KyUmvWrJnx/ZOFTExM
      qKenR11dXZKkmpoabdy4MX2f+5+esxACh7WlDpyLTFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgj
      cFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgj
      cFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgj
      cFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgj
      cFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFgjcFj7PxUw
      GzKSPeKVAAAAAElFTkSuQmCC
    </thumbnail>
    <thumbnail height='384' name='Walk Trips per Capita' width='384'>
      iVBORw0KGgoAAAANSUhEUgAAAYAAAAGACAYAAACkx7W/AAAACXBIWXMAAA7DAAAOwwHHb6hk
      AAAgAElEQVR4nO3deXwUdZ4//lf13elOOvfVkEAOCCCGU8KNgwoegzPgEBFhHIdZZdQdnZ1z
      la/uY/eh6+w81l1nVl0PHBRFHS7xQGUEFbkZATEigYRcJATI0Z1O31X1+4NN/WgThHR30p3U
      6/l4+HhIVXfnXfl06lX1+XyqSpBlWQYREamOJtYFEBFRbDAAiIhUigFARKRSDAAiIpViABAR
      qRQDgIhIpXSxLiBeBAKBbstkWQZnyRLRYCXwOoCeiaIIANBqtTGuhIiob7ALiIhIpRgAREQq
      NegCIBAIfGe/vSiKSvcOEZGaDZhBYFEU8dVXX+Hjjz9GeXk57HZ7yPozZ85g48aNqKurQ3Jy
      Mm655RZcddVVyvpAIICtW7fi4MGDkGUZEyZMwC233AK9Xt/fm0JEFBfiPgBkWUZtbS02btyI
      1tZWuN3ubjN2Ojo68NJLL6GkpAS33347Tp48ib/85S944IEHkJ+fD1mW8d577+Hrr7/Gz372
      M2g0Grz66quQJAkLFy6EIAgx2joiotgZEF1AX3/9NUpLS/H73/8emZmZPa43Go1YsGABUlNT
      cc0112DatGn49NNPAQAOhwOHDh3C0qVLMXToUNjtdtx55504fPgw2tvb+3tziIjiQtwHgCAI
      uOmmmzB37lwYjcZu62VZRmVlJcaOHRvSnTNx4kRUVlZClmWcO3cOZrMZWVlZyvqMjAwkJSWh
      ubm5X7aDiCjexH0AXI4kSejo6EB6enrI8rS0NPh8Pni9XnR0dMBqtcJgMCjr9Xo9EhMT0dHR
      0d8lExHFhQEfALIsIxAIhOzcAShnA4FAAD6fD3q9PqSvXxAE6HQ6+Hy+fq2XiCheDPgA0Gg0
      SEhIQGdnZ8hyt9sNQRBgNpuRmJgIr9cbMv1TkiR4vV5Yrdb+LpmIKC4M+AAQBAEpKSmoq6sL
      WV5XVwebzQadTofExEQ4HA54PB5lvdfrRVtbG5KSkvq7ZCKiuDAoAmD8+PH46quv0NLSAlmW
      IYoiPv30U0yaNAmCICAnJwdGoxFHjx5VbvBWUVEBrVbb7XoCIiK1GBDXAbzzzjvweDyQZRkt
      LS3Ytm0bkpKSYLVacfPNNyMvLw8jR47Ec889h3HjxqGmpgZutxvTp08HABgMBtx444144403
      0NDQAI1Gg7///e8oLy/vcWYREZEaxP3dQGVZxt///vceb9dsNpsxbtw4AIDP58ORI0dQU1OD
      9PR0TJo0qVv3TnV1Nb766ivIsoyrrroKhYWFl/y5vBsoEQ12cR8AscIAIKLBbsCPARARUXgY
      AEREKsUAICJSKQYAEZFKMQCIiFSKAUBEpFIMACIilWIAEBGpFAOAiEilGABERCrFACAiUikG
      ABGRSjEAiIhUigFARKRSDAAiIpViABARqRQDgIhIpRgAREQqxQAgIlIpBgARkUoxAIiIVIoB
      QESkUgwAIiKVYgAQEakUA4CISKUYAEREKsUAICJSKQYAEZFKMQCIiFSKAUBEpFIMACIilWIA
      EBGpFAOAiEilGABERCrFACAiUikGABGRSjEAiIhUigFARKRSDAAiIpViABARqRQDgIhIpRgA
      REQqxQAgIlIpBgARkUoxAIiIVIoBQESkUgwAIiKVYgAQEakUA4CISKUYAEREKqWLdQHREAgE
      cOzYMTQ3NyMhIQGjR49GcnIyBEFQXtPS0oKKigoEAgGMGjUKubm5MayYiCj2BFmW5VgXEYmO
      jg4888wzkCQJRUVFaG9vx/Hjx7F8+XJcffXVAIBTp05h9erVyM/Ph9lsxuHDh3HnnXeitLT0
      kp8riiIAQKvV9st2EBH1twF9BiDLMvbv3w+TyYSVK1fCYDAAAPbs2YPNmzdj7NixEEUR77//
      PsrKynDjjTdCo9Fg5MiR2LRpE0pKSmA0GmO8FUREsTHgxwB8Ph/S0tKg1+uVZVlZWfD7/QCA
      1tZWnD17Ftdccw00mgube/XVV0Or1aK6ujomNRMRxYMBfQYgCALGjRuHl19+GXv27EFhYSFc
      LhfeeecdzJo1C4IgoLOzEyaTCTabTXmfTqdDTk4O2tvblWVdXT5dZFlW/iMiGowGdAAAQE5O
      DubOnYu//vWvSEpKgtPpxMiRIzF79mwAF8YIjEZjyBmCIAiwWCxwOp3KMlEUQ3b2sixDkqSQ
      gWQiosFkwAfAiRMnsH37dixbtgx5eXnweDx4//338fbbb2Px4sUwGAzKzv3inXkgEFDGDACE
      /D/AQWAiGvwG9BiALMvYuXMnysrKUFpaitTUVNjtdtxxxx3Yt28fHA4HEhMT4fV64fV6lfdJ
      kgSn04mkpKQYVk9EFFsDPgBcLldI9w5woY9flmV4PB5lJ9/Q0KB08XR0dKChoQF2u73fayYi
      ihcDOgAEQcDEiROxbds2HDt2DE6nE2fPnsVrr70Gu92OzMxMWK1WjB8/Hps3b0ZjYyNaW1vx
      +uuvo7i4GFlZWbHeBCKimBnwF4IFg0F8/vnn2L17N2RZhiiKyM/Px4IFC5CSkgLgwlTRLVu2
      oKKiArIsY8iQIViyZAmsVuslP5djAEQ02A34AOgSDAbR2dkJg8EAk8nUbfaOLMvo7OyEKIpI
      Skq67OweBgARDXaDJgCijQFARIPdgB4DICKi8DEAiIhUigFARKRSDAAiIpViABARqRQDgIhI
      pRgAREQqxQAgIlIpBgARkUoxAIiIVIoBQESkUgwAIiKVYgAQEakUA4CISKUYAEREKsUAICJS
      KQYAEZFKMQCIiFSKAUBEpFIMACIilWIAEBGpFAOAiEilGABERCrFACAiUikGABGRSjEAiIhU
      igFARKRSDAAiIpViABARqRQDgIhIpRgAREQqxQAgIlIpBgARkUoxAIiIVIoBQESkUhEFQFtb
      Gw4dOgS/3w8AOHDgAJ544gmsW7cOgUAgKgUSEVHfCDsAZFnGW2+9hbVr10Kj0aC2tha/+93v
      UF9fj1dffRWvvPJKNOskIqIoCzsARFHEsWPHMHHiROh0Onz66afIzs7G008/jV/84hfYvn07
      JEmKZq1ERBRFEZ0BSJIEk8kESZKwb98+TJ06FVqtFqmpqQgEAhBFMZq1EhFRFOnCfqNOhxEj
      RuDtt99GS0sLjh07hhUrVgAAvv76a+Tm5kKv10etUCIiiq6wzwAEQcDixYtht9vx8ccfY9Gi
      RRgzZgz8fj/27t2L+fPnR7NOIiKKMkGWZTmSDwgEAvB4PLBYLNBqtZAkCa2trbDZbAP6DKCr
      +0qr1ca4EiKivtHrAOjo6Liivn29Xg+LxRJ2YbHGACCiwa7XAbBy5UocOXIEwIWBYFmWodGE
      9iSJoog5c+bgySefjF6l/YwBQESDXa8Hge+77z44nU4AwNatW+F0OlFeXq6sF0URL774IqZN
      mxa9KomIKOp6HQBXXXUVACAYDGLDhg2YPHlyyM5elmU0NDRg165dWLBgAQRBiF61REQUNRFd
      BxAIBFBfX9/tgi+dToeqqir4fL6ICyQior4R0XUAU6dOxQsvvIBhw4Zh5syZ0Gg0qKqqwtq1
      a1FYWAiDwRDNWomIKIoimgba2dmJ5557Dlu2bIHBYIAgCHC5XCgqKsKjjz6KwsLCaNbarzgI
      TESDXcTXAYiiiObmZpw8eRJerxc5OTkoKiqCyWQa0P3/DAAiGuzCDgBJkrBx40a43W4sW7Zs
      QO/se8IAIKLBLqJbQdTX12PDhg04e/ZsNGsiIqJ+EFEALFmyBDk5OXj55Zfh8XiiWRcREfWx
      sGcBAUB9fT0mTZqEd955B52dnSgtLVXW5ebm8mIwIqI4FlEA7NmzB5988gm0Wi0qKipQUVGh
      rPv2BWJERBRfIpoF5Ha7lecBfxtvBkdEFN8ingYKXAiCU6dOQZIkFBQUDOgdfxcGABENdhF1
      AXk8HvzlL3/BG2+8gWAwqCy/7bbbcO+998JsNkdc4JUSRRE+nw8ejwdmsxkmkynkLqWiKMLt
      dkOWZZjN5gH9rAIiomgIOwBkWcb69euxYcMGrFixApMnT4ZGo8GRI0fwwgsvwGg0YuXKlf1y
      fYDL5cKWLVvwzTffQBRFCIKAOXPm4LrrrlPWb9iwAZWVlZBlGdnZ2Vi2bBlSUlL6vDYiongV
      dgD4fD7s2LEDt912G+68805lR19cXAy/34+3334bd999N0wmU9SK7Ykoiti4cSO8Xi8eeOAB
      pKamoqWlBZ2dnQAuBNUHH3wAh8OBhx56CGazGevXr8frr7+OlStXdnuWARGRWoS995MkCX6/
      H8nJyd2O8pOTkxEIBK7oyWGRamxsRFVVFX70ox8hIyMDWq0WmZmZGD58OADA6XSioqICt9xy
      C9LT02GxWLBw4UKcPn0ap0+f7vP6iIjiVdgBYDKZcNVVV2HTpk348ssvEQwGIUkSKisrsW7d
      OowePRoJCQnRrLVHJ06cQFFRkRI6Pp8PF49rd3R0QKvVIicnR1mWkJAAu92O5ubmPq+PiChe
      hd0FpNFo8JOf/AQ1NTV44IEHlDOB9vZ2DBs2DD//+c/7vP9flmW0tLSgra0NzzzzDJqamiCK
      IoYOHYpFixYhKysLTqcTJpMJRqMxpPakpCTlyWYA4PV68e0JUf1xBkNEFCsRzQLKzMzEU089
      hcOHD6OiogKSJGHUqFGYPHlyn/f9d3G73QgGg5g9ezby8/MRDAbx3nvv4YUXXsDvfvc7iKII
      jUbTLYy0Wm3IzKWLAwK40MUlyzKngRLRoBVRADgcDjgcDkyZMgVTp04FcOGovKqqCsnJyUhP
      T49Kkd8lOTkZKSkpIbehWLp0KVatWoXGxkYkJSXB6/XC7/crO3lZluFyuUKeV9DT2YogCIPu
      LqdERF0iGgR+9tln8dJLL4Us75oe+uc//zni4i5HEARkZ2fjxIkTIUfzWq0WJpMJoijCYrHA
      7/ejtbVVWe/3+9HY2IjU1NQ+r5GIKF6FHQCBQADV1dUoLS0NmUqp0WgwatQoVFZWdntWcF8Y
      PXo0Ojs7ceDAAaXb5vjx4/D5fMjOzkZKSgqGDBmCnTt3IhAIQJZl7NmzBwaDAcOGDevz+oiI
      4lVEg8AJCQmorq6GKIpKX7kkSTh58iQSEhL6pfvEarXi1ltvxaZNm/DFF1/AZDKhrq4Ot956
      q3Il8k033YQ1a9bgT3/6E4xGI5qamrB8+XJeDUxEqhb2vYC6LrD6j//4DyxatAjTp0+HIAjY
      t28fXn/9dTz44IP4wQ9+EO16L1lLS0sLamtrEQgEkJ+fj+zs7JAAcjgcOHXqFAKBAIYPH37Z
      8QneC4iIBruIbgYXCASwfv16rFmzBi6XCwBgNptx1113YfHixQP6CJsBQESDXcR3A5VlGW63
      Gw0NDZBlGXl5eTCbzQN+9gwDgIgGu4huhCPLMnw+H2pqalBZWQmXywWj0Yj9+/fD4XBEq0Yi
      IuoDEV0H0NjYiH/913/F8ePHodFoMHv2bIwbNw5vvPEGxo8fj+XLl0erTiIiirKwzwBEUcSL
      L74IWZbx5z//GStXrgRwocvk2muvxWeffdbt1gpERBQ/wg4An8+HyspKlJeXY8yYMSEPfzGZ
      THA6nbyXDhFRHAs7ALRaLRITE1FdXR1ypC9JEvbt24f09HQOoBIRxbGIrgP46KOP8OSTT2L2
      7NkQRRENDQ0YMmQItm/fjkceeQTz58+Pdr39hrOAiGiwi2gaqCiK2LVrF1577TUcP34cAFBQ
      UIAlS5Zg7ty5A/ppWwwAIhrsIr4OoCddd9tMTEyM9kf3GwYAEQ12YR2iy7KM1tZWVFZW4tSp
      U/B4PCHr/v73v/fL3UCJiCh8YV0HcODAATzxxBNoaGiAXq/H1KlT8dvf/haZmZnYs2cPHnvs
      McybNy/atRLRAPX+F3VobnfHuowQWo0Gy+eMiHUZMdXrAAgEAnjyySeRkpKCX/3qV3A6nXj2
      2Wfx8ssvY9KkSXj88ccxa9Ys3HPPPX1RLxENQLVnO3DqbEesywih0w7s29VEQ68DoKamBmfO
      nMEjjzyC8ePHQ5ZldHZ24umnn8a7776LBQsW4IEHHui3R0ISEVF4eh0AHR0dEAQB+fn5AC48
      laugoADBYBBLly7Fz372MxgMhqgXSkRE0dXrAOiaNCTLMgKBgPL/Go0GCxcuhCAICAQCEAQB
      Ol1EtxoiIqI+FNYeOhgM4p577lGmSHq9Xvh8Ptx///3KTn/SpEn49a9/Hb1KiYgoqnodAFlZ
      WViyZMlln/dbWFgYdlFERNT3eh0AQ4YMwS9+8Yu+qIWIiPrRwL1XAxERRYQBQESkUgwAIiKV
      6nUArFixAtu3bwcA/Pu//zteeOGFqBdFRER9r9cB4PF40NLSAlmW4fF44PP5+qIuIiLqY72e
      BTRz5ky8+OKLqKiowNGjR6HX63Hu3LluryspKcGSJUuiUiQREUVfrwNg2bJlMBgMOHLkCDwe
      D4LBIFpaWrq9rqMjvm78REREoXodABaLBXfffTcA4A9/+APS09OVfxPFUlObG+v3VMW6jG50
      Gg1Wzh8T6zKIuonoZj333nvvgH7sIw0u/oCI+vOdsS6jG952mOJVRAGQmJiII0eO4PXXX0dF
      RQUkScKoUaNwxx13YNKkSdGqkYiI+kDYh++yLGP37t34xS9+AYfDgYULF2Lx4sXw+/146KGH
      8Le//S2adRIRUZSFfQYQDAbxxhtvYNasWVi1apXyDIAf//jHePLJJ7F27VrMmTOHt4QmIopT
      YZ8BBAIBnD9/HhMmTAh5AIxGo8HkyZPR3t4Otzu+ngFKRET/v7ADQK/XIzc3F7t374bL5VKW
      e71e7Ny5ExkZGbBYLFEpkoiIoi/s/hmdToc77rgDDz/8MO677z6UlpZCo9Hg6NGjqK2txWOP
      PaY8MIaIiOJP2AEgCALGjx+P//qv/8LGjRvx5ZdfQpIkFBUV4aGHHsLo0aOjWScREUVZRCO0
      Go0GJSUl+N3vfqc8H1iv1/PaACKiASAqU3Q0Gg2MRmM0PoqIiPoJD9WJiFQqogvBGhsbUV9f
      D1mWo1kTERH1g4gC4LnnnsOqVav4TAAiogEo7AAQBAHXXXcdTp8+jd27d/MsgIhogIloGmhZ
      WRl++9vfYs2aNUhISMCECROU9RqNhreBICKKYxHtof/4xz/iww8/hNfrxUMPPRRyS4jp06fj
      8ccfj7hAIiLqGxEFwM0334zS0tIe12VlZUXy0URE1MciCoDS0lKUlpZClmWIoggA0Gq1EAQ+
      AIOIKN5FFACSJOHrr7/Ghx9+iNraWkyePBm33347nn76adx0000YNWpUtOokIqIoi2ga6Oef
      f45/+qd/wvHjx+H3+3Hq1CnlBnBvv/121IokIqLoi+h5AG+++SZuvPFGPP300/j+978P4MLs
      oHHjxuHo0aOQJClqhRIRUXSFHQDBYBBtbW0YN24cTCZTyDqXywVRFHltABFRHAs7AAwGA4YP
      H4733nsPTqcTsixDlmW0t7djw4YNKC4u5l1BiYjiWEQPhFmxYgV+85vfYNGiRbBarXC73di5
      cycSExNx1113cTYQEVEci2gW0PDhw/H888/jk08+QWVlJQCgoKAA1157LdLT06NSIBER9Y2I
      79WQnJyMefPmYfr06dBoNEhOTuYtIIiIBoCI9tQejwfr16/Htm3b0N7eDo1Gg9zcXNx+++2Y
      MWMGxwCIiOJYRNcBvPXWW3j++eeRl5eHu+66Cz/+8Y9hMBiwatUqfPrpp9Gsk4iIoizsMwC/
      34/PP/8c8+bNw29/+1vo9XoAwI033ohVq1Zh/fr1mDNnDgeCiYjiVNhnABqNBgkJCSgoKFB2
      /gBgNBoxY8YMOByOqBRIRER9o9cB0NnZCYfDAbfbjRtuuAH79+9Ha2srHA4HHA4HnE4nmpub
      MWLECB79ExHFsV53AT366KOoqKgAAIiiiI6ODixZsiRkwLejowN333139KokIqKo63UALFy4
      EHPmzLns6zIzM8Oph4iI+kmvA2DatGl9UUdUyLKMhoYGiKKI/Pz8kC4op9OJuro6BINB5OXl
      ITU1NYaVEhHFXkTXAfj9fhw8eBCtra3d1mVnZ2PSpEmRfHyvVVdX46WXXoJWq8Vjjz2m3Jr6
      zJkzWLNmDQwGA/R6Pc6dO4fly5ejsLCwX+sjIoonYQeAJEl4+eWX8fLLL0On03W76GvGjBn9
      GgAejwdvv/02Jk6ciCNHjih3IpUkCe+//z7y8vKwcOFCaLVafPLJJ3jrrbfw61//mlctE5Fq
      hb33CwQCOHDgAH7wgx/gwQcf7BYA/XkVsCzL+Oyzz5CQkIBJkybhyJEjyrrW1lbU1NRg5cqV
      MBqNAICZM2fis88+w6lTp1BcXNxvdRIRxZOI7gaalZWFlJSUbs8D6G8NDQ3Yt28fVqxY0W1d
      Z2cnTCYT0tLSlGV6vR5DhgwJ6brqup31xWRZ5kNtBpB4fv6E2r9H8doyam+XsANAq9Xi1ltv
      xYsvvoj6+noYDIaQ9UajEcnJyREXeDk+nw9btmzB9OnTkZOTg6amppD1TqcTRqMx5GI1QRBg
      tVrhdDpDPufbOxC1fzkGGq/PF+sSeiZf6KJUM0kUY11Cd2yX8ANAlmW4XC4cP34cP/3pT5UB
      1y5lZWV49NFHIy7wcjXs3bsXwWAQM2bM6PHCM61WqxzdX7xeFMWQbqpvn8WI//eF/fZ2Ufwy
      d8ZpYAuAxWKJdRUxFZd/R2yXyMYANm3ahKKiIixevLjbYGp/PA/A4/Fgy5YtyM7Oxvr16wFc
      6PJxOp1Yt24dxowZg4yMDHi9Xvh8PpjNZgAXjuw7Ojpgs9n6vEYiongV0RQYWZYxa9YszJs3
      L1r19Iper0d5eXnIsra2Npw4cQLFxcXIyMhAYmIiRFFEc3Mzhg0bBuBCcJw+fRpZWVkxqJqI
      KD6EHQB6vR6zZ8/GwYMHIUlSTO79r9frcc0114Qsa2xsxK5duzBp0iTodDrIsoySkhK8++67
      uPvuu2E0GvH2228jMzMTdru932smIooXYQeAIAhwu904cOAAfvWrXylTLLuMGjUKy5cvj7jA
      cOoymUxKf78gCLjlllvw6quv4tFHH4VGo0FKSgruuecePrCGiFQtoi4gk8mkXOwlfmuUP1Yz
      aHJycvDP//zPIcssFgvuuecetLW1IRgMIiMjg3cqJSLViygAysvLu/XBxytBEHj/HyKii0QU
      AA6HA16vt8d1/XUdABERhSeiAHjppZewffv2HtdNmTIFq1atiuTjiYioD0UUANdffz2uuuqq
      kGXBYBDPPfccxo8fH1FhRETUtyIKgLFjx2Ls2LEhy2RZxtdff40TJ05EVBgREfWtPpkHWVBQ
      gL1798Lv9/fFxxMRURREdAZw5MgRnD59OmSZJEl49913YTQa4/P+H0REBCDCAPj44497HARO
      TEzEypUrGQBERHEsogBYsWIFli1b1m25xWJBQkJCJB9NRER9LKIASEpKilYdRETUz3odAPX1
      9fjrX/962Vs9FBYW4oc//GHYhRERUd/qdQB4PB5UVlZeMgAkSUJFRQWmTp3KACCKwGNvHozL
      x1zec8No5Kaq+0Eqg0WvA6C4uBjPPvtst+V+vx/79+/Hiy++CLPZjAkTJkSlQCK18viCcfks
      XSkOQ4nC0+sAuPgumrIsw+PxYP/+/Vi7di1qa2sxf/58PPHEE8jNzY1qoUREFF1hDQJ37fj3
      7NmDN954A6dPn8bcuXOxatUq5OXl8VbLREQDQK8DwOfz4fPPP8e6devQ2NiI66+/Hg8//DDy
      8vL4gBUiogGk1wFQWVmJ//f//h/sdjvuuusuDB06FGfOnMGZM2dCXpeSkoKRI0dGrVAiIoqu
      XgeA3+9HMBhEbW0tnnrqqUu+bubMmfjDH/4QUXFERNR3eh0ApaWl+Oijjy7/wbqIrjEjIqI+
      1uu9tE6ng81m64taiIioH3HUlohIpRgAREQqxQAgIlIpBgARkUoxAIiIVIoBQESkUgwAIiKV
      YgAQEakUA4CISKUYAEREKsUAICJSKQYAEZFKMQCIiFSKAUBEpFIMACIilWIAEBGpFAOAiEil
      GABERCrFACAiUikGABGRSjEAiIhUigFARKRSDAAiIpViABARqRQDgIhIpRgAREQqxQAgIlIp
      BgARkUoxAIiIVIoBQESkUgwAIiKVYgAQEakUA4CISKV0sS6AiGgg+eBQHWQ51lV0N60kG7YE
      Q6/ewwAgIuqF7UcbY11Cj8bmp/Y6ANgFRESkUgwAIiKVYgAQEanUoAoASZIgSdIl14uiiGAw
      2I8VERHFrwE/CCzLMiorK7F9+3bU1dUhGAyiqKgIP/rRj5CamgoACAQC2Lp1K/bs2YNgMIix
      Y8eivLwcRqMxxtUTEcXOgD8DOHz4MNasWYOxY8fi97//PX7/+9/DbDZj9erVkGUZsizjo48+
      wtGjR3H//ffj4YcfRnt7O9auXQs5HudyERH1kwEfABkZGfjlL3+JGTNmICkpCampqfjhD3+I
      hoYGBAIBuFwufPHFF7jttttgt9uRnJyMZcuW4cSJEzh79mysyyciipkBHwBDhgxBenq68m9Z
      lnHs2DHYbDbo9Xo4nU7ldV2SkpKQm5uLhoaGfq+XiCheDPgxgIsFAgHs2bMHH374IcrLyyEI
      Ajo6OmAymWAymZTXaTQaJCcno6OjQ1nm8/m6dQl914DyYNHuDqDV5Yt1Gd2YDVrkJJt79R6v
      L/62AwAgA263O9ZVRI3X64Pb3btjRzEe/5YGWbv4wmiXQRMAbW1t2LhxIxobG3HnnXeipKQE
      AOD3+6HVaqHRhP5i9Ho9fBftMPR6fch6SZIgy3K39w02x6vasPVQfazL6KYwOwkr5o7s1XsM
      hkAfVRO5wTThwGDQ93p7NILQR9VEQBhc7aIPo10GRQA0NTVh9erVyMnJwf3334/k5GQI//eF
      S0xMhM/nQzAYVHbysiyjs7MTw4YNUz7j2zt6WZYhCAK0Wm2/bUcsaDRx+IcJQOlc9D0AABjE
      SURBVAB6/bvXCHEa1kLvtyWeaTSaXm+PEI8BALZLnP7FXDmv14tXXnkFI0eOxLJly5CSkhLy
      ZbNYLPB6vXA4HMqyYDCIM2fOwGazxaJkIqK4MOAD4KuvvoLH48HNN98MrVarXAzW1YWTmpqK
      zMxMHDhwQFn21VdfIRAIoLCwMNblExHFzIDuApJlGbW1tfB6vVi7dm3IOq1Wi8WLF8NqtWLe
      vHl49dVX0dzcDLPZjMOHD+O2224bVP1/RES9NaADAAAmT56M4cOHd1uu0WhgMFy4NWpRURHu
      ueceVFRUIBAI4Gc/+xkKCgr6u1QiorgyoANAEATk5eUhLy/vsq/Nzc1Fbm5uP1RFRDQwDPgx
      ACIiCg8DgIhIpRgAREQqxQAgIlIpBgARkUoxAIiIVIoBQESkUgP6OoBYqT7jRLs7/m49nJOS
      gJwUS6zLIKIBggEQhp3HmlBR3xbrMrq57mo7A4CIrhi7gIiIVIoBQESkUgwAIiKVYgAQEakU
      A4CISKUYAEREKsUAICJSKQYAEZFKMQCIiFSKAUBEpFIMACIilWIAEBGpFAOAiEilGABERCrF
      ACAiUikGABGRSjEAiIhUigFARKRSDAAiIpViABARqRQDgIhIpRgAREQqxQAgIlIpBgARkUox
      AIiIVIoBQESkUgwAIiKVYgAQEakUA4CISKUYAEREKsUAICJSKQYAEZFKMQCIiFSKAUBEpFIM
      ACIilWIAEBGpFAOAiEilGABERCrFACAiUikGABGRSuliXUB/aWlpwbFjxxAIBFBSUoKcnJxY
      l0REFFOqOAOora3Ff//3f6OiogL19fV46qmncPTo0ViXRUQUU4P+DCAYDOK9997D5MmTcfPN
      N0Oj0WD//v3YsGEDRowYAaPRGOsSiYhiYtCfAbS2tqK5uRlTpkyBRnNhc8eNGweNRoPq6uoY
      V0dEFDuDPgDcbjdMJhOSk5OVZTqdDrm5uWhvb49hZUREsTXoA8DpdMJoNEKv1yvLBEGAxWJB
      R0dHDCsjIoqtQR8ABoMBoihCluWQ5YFAICQUiIjUZtAHQGJiInw+H7xer7JMkiQ4nU4kJSXF
      sDIiothSRQBIkoTGxkblLMDlcqGhoQG5ubkxro6IKHZUEQDjx4/Hpk2b0NzcjPb2dqxbtw6F
      hYXIzs6OdXlERDEz6K8DEAQB119/PTo7O/HMM88AAOx2O5YsWQJBEGJcHRFR7Az6AAAAk8mE
      8vJyuFwuiKIIm83GnT8RqZ4qAgC4cCaQmJgY6zKIiOLGoB8DICKinjEAiIhUSpC/fYWUSnk8
      nm4Xi12KKMmIx9+aRgNoejm2IckyJKmPCoqAIABaTe+2RQYginHYMAB02t6POQXjdFu0WgG9
      3Zp4/ZtRe7swAC5B+r+9YtcN5AYyURQBAFqtNsaVRE6SJIiiOGiu4vb7/TAYDLEuIyoCgQC0
      Wu2g+JtRS7sM/JbqI7IsX/EZQbyTJEkJtIFOlmUl0AaDYDAY6xKihu0Sn76rXRgAREQqxQAg
      IlIpBgARkUpxEPgSun4tg+GKYW5L/JJleVBtCzA42kYt7cIAICJSKXYBERGplGruBdQXJElC
      a2srHA4HbDYbUlNTv3MOtCiKcDgc6OzsBABYrVYkJyf3eGomSRLOnz8Po9EIm82mLPd4PHC7
      3SGv1el0SEpKiuiUVZZluN1utLS0QBAEpKenw2QyXfIzZVmGx+OBw+GAz+eDyWRCampqyNxp
      r9erbGsXQRCQmpqq/DsQCKC1tRUulwupqamX/H30lt/vx/nz5+Hz+ZCWlobExMTv/Fy/34/2
      9nZ4PB7o9XrYbDZYLBZlfTAYhNPp7DY12GazQafTQZZlOJ3ObtMHTSZTyOeEQxRFtLa2wul0
      Ijk5Gampqd+5LaIoor29XfmeJCUldft+yLIMl8uFtrY2BINB2Gw2pKSkKN/fru1paWmB2WxG
      WlpaVObFy7KM9vZ2tLa2wmq1Ii0tDTrdpXdDl2uXrs9sa2tDe3s7tFotUlJSlPbu63ZpaWlB
      R0dHxO0iSRIcDkeP07V1Op2yD4h2uzAAwiSKIj755BN8/vnnMJlM8Hq9mD59Oq699toeL7hy
      u93YsGEDamtrodVqIQgCfD4fysrKcN1114W8R5ZlHDp0CG+++SZKSkpw9913K+u2bduGw4cP
      w2QyKcsyMjJwxx13wGg0hr09zc3NeP311+H1eiHLMiwWC5YtW4a0tLQeX3/48GF88MEHkCQJ
      Wq0WgUAANpsNS5YsQUZGBgBg165d2LlzJxISEpT36fV6PPjgg8r2b968GcePH4fRaITP58OC
      BQswbty4sLcDuPC7XrduHRobG6HX6yFJEm677TaMGDGix9fX19dj/fr16OzshE6ngyRJ0Gg0
      WLBgAUaPHg0AqK6uxtq1a2G1WkPe++Mf/xhZWVmQZRnPP/88gsFgSFtOmDABc+fODTvUgsEg
      PvzwQxw8eBAmkwkejwdz587FzJkze3y91+vFX/7yF5w7d07ZsQYCAcyaNQuzZ89WdorHjx/H
      li1blL5uv9+PxYsXK7+jb775Bhs3blTadujQoVi8eHFIW4bj0KFDeOedd2AymeDz+TBu3Dgs
      WLCgx9deql1uvfVWjBo1CsCFA6Vdu3Zhx44dMJlMkCQJOp0OP/3pT5GSknLJdpk4cSK+973v
      9Vu7eDwerFmz5pLt0rXe7/eHvK+zsxOZmZm47777AES/XRgAYaqpqcEnn3yC5cuXY/jw4aip
      qcGaNWuQn5+P4uLibq+XJAlDhw7FddddpxxpnTp1CmvWrMHIkSMxbNgw5bUulwtbtmxBaWkp
      nE5nyOfU19fj2muvxdixY5VlWq02oqMAURSxadMm5ObmYsGCBZBlGX/961+xefNm/PSnP+3x
      PbIsY8GCBcjPz4fRaERnZyc2btyIDz74AHfeeScAoKGhAWVlZSgrK1PeJwiC8kd38OBBVFVV
      4R/+4R+QlpaGQ4cOYcOGDcjPz0dKSkrY27Njxw44HA488MADSEhIwM6dO7FhwwY88MAD3Xbg
      XdtSVlaGkpISWK1W+P1+7N69G5s3b0ZhYSGMRiOam5tht9tRXl4e8t6uO8wGg0E0NzfjwQcf
      DPkZRqMxojOayspK7Nu3D3fffTfsdjtOnjyJl19+GSNGjEBWVlaP2zJ8+HAsXLhQOWqsrKzE
      mjVrUFpaipSUFLS0tOCtt97Cddddh4kTJ0Kr1aK1tRVmsxkA0NHRgY0bN2L69OmYNm0aXC4X
      Vq9ejZ07d2LevHlhb0trayvWr1+P2267DWPHjsW5c+fw3nvvweVyXXG77Nq1C5s2bUJBQQGM
      RiOOHTuGbdu24a677kJeXp5yVN51dD9Q2sVms+EnP/lJtzPMF154AYWFhQAutMuGDRswY8aM
      qLULxwDCtH//flx99dUoKiqCTqdDYWEhSktLsX///h5fb7VaMWfOHOTk5MBkMsFgMKCgoAA2
      mw0ul0t5nSzL2LRpE8aMGdNjkDQ3N2PYsGFITk5W/rtc98bltLS0oLa2FjfccAMSEhJgsVhw
      88034+uvv0ZHR0eP75kwYQLGjBkDq9UKvV6P5ORkjBo1Cg6HA7IsIxgMoq2tDUOHDg2ptevL
      7/f7sX//flx77bXIysqCXq/HhAkTkJGRgaNHj4a9LR6PBwcPHsRNN90Em80Gg8GAadOmQRAE
      VFdX9/ievLw8TJ06FSkpKdDr9bBYLLj66qvh9Xrh8/kAAGfPnoXdbg/ZluTkZOWosqWlBUaj
      Ebm5uSHru3aq4ZAkCXv37sWUKVOQn58PvV6PkpISjBkzBnv37u3xPWazGfPmzUNmZiaMRiOM
      RiNGjRoFvV4Pl8sFWZaxZ88e5Ofno6ysDEajETqdDpmZmUqYnThxAnq9HmVlZTAYDEhJScH8
      +fNx4MCBkGdr99aRI0eQm5uLcePGQa/XIycnB2PGjAm7XSRJwrZt23D99dejoKAAOp1OaYOu
      A6L+bJfRo0dfsl0SEhK+s100Gg1sNltIje3t7WhpacH06dMBXGgXg8HQrV0OHjwYdrswAMLQ
      9Yzh0aNHKzteQRAwevRoNDY2XtFtF7pOXX0+H/Lz85XlFRUVqK6u7vG02OPxoKOjA5mZmdHb
      GFwIlczMzJCj7vT0dKSmpqK5ufmKP+OTTz5BaWkpBEFAIBCA2+1GcnJyj6/3eDzwer0YNmyY
      8jvUarUYMWIEGhsbw94Wp9MJjUaDIUOGKJ9rMpkwbNgwNDU1XdFnuN1uvPPOOygsLFR2imfO
      nPnOR4h2/Q6jeR+crqPZ4uLibt+zhoaGK/qMYDCIbdu2wWq1Ijs7G6IooqamBhMnToQgCOjs
      7EQgEAh5T1NTE4YPH650KQqCgKFDhwLAJQ8IrsTp06dRUlISsi3p6em9apd3331XaZfOzk6c
      O3cO48ePV8Y0vv2319zcjKysrH5rl9OnT1/RZwSDQXz00UdKu/Tkww8/xLRp05TvYFNTk3Lm
      0/Uzhw4dClmWw24XdgGFwe/3w+v1IikpKWS5zWaD1+uF3+8P6aO/+H07duzA6dOnUV1djays
      LNx7773KqWnXqffixYt7fH97ezsEQcC//du/AbgwiDRq1CjMmTOnx1PoK9Xe3t7tKWmCIMBm
      s6G9vf2S7zt58iT27duHs2fP4ty5c5g3b55ytB0MBtHS0oL/+Z//gVarhdVqRVFREa6//nok
      JSXB7/dDkqSQ7RQEAcnJyaipqQl7W9xuNwwGQ0h/75Vsy/nz57Fjxw60tLTg5MmTKCsrw/e/
      /33ld+J0OrF+/Xps3rwZZrMZQ4cOxfe+9z3Y7XYIgoC2tjbU1tbikUcegVarRXp6Oq655hpM
      nDjxOwc5v4skSXC73d0GK202GxwOxyXf5/f78eGHH6K5uRmnTp2C3W7Hz3/+c+h0Oni9Xjid
      TuzYsQNvvfWWclYwZswY3H777bBarWhvb0dmZmbI90Gn00Gn08Hj8YS1LQDgcDhCAgC40Dbf
      tS1d7XL+/HlUVVWFtIvT6YQkSXjllVdQV1cHv98PrVaLWbNmYd68eTAYDGhra0NNTU3U28Xj
      8XT7m+s6ar8Uv9+PDz74AGfPnu3WLt9WU1OD6upqLF26VFnWF+3CAIiiy108IggCrFYrUlJS
      kJ2djY6ODhw/fhxpaWkQBAFbt25FYWEhRo4c2eP7s7Oz8fDDD0OSJAQCAZw9exYff/wx6urq
      sGLFin6/e6HBYEBycrIyg+jkyZMYO3YsUlJSYLVa8cgjjyAYDCIYDOL8+fP49NNP8eyzz+KX
      v/xlv9Z5JbRardI91d7ejtOnT6OhoQEFBQUQBAH33Xef0u3gcDhw8OBBPPfcc7j//vuRnZ2N
      6dOn46qrrgJw4ezm1KlT2Lx5Mzo7O3HttddG9aKiy31W19PvAoEAOjs74XQ6UVlZicmTJ0OW
      ZQQCAQwbNgyLFy9Geno62tra8Oqrr2Lz5s3K+E1fuNQlR991KdLF7eJwOELapevMZcKECVi6
      dCkSExNRVVWFV199FWlpaZg2bdol28XtdmPOnDlht0s4l08JgoCkpCQEg0G4XC44nU6cOHEC
      kyZNCqlDkiR8/PHHmDJlSsQzlS6HARAGg8EAk8nUbYDW6XTCaDReckes1+uV/jwAqKurw0sv
      vYTk5GRYLBbs2rULZWVl+OijjwBcGERtbm7G1q1bMX78eGRnZ4fMysnNzYXdbsd//ud/wul0
      Ij09Pazt6TqivDjAZFlWprdeSl5eHvLy8gBcmH2yZcsWrFu3Dvfee68yHa9LTk4OiouL8S//
      8i9oaGhQpsx19bFf6c+8nISEBPj9/pA7IHZ97qW6owAgJSUFN9xwA4ALp/h79+7Fa6+9hn/8
      x39EcnJyyNleVlYWCgoK8L//+784duwYsrOzYTAYlNlPXb8bk8mETz/9FLNnzw7rVtwajQZm
      s7nbVNquM7ZL0ev1mDNnjrLtVVVVWL16NTIzM2G322Gz2TBy5EhlsDI9PR0//OEP8cILLyjP
      zG5vbw/5PnQFeSR9511HyN8+UAq3XRITE6HX61FaWqrUVVxcjFmzZuGbb77B1KlTv7NdZs2a
      FVG7XDx2B+Cy392e2uWll15CZmZmyCSQhoYGnDp1CrfeemvI76mnv9NI24VjAGHQaDTIycnB
      N998oxwJdE2ty83NveL+xqFDh6KkpATV1dWwWCy48cYbkZKSosyU6ZqyJwgCNBoNJEnqduRh
      Npuh1WojuhVvdnY2zp07F3L62traira2th5nNPTEZDJh+vTpqK+vV24//e1aDQYDDAaD8oU1
      m82ora1V1kuShBMnTiA3NzfsbUlKSoIsyyHjCD6fD3V1dcjJybmiz9BqtRg/fjy0Wi3Onz/f
      47ZotVokJCQo88t7GvexWq0QRTHs24prtVqkpaWhqqoqZPk333wDu91+RZ8hCAKKiopQUFCA
      qqoq6HQ6pKamhnx3gQtto9FoIAgCcnNzUVtbGzIlsatvO5Lnaufm5uLEiRMhP/f8+fPfObZy
      sW+3S1JSEjQaTbdBZIPBoOwgv6tdwtXVLt/+uZG0SxdJkvDZZ59h7NixIdfLABcOompqarq1
      SyTPO2cAhGny5Mk4fPgw6urqIMsy6uvr8cUXX2Dy5MnKa7744gtlR3TixAns3r1b6XMFLgxQ
      VVVVIScnB1lZWZg/f37If1dffTWys7Mxf/58ZGZmYu/evdi2bZtyFOD3+/HZZ5/BYrF0G4/o
      jbS0NNjtdvztb39DIBCA3+/H1q1bMWLECOVzz5w5gyNHjiin3e+88w4aGhqUPzCfz4dDhw4p
      F8N9+eWXeO+999Da2gpJkhAMBrFz504Eg0HY7XYYDAZMmjQJ27dvR2trK2RZxpdffommpqaQ
      Ka69ZTabMX78eLz//vtwu92QJAn79u1DIBBQptO5XC7s379fObLeuXMnjh07pmybJEn45ptv
      4PP5YLPZ0NTUhDfffBOnT5+GKIqQJAnHjh1DVVUVCgoKAABvvvkmjhw5oszGaG9vx44dO1BU
      VBT2g3g0Gg3KysqwZ88enD59GrIs4+TJkzhy5Igytdbv9+Pw4cM4e/YsAODo0aM4cOCA0ifc
      9d2sqalRDk6mTZuGAwcO4OTJk0qX0Pbt2zFixAhoNBoUFxfD4/HgwIEDkCQJLpcLH3zwASZM
      mNDj2NSVKi0tRV1dHb766ivIsozz58+joqIi7HbR6/WYMWMGtmzZgra2NgAXjsL379+PESNG
      QBCEHttl+/btKCoqCntgWKPRYMqUKdi9ezcaGxsjapfa2tqQA56mpiZUVlZi5syZ3eorLi6G
      2+2OaruwCyhMBQUFmDFjBp555hmkpqaitbUVc+bMUb7MAPDWW29h9uzZyM3NhV6vx549e/Du
      u+/CarVCq9XC4XBgwoQJGD9+/CV/zsWngMOGDcOmTZuwY8cOWCwW+Hw+mM1mlJeXR/SHqdFo
      sGjRIqxevRqPP/44ZFmGyWTCihUrlNccO3YM27dvx69//Wvo9Xq43W48++yz0Ol0SExMhNPp
      hMViQXl5OTQaDex2O/bv348//vGPMJvNCAaDEAQBS5cuVS5amTx5MmpqavDHP/4RiYmJaG9v
      R3l5eUTXAADA9773PTQ0NODxxx+H2WyG1+vF0qVLlf7UlpYWvPLKK/jNb34Di8WiXPfg9/uV
      gfxgMIhbbrkF6enp8Hg80Ol0ePbZZ6HVaqHX65ULf7pO3UeMGIF3330Xb775JqxWKzo6OjBs
      2DDMnz8/ov7/kSNHYuLEifjTn/4Em82GtrY23HLLLcqZmcfjwfr163HDDTcoUwzff/99bNy4
      UQlvl8uFKVOmKGNLBQUFmDt3LlavXo20tDR4PB4kJCQoFxxarVYsWrQIr732Gj777DN0dnYi
      Ly9P6b4IV2pqKhYtWoS1a9ciJSUFHR0dmDBhQtjtAgAzZ87E+fPn8eSTTyIrKwttbW0YMWIE
      Jk6cCKDv2qWkpAQTJkzA008//Z3tcvHUz57apaysTLn4TpZl7Nq1C8XFxT2eFSUmJmLRokV4
      /fXXlXbJz8/H7Nmzw94O3gwuAl0Dgk1NTcjJyYHNZgtJbbfbDZ1OB4PBoMyN7+joQFtbmzIF
      zmq1XvJIpKt/7+KdezAYhMfjUW4T0XUpeKSDjLIsw+fzoampCYIgICcnJ+RzA4GAUkvXpese
      jwdtbW1wu93KrQT0er3yHlEUlVr1ej3S0tK6XYDTNVuora0NdrsdVqs1KgOmgUAA586dg8vl
      gt1uR0JCQkhdXq8XJpMJWq1W2XaHwwGHwwGLxYLU1NSQW2FIkgSfz4fW1lYEAgFkZGSEfGbX
      GVlnZyfa2tqUax7CnWlyMVEU0dbWpnSXdHV9dP3crtsk6PV65Yi+63um0+mQlpYGi8US8j3r
      OoJsbGyE2WxW2rtL18B+Q0MDkpKSkJGREZVt6Zqy2NjYiJSUlJBbQYTTLhf/fpqbm5GWloaM
      jAzlrGsgtUvX9nYdZFzq9xfNdvn/AOHpxwZfI3ubAAAAAElFTkSuQmCC
    </thumbnail>
    <thumbnail height='50' name='filters' width='146'>
      iVBORw0KGgoAAAANSUhEUgAAAJIAAAAyCAYAAAC3Z4JgAAAACXBIWXMAAA7DAAAOwwHHb6hk
      AAAHp0lEQVR4nO2bW0hUXRvHf7PHTkJlEZZNKkonI0zcJml0ToK8iigiQk1D0BSKAikqEA91
      YRiBQWFlpOUUhYQVHUmkYm5KK8tMEs1KJ2vMHGUmnfVdiPttPm2cejfMx8f6Xe1Z6/8888D8
      2fvZa60xCCEEEsm/RPF1AZL/D6SRJLogjSTRBWkkiS5II0l0QRpJogvSSBJdkEaS6II0kkQX
      pJEkuiCNJNEFaSSJLkgjSXRBGkmiC9JIEl2QRpLogjSSRBekkSS6II0k0QVpJIkuSCNJdEEa
      SaIL0kgSXZBGkuiCNJJEF6SRJLrg5+sCJEO4XC4qKyuxWCy8fPmSnp4eABRFITg4mISEBBIT
      EwkODv6jvGlpadr1/v37iYiI8Khva2vjwoULvHjxgo8fP+J0Ohk/fjwmk4klS5aQnJw8ag0G
      +d9/39Pb28uBAwd4+vSpNqYoCkIIfv15oqKiKC0t9Tqv1Wpl48aN2udTp04RGxv7W31xcTGX
      L1/G5XL9VqMoCtu3b2fPnj1u4/KO5GM6OjrIzs6mpaUFRVFISkoiPj6exYsXA9De3o7FYuHa
      tWsoyp91IoWFhURERPD+/XscDodH7fnz56moqAAgJCSElJQUoqKimDFjBl1dXdTX11NWVkZr
      ayvl5eVMnTqVnTt3/pNASHzKoUOHhKqqIj4+XtTU1HjU2mw2r/Pevn1bqKoqmpqaxMqVK4Wq
      qsJisYyq7e7uFjExMUJVVZGZmSkGBgZG1Q0MDIjMzEyhqqqIiYkR3d3d2pxstn1IW1sbd+7c
      AeDYsWOsXLnSoz4gIMCrvDabjaKiIgDmzZs3pr6+vh4hBIqiUFBQgNFoHFVnNBopKCjQHrv1
      9fXanDSSDykrK8PlcjF//nxWrFihW96ioiK6u7sJCwvzSv/u3Ttg6JE2llkDAgIICQlxiwNp
      JJ9ht9u5efMmANu3b9ctb21tLXfu3EFRFA4fPuxVzPBdy2q1emy0Yejt0mq1usWBNJLPaG9v
      Z3BwkPHjx7NhwwZdctrtdo4ePQrAli1biIyM9CouJiYGPz8/+vr6MJvNHrVms5m+vj78/PyI
      iYnRxqWRfMTHjx8BmDNnDuPGjdPGhRB8+vSJFy9eYLPZ/ijnyZMnsVqtBAUFsXv3bq/j/P39
      ycjIAP5ZAhgYGHDT/Pz5k0uXLlFcXAxARkYG/v7+2rx8/fcRnz9/Bob6kvfv31NdXU1dXR3N
      zc309fVpumnTprFq1SqysrI89i/Pnj3j+vXrABw8eNDtR/aG5ORkHA4HpaWlHD9+nIqKCpYs
      WcLMmTPp7Oykrq6Ozs5OFEUhPT2d5ORkt3hpJB8xfEd69OgRjx49cpsbN24cBoMBp9OJzWaj
      qqqKhw8fUlhYyLJly0bkcjgc5OfnI4QgMTGRuLi4v6opPT2d+Ph4UlNT6ejooKOjw23eaDRy
      /vx5Fi1aNCJWGslHfPnyRbueNGkSa9euZfny5cydO5fQ0FAMBgPt7e1cvXoVs9lMT08PR44c
      4cqVKyPuTKdPn6atrY3p06ezb9++v66psrKSM2fO4HK5mDx5MgsXLiQgIIDu7m4aGxv58eMH
      WVlZpKens23bNvdgr1e4JLpSUFAgVFUV2dnZwm63e9RaLBZtwTAvL89t7vXr12Lp0qVCVVVx
      7969UePHWpAUQohz584JVVVFXFycOHv27Iia7Ha7OHv2rIiLixOqqopz5865zctm20cEBQUB
      Q3tXY/UzsbGxbN26FYDnz59r44ODg+Tl5eFyuVi9ejXr16//q1pqa2spKSnBYDCQm5tLamrq
      iJr8/f1JTU0lNzcXg8FASUkJtbW12rx8tPkIk8kEwKdPn7zSr1mzBrPZzIcPH3A4HEyYMIGL
      Fy/S1NQEDL1V5efnjxo7vM9WXl7O3bt3gaG3xZSUFABqamoAWLduHQkJCR7rSEhI4MGDB9y/
      f5+amhptIVUayUfMnj0bGFpP6unpYcqUKV7pXS4XNpuNWbNm8erVK23+8ePHY37nkydPtOvI
      yEjNSBaLBYDo6Givao+Ojub+/ftaHEgj+Yzg4GCMRiNOp5OqqiqSkpI86oeb84kTJxIYGAhA
      UlKSx2Mhw5w4cQKn08mmTZu01ehfjdnV1QXw2z22/2bixIkAfP/+XRuTRvIRU6dOJTExkRs3
      bnDlyhV27Njh8ZjIcG8UHh6u6SIjI71avS4pKcHpdJKQkDDCeIqiMH/+fBoaGmhoaGDz5s1j
      5qurqwNw28uTzbYPSUtLw2g00tHRwYkTJ9wOsf3K169fKSsrA3DbltCL4UdadXW12+G60Xj6
      9CnV1dVucSCN5FNMJhOJiYkAXLp0iSNHjtDf3++maWxsJDMzk97eXgIDA9m1a5fudezatQuT
      yYTL5SInJ4fS0tIRdfT391NaWkpOTg4ulwuTyeRWizxq62McDgcFBQXcunULAD8/PxYsWEBI
      SAitra28fftW29wtKioiPj7+j79j1apV2O12j0dt37x5w969e7V+acKECYSGhhIYGIjVaqW1
      tVV7+5sxYwbFxcVu57+lkf6HGBwcpLm5mYaGBjo7OwkPD9dM9afHbP9tHe3t7bS0tPDt2zem
      T59OWFgYc+bM+W1DLo0k0YX/AEyf2Ggc6cFmAAAAAElFTkSuQmCC
    </thumbnail>
  </thumbnails>
</workbook>
