VisionEval Release Notes
========================

This directory includes .md files that describe changes that are made
through releases, pull requests and other feature updates.

Each pull request should ideally place a unique document in this folder
describing what the pull request means to accomplish. When the pull request
is incorporated, release notes for the version receiving it should be
updated here as well.

Things that can usefully go into such a document are descriptions of
build changes (e.g. unique build/config/VE-Components.yml or
build/config/VE-Config.yml), lists of files that are altered, and
most importantly, a description of intent (e.g. an issue to be resolved,
a contract task to be completed, etc.).

If the pull request does not supply an R script with tests for the
pull request, the pull request document here should contain a brief
description of how to test the material being submitted.

